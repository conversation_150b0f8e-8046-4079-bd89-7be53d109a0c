@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  
  /* MindMate AI Theme */
  --chat-primary: 124 58 237;  /* Purple */
  --chat-primary-foreground: 255 255 255;
  --chat-secondary: 99 102 241;  /* Indigo */
  --chat-secondary-foreground: 255 255 255;
  --chat-accent: 139 92 246;  /* Light purple */
  --chat-accent-foreground: 255 255 255;
  --chat-muted: 243 244 246;
  --chat-muted-foreground: 107 114 128;
  --chat-border: 229 231 235;
  --chat-input: 249 250 251;
  --chat-ring: 124 58 237;
  --chat-background: 255 255 255;
  --chat-foreground: 17 24 39;
  --chat-card: 255 255 255;
  --chat-card-foreground: 17 24 39;
  --chat-popover: 255 255 255;
  --chat-popover-foreground: 17 24 39;
  --chat-destructive: 239 68 68;
  --chat-destructive-foreground: 255 255 255;
  --chat-success: 16 185 129;
  --chat-success-foreground: 255 255 255;
  --chat-warning: 245 158 11;
  --chat-warning-foreground: 17 24 39;
  --chat-sidebar: 249 250 251;
  --chat-sidebar-foreground: 55 65 81;
  --chat-sidebar-hover: 243 244 246;
  --chat-message-ai: 243 244 246;
  --chat-message-user: rgb(var(--chat-primary) / 0.1);
}

.dark {
  --chat-background: 18 18 18;
  --chat-foreground: 229 229 229;
  --chat-card: 38 38 38;
  --chat-card-foreground: 229 229 229;
  --chat-popover: 38 38 38;
  --chat-popover-foreground: 229 229 229;
  --chat-primary: 167 139 250;  /* Lighter purple for dark mode */
  --chat-primary-foreground: 17 24 39;
  --chat-secondary: 129 140 248;  /* Lighter indigo for dark mode */
  --chat-secondary-foreground: 17 24 39;
  --chat-muted: 64 64 64;
  --chat-border: 64 64 64;
  --chat-input: 38 38 38;
  --chat-sidebar: 23 23 23;
  --chat-sidebar-foreground: 212 212 212;
  --chat-sidebar-hover: 38 38 38;
  --chat-message-ai: 38 38 38;
  --chat-message-user: rgb(124 58 237 / 0.2);
  --chat-ring: 167 139 250;
  --chat-muted-foreground: 156 163 175;
  --chat-accent: 55 65 81;
  --chat-accent-foreground: 243 244 246;
  --chat-destructive: 220 38 38;
  --chat-destructive-foreground: 243 244 246;
  --chat-border: 55 65 81;
  --chat-input: 55 65 81;
  --chat-ring: 59 130 246;
}

#root {
  margin: auto;
  font-family: 'Inter', system-ui, sans-serif;
}

/* Enhanced Chat Styles */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: rgb(var(--chat-background));
  color: rgb(var(--chat-foreground));
}

/* Message Animations */
@keyframes slideInFromBottom {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.animate-in {
  animation: slideInFromBottom 0.3s ease-out;
}

.slide-in-from-bottom-2 {
  animation: slideInFromBottom 0.3s ease-out;
}

/* Typing Indicator Animation */
@keyframes bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

.animate-bounce {
  animation: bounce 1.4s infinite;
}

/* Syntax Highlighting Styles */
.syntax-highlighter {
  position: relative;
  font-family: 'JetBrains Mono', ui-monospace, SFMono-Regular, monospace;
}

.syntax-highlighter .keyword {
  color: #c678dd;
  font-weight: 500;
}

.syntax-highlighter .string {
  color: #98c379;
}

.syntax-highlighter .number {
  color: #d19a66;
}

.syntax-highlighter .boolean {
  color: #56b6c2;
}

.syntax-highlighter .comment {
  color: #5c6370;
  font-style: italic;
}

.syntax-highlighter .tag {
  color: #e06c75;
}

.syntax-highlighter .attr-name {
  color: #d19a66;
}

.syntax-highlighter .attr-value {
  color: #98c379;
}

.syntax-highlighter.light-theme .keyword {
  color: #a626a4;
}

.syntax-highlighter.light-theme .string {
  color: #50a14f;
}

.syntax-highlighter.light-theme .number {
  color: #986801;
}

.syntax-highlighter.light-theme .boolean {
  color: #0184bc;
}

.syntax-highlighter.light-theme .comment {
  color: #a0a1a7;
}

.syntax-highlighter.light-theme .tag {
  color: #e45649;
}

.syntax-highlighter.light-theme .attr-name {
  color: #986801;
}

.syntax-highlighter.light-theme .attr-value {
  color: #50a14f;
}

/* Inline Code Styles */
.inline-code {
  background: rgb(var(--chat-muted));
  color: rgb(var(--chat-foreground));
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
  font-family: 'JetBrains Mono', ui-monospace, SFMono-Regular, monospace;
  font-size: 0.875em;
}

/* Prose Styles for Markdown */
.prose {
  max-width: none;
  color: rgb(var(--chat-foreground));
}

.prose h1,
.prose h2,
.prose h3,
.prose h4,
.prose h5,
.prose h6 {
  color: rgb(var(--chat-foreground));
  font-weight: 600;
  margin-top: 1.5em;
  margin-bottom: 0.5em;
}

.prose h1 {
  font-size: 1.5em;
}

.prose h2 {
  font-size: 1.25em;
}

.prose h3 {
  font-size: 1.125em;
}

.prose p {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
}

.prose a {
  color: rgb(var(--chat-primary));
  text-decoration: underline;
}

.prose a:hover {
  text-decoration: none;
}

.prose strong {
  font-weight: 600;
  color: rgb(var(--chat-foreground));
}

.prose em {
  font-style: italic;
}

.prose ul,
.prose ol {
  margin-top: 0.75em;
  margin-bottom: 0.75em;
  padding-left: 1.5em;
}

.prose li {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* Layout */
.chat-container {
  @apply flex h-screen bg-[rgb(var(--chat-background))];
  font-family: 'Inter', sans-serif;
}

/* Sidebar */
.chat-sidebar {
  @apply w-64 md:w-72 bg-[rgb(var(--chat-sidebar))] text-[rgb(var(--chat-sidebar-foreground))] 
         border-r border-[rgb(var(--chat-border))] flex flex-col h-full transition-all duration-200;
}

.chat-sidebar-header {
  @apply p-4 border-b border-[rgb(var(--chat-border))] flex items-center justify-between;
}

.chat-sidebar-search {
  @apply p-3 border-b border-[rgb(var(--chat-border))];
}

.chat-sidebar-search input {
  @apply w-full px-3 py-2 rounded-lg bg-[rgb(var(--chat-input))] text-sm 
         focus:outline-none focus:ring-2 focus:ring-[rgb(var(--chat-primary))];
}

.chat-conversations {
  @apply flex-1 overflow-y-auto;
}

.chat-conversation-item {
  @apply px-4 py-3 hover:bg-[rgb(var(--chat-sidebar-hover))] cursor-pointer 
         transition-colors duration-200 flex items-center gap-3;
}

.chat-conversation-item.active {
  @apply bg-[rgb(var(--chat-message-user))] border-r-2 border-[rgb(var(--chat-primary))];
}

/* Main Chat Area */
.chat-main {
  @apply flex-1 flex flex-col h-full overflow-hidden;
}

.chat-header {
  @apply p-4 border-b border-[rgb(var(--chat-border))] flex items-center justify-between;
  background-color: rgb(var(--chat-background));
  backdrop-filter: blur(10px);
  position: sticky;
  top: 0;
  z-index: 10;
}

.chat-messages {
  @apply flex-1 overflow-y-auto p-4 space-y-4;
  scroll-behavior: smooth;
}

/* Message Bubbles */
.message-bubble {
  @apply max-w-3xl mx-auto w-full px-4 py-3 rounded-2xl;
  transition: all 0.2s ease;
}

.message-ai {
  @apply bg-[rgb(var(--chat-message-ai))] text-[rgb(var(--chat-foreground))];
  border-top-left-radius: 0.5rem;
}

.message-user {
  @apply bg-[rgb(var(--chat-message-user))] text-[rgb(var(--chat-foreground))] ml-auto;
  border-top-right-radius: 0.5rem;
}

.message-header {
  @apply flex items-center gap-2 mb-1;
}

.message-avatar {
  @apply w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm;
  background: linear-gradient(135deg, rgb(var(--chat-primary)), rgb(var(--chat-accent)));
}

.message-time {
  @apply text-xs text-[rgb(var(--chat-muted-foreground))];
}

/* Input Area */
.chat-input-container {
  @apply p-4 border-t border-[rgb(var(--chat-border))];
  background-color: rgb(var(--chat-background));
  backdrop-filter: blur(10px);
  position: sticky;
  bottom: 0;
}

.chat-input-wrapper {
  @apply max-w-3xl mx-auto w-full;
}

.chat-input {
  @apply w-full px-4 py-3 rounded-full bg-[rgb(var(--chat-input))] 
         focus:outline-none focus:ring-2 focus:ring-[rgb(var(--chat-primary))] 
         placeholder-[rgb(var(--chat-muted-foreground))] text-[rgb(var(--chat-foreground))];
  border: 1px solid rgb(var(--chat-border));
  transition: all 0.2s ease;
}

.chat-input-actions {
  @apply flex items-center justify-between mt-2 px-2;
}

.chat-send-button {
  @apply p-2 rounded-full bg-[rgb(var(--chat-primary))] text-white 
         hover:bg-[rgb(var(--chat-primary)/0.9)] transition-colors;
}

/* Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

.message-enter {
  animation: fadeIn 0.3s ease-out forwards;
}

/* Custom Scrollbar */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  @apply bg-[rgb(var(--chat-border))] rounded-full;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  @apply bg-[rgb(var(--chat-muted-foreground))];
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgb(var(--chat-muted-foreground) / 0.3);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgb(var(--chat-muted-foreground) / 0.5);
}

/* Focus Styles */
.focus-visible\:ring-2:focus-visible {
  outline: 2px solid transparent;
  outline-offset: 2px;
  box-shadow: 0 0 0 2px rgb(var(--chat-ring));
}

/* Button Variants */
.btn-primary {
  background: rgb(var(--chat-primary));
  color: rgb(var(--chat-primary-foreground));
}

.btn-primary:hover {
  background: rgb(var(--chat-primary) / 0.9);
}

.btn-secondary {
  background: rgb(var(--chat-secondary));
  color: rgb(var(--chat-secondary-foreground));
}

.btn-secondary:hover {
  background: rgb(var(--chat-secondary) / 0.8);
}

.btn-ghost:hover {
  background: rgb(var(--chat-accent));
  color: rgb(var(--chat-accent-foreground));
}

.btn-outline {
  border: 1px solid rgb(var(--chat-border));
  background: transparent;
  color: rgb(var(--chat-foreground));
}

.btn-outline:hover {
  background: rgb(var(--chat-accent));
  color: rgb(var(--chat-accent-foreground));
}

/* Input Styles */
.input-field {
  background: rgb(var(--chat-background));
  border: 1px solid rgb(var(--chat-border));
  color: rgb(var(--chat-foreground));
}

.input-field:focus {
  border-color: rgb(var(--chat-ring));
  box-shadow: 0 0 0 2px rgb(var(--chat-ring) / 0.2);
}

.input-field::placeholder {
  color: rgb(var(--chat-muted-foreground));
}

/* Card Styles */
.card {
  background: rgb(var(--chat-card));
  color: rgb(var(--chat-card-foreground));
  border: 1px solid rgb(var(--chat-border));
}

/* Utility Classes */
.text-muted {
  color: rgb(var(--chat-muted-foreground));
}

.bg-muted {
  background: rgb(var(--chat-muted));
}

.border-muted {
  border-color: rgb(var(--chat-border));
}

/* Line Clamp Utility */
.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

/* Responsive Design */
@media (max-width: 768px) {
  .chat-container {
    font-size: 14px;
  }
  
  .prose {
    font-size: 14px;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  :root {
    --chat-border: 0 0 0;
    --chat-muted-foreground: 75 85 99;
  }
  
  .dark {
    --chat-border: 255 255 255;
    --chat-muted-foreground: 156 163 175;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  .animate-in,
  .slide-in-from-bottom-2,
  .animate-bounce {
    animation: none;
  }
  
  * {
    transition-duration: 0.01ms !important;
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
  }
}