import React, { useState, useEffect, useRef } from 'react';
import { ChatContext } from '../contexts/ChatProvider';
import { FontAwesomeIcon } from './icons/FontAwesome';
import {
  faPaperPlane,
  faRobot,
  faUser,
  faSpinner,
  faSearch,
  faPlus,
  faEllipsisV,
  faTrash,
  faEdit,
  faCopy,
  faThumbsUp,
  faThumbsDown,
  faPaperclip,
  faMicrophone,
  faImage,
  faCode,
  faLightbulb
} from './icons/FontAwesome';

// Avatar Component
const Avatar = ({ name, isAI = false, className = '' }) => {
  const initials = name ? name.charAt(0).toUpperCase() : '?';
  return (
    <div 
      className={`w-8 h-8 rounded-full flex items-center justify-center text-white font-medium text-sm ${className} ${
        isAI 
          ? 'bg-gradient-to-br from-purple-500 to-indigo-600' 
          : 'bg-gradient-to-br from-blue-500 to-cyan-500'
      }`}
    >
      {isAI ? <FontAwesomeIcon icon={faRobot} className="w-4 h-4" /> : initials}
    </div>
  );
};

// MessageBubble Component
const MessageBubble = ({ message, isUser }) => {
  const [showActions, setShowActions] = useState(false);
  const bubbleRef = useRef(null);

  // Format time
  const formatTime = (timestamp) => {
    if (!timestamp) return '';
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  // Handle click outside to close actions
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (bubbleRef.current && !bubbleRef.current.contains(event.target)) {
        setShowActions(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Render message content with basic markdown support
  const renderContent = () => {
    if (!message.content) return null;
    
    let content = message.content
      // Bold text
      .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
      // Italic
      .replace(/\*(.*?)\*/g, '<em>$1</em>')
      // Code blocks
      .replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-800 text-gray-100 p-3 rounded-lg my-2 overflow-x-auto"><code>$1</code></pre>')
      // Inline code
      .replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-700 px-1.5 py-0.5 rounded text-sm font-mono">$1</code>')
      // Links
      .replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" class="text-blue-500 hover:underline" target="_blank" rel="noopener noreferrer">$1</a>');

    return <div className="prose dark:prose-invert max-w-none" dangerouslySetInnerHTML={{ __html: content }} />;
  };

  return (
    <div 
      ref={bubbleRef}
      className={`group flex mb-4 ${isUser ? 'justify-end' : 'justify-start'}`}
      onMouseEnter={() => setShowActions(true)}
      onMouseLeave={() => setShowActions(false)}
    >
      {!isUser && <div className="flex-shrink-0 mr-3"><Avatar name="AI" isAI /></div>}
      
      <div className="flex flex-col max-w-3xl">
        <div 
          className={`relative rounded-2xl px-4 py-3 ${
            isUser 
              ? 'bg-blue-100 dark:bg-blue-900/30 rounded-tr-none' 
              : 'bg-gray-100 dark:bg-gray-800 rounded-tl-none'
          }`}
        >
          <div className="flex items-center mb-1">
            <span className="font-medium text-sm text-gray-700 dark:text-gray-200">
              {isUser ? 'You' : 'AI Assistant'}
            </span>
            <span className="mx-2 text-gray-400">·</span>
            <span className="text-xs text-gray-500">
              {formatTime(message.timestamp)}
            </span>
          </div>
          
          {message.isTyping ? (
            <div className="flex items-center space-x-2 py-1">
              <div className="flex space-x-1.5">
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
              </div>
            </div>
          ) : (
            renderContent()
          )}
        </div>

        {/* Message Actions */}
        <div 
          className={`flex items-center mt-1 space-x-2 px-1 transition-opacity duration-200 ${
            showActions ? 'opacity-100' : 'opacity-0'
          }`}
        >
          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-1">
            <FontAwesomeIcon icon={faThumbsUp} className="w-4 h-4" />
          </button>
          <button className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-1">
            <FontAwesomeIcon icon={faThumbsDown} className="w-4 h-4" />
          </button>
          <button 
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 p-1"
            onClick={() => {
              navigator.clipboard.writeText(message.content);
            }}
          >
            <FontAwesomeIcon icon={faCopy} className="w-4 h-4" />
          </button>
        </div>
      </div>
      
      {isUser && <div className="flex-shrink-0 ml-3"><Avatar name="You" /></div>}
    </div>
  );
};

// Chat Input Component
const ChatInput = ({ onSend, isLoading }) => {
  const [message, setMessage] = useState('');
  const inputRef = useRef(null);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (message.trim() && !isLoading) {
      onSend(message);
      setMessage('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className={`px-${spacing.md} pb-${spacing.md} pt-${spacing.sm}`}>
      <div className="relative max-w-3xl mx-auto">
        <div className={`flex items-center bg-${colors.surface} dark:bg-${colors.surfaceDark} rounded-full border border-${colors.border} dark:border-${colors.borderDark} focus-within:ring-2 focus-within:ring-${colors.primary} focus-within:border-transparent transition-all duration-200`}>
          <button 
            type="button"
            className={`p-${spacing.sm} text-${colors.textSecondary} hover:text-${colors.text} dark:hover:text-${colors.textDark}`}
          >
            <FontAwesomeIcon icon={faPaperclip} className="w-5 h-5" />
          </button>
          
          <input
            ref={inputRef}
            type="text"
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Type a message..."
            className={`flex-1 bg-transparent border-0 focus:ring-0 focus:outline-none text-${colors.text} dark:text-${colors.textDark} placeholder-${colors.textSecondary} ${typography.base}`}
            disabled={isLoading}
          />
          
          <button
            type="submit"
            disabled={!message.trim() || isLoading}
            className={`p-${spacing.sm} rounded-full mr-${spacing.xs} ${
              message.trim() && !isLoading
                ? `text-${colors.primary} hover:text-${colors.primaryDark} dark:text-${colors.primaryLight} dark:hover:text-${colors.primary}`
                : `text-${colors.textSecondary}`
            }`}
          >
            {isLoading ? (
              <FontAwesomeIcon icon={faSpinner} className="w-5 h-5 animate-spin" />
            ) : (
              <FontAwesomeIcon icon={faPaperPlane} className="w-5 h-5" />
            )}
          </button>
        </div>
        
        <div className={`flex justify-center mt-${spacing.xs}`}>
          <span className={`text-${colors.textSecondary} ${typography.tiny}`}>
            AI may produce inaccurate information about people, places, or facts.
          </span>
        </div>
      </div>
    </form>
  );
};

// Main Chat Component
const ModernChat = ({ messages = [], onSendMessage, isLoading = false }) => {
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-900">
      {/* Chat Header */}
      <div className="border-b border-gray-200 dark:border-gray-800 p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Avatar name="AI" isAI />
            <div>
              <h2 className="font-semibold text-gray-900 dark:text-white">AI Assistant</h2>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {isLoading ? 'Typing...' : 'Online'}
              </p>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
              <FontAwesomeIcon icon={faSearch} className="w-5 h-5" />
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800">
              <FontAwesomeIcon icon={faEllipsisV} className="w-5 h-5" />
            </button>
          </div>
        </div>
      </div>
      
      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {messages.map((message, index) => (
          <MessageBubble 
            key={message.id || index} 
            message={message} 
            isUser={message.sender === 'user'} 
          />
        ))}
        <div ref={messagesEndRef} />
      </div>
      
      {/* Chat Input */}
      <ChatInput 
        onSend={onSendMessage} 
        isLoading={isLoading} 
      />
    </div>
  );
};

export default ModernChat;
