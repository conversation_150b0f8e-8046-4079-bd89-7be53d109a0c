<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8" />
    <link rel="icon" href="/favicon.ico" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />

    <meta
      name="description"
      content="The Collective - PC-based AI Assistant"
    />
    <link rel="apple-touch-icon" href="/logo192.png" />
    <link rel="manifest" href="/manifest.json" />
    <title>The Collective</title>
    <style>
      .loading-fallback {
        padding: 20px;
        font-family: Arial, sans-serif;
        text-align: center;
        color: #333;
      }
    </style>
  </head>
  <body>
    <noscript>You need to enable JavaScript to run this app.</noscript>
    <div id="root">
      <div class="loading-fallback">
        <h2>Loading...</h2>
        <p>If this message persists, there may be a JavaScript loading issue.</p>
      </div>
    </div>
    <!-- Stable portal container that persists across HMR reloads -->
    <div id="portal-root"></div>
    <script>
      console.log('HTML script tag executed');
      // Development-only logging; do not mutate the React root from here
      window.addEventListener('error', function(e) {
        try {
          console.error('Global error:', e.error || e.message || e);
          // Avoid mutating #root. React owns the DOM under #root.
        } catch (err) {
          console.error('Error in HTML error handler:', err);
        }
      });

      // Test if modules are supported
      if ('noModule' in HTMLScriptElement.prototype) {
        console.log('ES modules are supported');
      } else {
        console.log('ES modules are NOT supported');
        // Avoid mutating #root here as well
      }
    </script>
    <script type="module" src="/src/index.jsx"></script>
  </body>
</html>