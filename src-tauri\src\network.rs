use serde::{Deserialize, Serialize};
use std::process::Command;
use pnet::datalink;
use pnet::ipnetwork::IpNetwork;

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NetworkInterfaceInfo {
    pub name: String,
    pub mac: Option<String>,
    pub ips: Vec<String>,
    pub ipv4: Option<String>,
    pub ipv6: Option<String>,
    pub gateway: Option<String>,
    pub netmask: Option<String>,
    pub dns_servers: Vec<String>,
    pub is_up: bool,
    pub is_loopback: bool,
    pub speed: Option<u64>,
    pub tx_bytes: u64,
    pub rx_bytes: u64,
    pub tx_packets: u64,
    pub rx_packets: u64,
    pub tx_errors: u64,
    pub rx_errors: u64,
    pub mtu: u32,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct NetworkDevice {
    pub ip: String,
    pub mac: Option<String>,
    pub hostname: Option<String>,
    pub vendor: Option<String>,
    pub is_self: bool,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct NetworkStats {
    pub upload_speed: f64,
    pub download_speed: f64,
    pub total_upload: u64,
    pub total_download: u64,
    pub packets_sent: u64,
    pub packets_received: u64,
}

#[tauri::command]
pub async fn get_network_interfaces() -> Result<Vec<NetworkInterfaceInfo>, String> {
    let mut interfaces = Vec::new();
    
    for iface in datalink::interfaces() {
        let mut interface_info = NetworkInterfaceInfo {
            name: iface.name.clone(),
            mac: iface.mac.map(|mac| mac.to_string()),
            ips: iface.ips.iter().map(|ip| ip.to_string()).collect(),
            ipv4: None,
            ipv6: None,
            gateway: None,
            netmask: None,
            dns_servers: Vec::new(),
            is_up: iface.is_up(),
            is_loopback: iface.is_loopback(),
            speed: get_interface_speed(&iface.name),
            tx_bytes: 0,
            rx_bytes: 0,
            tx_packets: 0,
            rx_packets: 0,
            tx_errors: 0,
            rx_errors: 0,
            mtu: 1500, // MTU access removed as it's not directly available in the current pnet version
        };

        for ip in &iface.ips {
            match ip {
                IpNetwork::V4(ipv4) => {
                    interface_info.ipv4 = Some(ipv4.ip().to_string());
                    interface_info.netmask = Some(ipv4.mask().to_string());
                }
                IpNetwork::V6(ipv6) => {
                    interface_info.ipv6 = Some(ipv6.ip().to_string());
                }
            }
        }

        interface_info.gateway = get_default_gateway(&iface.name);
        interface_info.dns_servers = get_dns_servers();
        
        interfaces.push(interface_info);
    }
    
    Ok(interfaces)
}

#[tauri::command]
pub async fn get_network_stats(interface_name: &str) -> Result<NetworkStats, String> {
    let mut stats = NetworkStats {
        upload_speed: 0.0,
        download_speed: 0.0,
        total_upload: 0,
        total_download: 0,
        packets_sent: 0,
        packets_received: 0,
    };

    // Get network interfaces using pnet
    for iface in datalink::interfaces() {
        if iface.name == interface_name {
            // Note: This is a placeholder implementation
            // For real network stats, consider using platform-specific APIs or other crates
            // like `systemstat` or `netstat`
            if !iface.ips.is_empty() {
                // Placeholder values - implement actual measurement
                stats.upload_speed = 0.0;
                stats.download_speed = 0.0;
            }
            break;
        }
    }
    
    Ok(stats)
}

#[tauri::command]
pub async fn scan_network_devices(interface_name: &str) -> Result<Vec<NetworkDevice>, String> {
    let mut devices = Vec::new();
    
    // Add the current device
    if let Ok(interfaces) = get_network_interfaces().await {
        for iface in interfaces {
            if iface.name == interface_name {
                if let Some(ip) = iface.ipv4 {
                    devices.push(NetworkDevice {
                        ip,
                        mac: iface.mac,
                        hostname: None,
                        vendor: None,
                        is_self: true,
                    });
                }
            }
        }
    }
    
    // TODO: Add network scanning logic here
    
    Ok(devices)
}

fn get_interface_speed(interface_name: &str) -> Option<u64> {
    #[cfg(target_os = "windows")]
    {
        let output = Command::new("powershell")
            .args(&[
                "-Command",
                &format!("Get-NetAdapter | Where-Object {{ $_.Name -eq '{}' }} | Select-Object -ExpandProperty Speed", interface_name)
            ])
            .output()
            .ok()?;
            
        if output.status.success() {
            if let Ok(speed_str) = String::from_utf8(output.stdout) {
                if let Ok(speed) = speed_str.trim().parse::<u64>() {
                    return Some(speed / 1_000_000); // Convert to Mbps
                }
            }
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        let output = Command::new("ethtool")
            .arg(interface_name)
            .output()
            .ok()?;
            
        if output.status.success() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                for line in output_str.lines() {
                    if line.contains("Speed:") {
                        if let Some(speed_str) = line.split_whitespace().nth(1) {
                            if let Ok(speed) = speed_str.trim_end_matches("Mb/s").parse::<u64>() {
                                return Some(speed);
                            }
                        }
                    }
                }
            }
        }
    }
    
    None
}

fn get_default_gateway(interface_name: &str) -> Option<String> {
    #[cfg(target_os = "windows")]
    {
        let output = Command::new("powershell")
            .args(&[
                "-Command",
                "Get-NetRoute -DestinationPrefix '0.0.0.0/0' | Select-Object -ExpandProperty NextHop"
            ])
            .output()
            .ok()?;
            
        if output.status.success() {
            if let Ok(gateway) = String::from_utf8(output.stdout) {
                return Some(gateway.trim().to_string());
            }
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        let output = Command::new("ip")
            .args(&["route", "show", "default"])
            .output()
            .ok()?;
            
        if output.status.success() {
            if let Ok(output_str) = String::from_utf8(output.stdout) {
                for line in output_str.lines() {
                    if line.contains("default") && line.contains(interface_name) {
                        if let Some(gateway) = line.split_whitespace().nth(2) {
                            return Some(gateway.to_string());
                        }
                    }
                }
            }
        }
    }
    
    None
}

fn get_dns_servers() -> Vec<String> {
    let mut dns_servers = Vec::new();
    
    #[cfg(target_os = "windows")]
    {
        if let Ok(output) = Command::new("powershell")
            .args(&[
                "-Command",
                "Get-DnsClientServerAddress -AddressFamily IPv4 | Select-Object -ExpandProperty ServerAddresses"
            ])
            .output()
        {
            if output.status.success() {
                if let Ok(output_str) = String::from_utf8(output.stdout) {
                    for line in output_str.lines() {
                        let line = line.trim();
                        if !line.is_empty() {
                            dns_servers.push(line.to_string());
                        }
                    }
                }
            }
        }
    }
    
    #[cfg(not(target_os = "windows"))]
    {
        use std::fs;
        use std::path::Path;
        
        let resolv_conf = if Path::new("/etc/resolv.conf").exists() {
            fs::read_to_string("/etc/resolv.conf").ok()
        } else {
            None
        };
        
        if let Some(contents) = resolv_conf {
            for line in contents.lines() {
                if line.starts_with("nameserver") {
                    if let Some(ip) = line.split_whitespace().nth(1) {
                        dns_servers.push(ip.to_string());
                    }
                }
            }
        }
    }
    
    dns_servers
}
