import React from 'react';
import ErrorPopup from './ErrorPopup';

/**
 * Error Boundary component to catch errors in child component trees
 * and display fallback UI instead of crashing the entire application
 */
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { 
      hasError: false, 
      error: null,
      errorInfo: null,
      componentStack: null
    };
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // You can also log the error to an error reporting service
    console.error('Error caught by ErrorBoundary:', error, errorInfo);
    
    // Add the component stack to state
    this.setState({
      errorInfo,
      componentStack: errorInfo?.componentStack
    });
    
    // Report to error tracking service if available
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
    
    // Dispatch a custom error event for the ErrorIndicator to pick up
    const errorEvent = new ErrorEvent('error', {
      message: `Component Error: ${error.message}`,
      filename: 'React Component',
      error: error,
      lineno: 1,
      colno: 1,
    });
    window.dispatchEvent(errorEvent);
  }

  componentDidUpdate(prevProps) {
    // Reset the error state if the component's key or children change
    if (this.state.hasError && 
        (prevProps.children !== this.props.children || prevProps.resetKey !== this.props.resetKey)) {
      this.setState({ 
        hasError: false, 
        error: null,
        errorInfo: null,
        componentStack: null
      });
    }
  }

  render() {
    if (this.state.hasError) {
      // If the fallback component is provided, render it
      if (this.props.fallback) {
        return this.props.fallback({
          error: this.state.error,
          componentStack: this.state.componentStack,
          resetError: () => this.setState({ hasError: false, error: null })
        });
      }
      
      // Otherwise, render the default error display
      return (
        <div className={this.props.wrapperClassName || ''}>
          <ErrorPopup
            title={this.props.title || "Component Error"}
            message={this.props.message || "An error occurred while rendering this component."}
            error={this.state.error}
            showDetails={this.props.showDetails || false}
            onReload={this.props.onReload}
            onRetry={() => this.setState({ hasError: false, error: null })}
            className={this.props.className || ''}
          />
        </div>
      );
    }

    // If there's no error, render the children
    return this.props.children;
  }
}

export default ErrorBoundary;