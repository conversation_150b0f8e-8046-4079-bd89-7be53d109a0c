import React from 'react';

// Simple syntax highlighter for the chat plugin
export const Prism = {
  highlight: (code, language) => {
    // Simple syntax highlighting with basic patterns
    let highlighted = code;
    
    if (language === 'javascript' || language === 'js') {
      highlighted = highlighted
        .replace(/\b(const|let|var|function|return|if|else|for|while|class|import|export|from|default)\b/g, '<span class="keyword">$1</span>')
        .replace(/\b(true|false|null|undefined)\b/g, '<span class="boolean">$1</span>')
        .replace(/\b\d+\b/g, '<span class="number">$&</span>')
        .replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="string">$&</span>')
        .replace(/\/\/.*$/gm, '<span class="comment">$&</span>');
    } else if (language === 'python') {
      highlighted = highlighted
        .replace(/\b(def|class|import|from|return|if|else|elif|for|while|try|except|with|as|pass|break|continue)\b/g, '<span class="keyword">$1</span>')
        .replace(/\b(True|False|None)\b/g, '<span class="boolean">$1</span>')
        .replace(/\b\d+\b/g, '<span class="number">$&</span>')
        .replace(/(["'])((?:\\.|(?!\1)[^\\])*?)\1/g, '<span class="string">$&</span>')
        .replace(/#.*$/gm, '<span class="comment">$&</span>');
    } else if (language === 'html') {
      highlighted = highlighted
        .replace(/(&lt;\/?)(\w+)([^&]*?)(&gt;)/g, '<span class="tag">$1$2</span><span class="attr">$3</span><span class="tag">$4</span>')
        .replace(/(\w+)(=)/g, '<span class="attr-name">$1</span>$2')
        .replace(/(=)(["'])(.*?)\2/g, '$1<span class="attr-value">$2$3$2</span>');
    }
    
    return highlighted;
  }
};

export const oneDark = {
  'code[class*="language-"]': {
    color: '#abb2bf',
    background: '#282c34',
  },
  'pre[class*="language-"]': {
    color: '#abb2bf',
    background: '#282c34',
  },
};

export const oneLight = {
  'code[class*="language-"]': {
    color: '#383a42',
    background: '#fafafa',
  },
  'pre[class*="language-"]': {
    color: '#383a42',
    background: '#fafafa',
  },
};

const SyntaxHighlighter = ({ 
  children, 
  language, 
  style = oneDark, 
  PreTag = 'pre',
  className = '',
  ...props 
}) => {
  const code = typeof children === 'string' ? children : String(children);
  const highlighted = Prism.highlight(code, language);
  
  const isDark = style === oneDark;
  
  return React.createElement(PreTag, {
    className: `syntax-highlighter ${className} ${isDark ? 'dark-theme' : 'light-theme'}`,
    style: {
      backgroundColor: isDark ? '#282c34' : '#fafafa',
      color: isDark ? '#abb2bf' : '#383a42',
      padding: '1rem',
      borderRadius: '0.5rem',
      overflow: 'auto',
      fontSize: '0.875rem',
      lineHeight: '1.5',
      fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
    },
    ...props
  }, (
    <code 
      dangerouslySetInnerHTML={{ __html: highlighted }}
      style={{
        backgroundColor: 'transparent',
        padding: 0,
        borderRadius: 0,
      }}
    />
  ));
};

export default SyntaxHighlighter;