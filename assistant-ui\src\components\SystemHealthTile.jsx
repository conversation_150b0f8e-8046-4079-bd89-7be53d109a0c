import React, { useState, useEffect, useCallback } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from './ui/card';
import { Progress } from './ui/progress';
import StatusIndicator from './ui/status-indicator';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faMicrochip, faMemory, faExclamationTriangle, faCheckCircle } from '@fortawesome/free-solid-svg-icons';
import { invoke } from '@tauri-apps/api/core';

const SystemHealthTile = () => {
  const [systemMetrics, setSystemMetrics] = useState({
    cpuUsage: 0,
    memoryUsage: 0,
    totalMemory: 0,
    usedMemory: 0
  });
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  const loadSystemMetrics = useCallback(async () => {
    try {
      setIsLoading(true);
      setError(null);
      
      // Get system metrics from backend
      const metrics = await invoke('get_system_metrics');
      
      setSystemMetrics({
        cpuUsage: Math.round(metrics.cpu_usage),
        memoryUsage: Math.round((metrics.used_memory / metrics.total_memory) * 100),
        totalMemory: metrics.total_gb,
        usedMemory: metrics.used_gb
      });
    } catch (err) {
      console.error('Failed to load system metrics:', err);
      setError('Failed to load system metrics');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    loadSystemMetrics();
    
    // Refresh every 30 seconds
    const interval = setInterval(loadSystemMetrics, 30000);
    
    return () => clearInterval(interval);
  }, [loadSystemMetrics]);

  const getHealthStatus = useCallback(() => {
    const { cpuUsage, memoryUsage } = systemMetrics;
    const avgUsage = (cpuUsage + memoryUsage) / 2;
    
    if (avgUsage > 90) return 'error';
    if (avgUsage > 75) return 'warning';
    return 'online';
  }, [systemMetrics]);

  const getHealthLabel = useCallback(() => {
    const status = getHealthStatus();
    switch (status) {
      case 'error': return 'Critical';
      case 'warning': return 'Warning';
      case 'online': return 'Healthy';
      default: return 'Unknown';
    }
  }, [getHealthStatus]);

  if (isLoading) {
    return (
      <Card className="animate-fade-in">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">System Health</CardTitle>
          <FontAwesomeIcon icon={faMicrochip} className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 mb-4">
            <div className="h-4 bg-muted rounded w-24 animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-16 animate-pulse"></div>
          </div>
          <div className="space-y-3">
            <div className="h-2 bg-muted rounded animate-pulse"></div>
            <div className="h-2 bg-muted rounded animate-pulse"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className="border-destructive">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium text-destructive">System Health</CardTitle>
          <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4 text-destructive" />
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-destructive">
            <FontAwesomeIcon icon={faExclamationTriangle} className="h-4 w-4" />
            <span className="text-sm">Error loading metrics</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { cpuUsage, memoryUsage, totalMemory, usedMemory } = systemMetrics;
  const healthStatus = getHealthStatus();
  const healthLabel = getHealthLabel();

  return (
    <Card className="animate-fade-in">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">System Health</CardTitle>
        <div className="flex items-center gap-2">
          <StatusIndicator status={healthStatus} size="sm" />
          <FontAwesomeIcon icon={faMicrochip} className="h-4 w-4 text-muted-foreground" />
        </div>
      </CardHeader>
      <CardContent>
        <div className="flex items-center gap-2 mb-4">
          <FontAwesomeIcon 
            icon={healthStatus === 'error' ? faExclamationTriangle : faCheckCircle} 
            className={`h-4 w-4 ${
              healthStatus === 'error' ? 'text-destructive' : 
              healthStatus === 'warning' ? 'text-yellow-500' : 'text-green-500'
            }`} 
          />
          <span className="text-sm font-medium">{healthLabel}</span>
        </div>
        
        <div className="space-y-4">
          {/* CPU Usage */}
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-muted-foreground">CPU</span>
              <span className="font-medium">{cpuUsage}%</span>
            </div>
            <Progress 
              value={cpuUsage} 
              className="h-2" 
              indicatorClassName={
                cpuUsage > 90 ? 'bg-destructive' : 
                cpuUsage > 75 ? 'bg-yellow-500' : 'bg-green-500'
              } 
            />
          </div>
          
          {/* Memory Usage */}
          <div>
            <div className="flex justify-between text-xs mb-1">
              <span className="text-muted-foreground">
                RAM ({usedMemory.toFixed(1)}GB / {totalMemory.toFixed(1)}GB)
              </span>
              <span className="font-medium">{memoryUsage}%</span>
            </div>
            <Progress 
              value={memoryUsage} 
              className="h-2" 
              indicatorClassName={
                memoryUsage > 90 ? 'bg-destructive' : 
                memoryUsage > 75 ? 'bg-yellow-500' : 'bg-green-500'
              } 
            />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default SystemHealthTile;