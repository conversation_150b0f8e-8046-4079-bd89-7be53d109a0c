import React, { useState, useEffect, useCallback, memo, useRef } from 'react';
import { Button } from "./ui/button";
import ErrorPopup from './ErrorPopup';
import { useIsMounted } from '../hooks/useIsMounted';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faPlus, faTimes, faGlobe, faComments, faFolder, faNoteSticky, faCloud, faWrench, faCode, faSearch } from '@fortawesome/free-solid-svg-icons';
import QuickMenu from './QuickMenu';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';
import PluginLoader from './PluginLoader';

import { SafePortal } from '../contexts/PortalContext.jsx';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('TabSystem Error:', error, errorInfo);
  }

  componentDidUpdate(prevProps) {
    if (prevProps.children !== this.props.children && this.state.hasError) {
      this.setState({ hasError: false, error: null });
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorPopup
          title="Tab System Error"
          message="An error occurred in the tab system. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
        />
      );
    }
    return this.props.children;
  }
}

/**
 * TabSystem component for managing application tabs
 * @param {TabSystemProps} props - Component props
 */
const TabSystem = React.memo(({ tabs = [], addTab = () => {}, closeTab = () => {} }) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [showAppMenu, setShowAppMenu] = useState(false);
  const [availableApps, setAvailableApps] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const isMounted = useIsMounted();
  const abortControllerRef = useRef(null);
  const menuRef = useRef(null);

  // Initialize the abort controller in an effect
  useEffect(() => {
    abortControllerRef.current = new AbortController();
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  // Handle clicks outside menu with cleanup
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        setShowAppMenu(false);
      }
    };

    // Use capture phase to catch events before they bubble up
    document.addEventListener('mousedown', handleClickOutside, true);

    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
    };
  }, []);

  // Load available apps from plugin system with error handling and cleanup
  useEffect(() => {
    let isActive = true;

    const loadAvailableApps = async () => {
      if (!isActive || !isMounted()) return;

      // Don't create a new abort controller here, use the one from the effect
      if (!abortControllerRef.current) return;

      const { signal } = abortControllerRef.current;

      try {
        setLoading(true);

        // Get all plugins from the backend with abort signal
        const plugins = await invoke('get_plugins', {}, { signal }).catch(err => {
          if (err.name === 'AbortError') return null;
          console.warn('Failed to load plugins, using fallback:', err);
          return null;
        });

        if (!isActive || !isMounted()) return;

        // Process plugins or use fallback
        const apps = (Array.isArray(plugins) ? plugins : [])
          .filter(plugin => plugin?.enabled && (plugin.plugin_type === 'ui_component' || plugin.app_type))
          .map(plugin => {
            // Map plugin names to appropriate icons
            let icon;
            switch ((plugin.name || '').toLowerCase()) {
              case 'chat':
                icon = faComments;
                break;
              case 'file explorer':
                icon = faFolder;
                break;
              case 'notes':
                icon = faNoteSticky;
                break;
              case 'cloud':
                icon = faCloud;
                break;
              case 'tools':
                icon = faWrench;
                break;
              case 'developer':
                icon = faCode;
                break;
              case 'browser':
                icon = faGlobe;
                break;
              default:
                icon = faGlobe;
            }

            return {
              id: `plugin-${plugin.id || plugin.name}`,
              name: plugin.name || 'Unknown App',
              icon,
              path: `/app/${plugin.id || (plugin.name || '').toLowerCase().replace(/\s+/g, '-')}`,
              description: plugin.description || '',
              app_type: plugin.app_type || 'plugin',
              plugin_type: plugin.plugin_type,
              component: plugin.name || 'Unknown App'  // This should match the key in PluginComponents
            };
          });

        if (!isActive || !isMounted()) return;

        setAvailableApps(apps);
        setError(null);
      } catch (error) {
        if (error.name !== 'AbortError' && isActive && isMounted()) {
          console.error('Error loading apps:', error);
          setError(error);
        }
      } finally {
        if (isActive && isMounted()) {
          setLoading(false);
        }
      }
    };

    loadAvailableApps();

    return () => {
      isActive = false;
      // Don't abort the controller here, let the main effect cleanup handle it
    };
  }, [isMounted]);

  // Cleanup function for abort controller and other resources
  useEffect(() => {
    let isMounted = true;

    return () => {
      isMounted = false;

      // Abort any pending requests
      const controller = abortControllerRef.current;
      if (controller) {
        controller.abort();
      }

      // Clean up any other resources if needed
      if (menuRef.current) {
        // No need to manually remove the element from DOM as React manages it
        // Just clean up any custom event listeners here if needed
      }
      menuRef.current = null;
    };
  }, []);

  // Show error if any
  if (error) {
    return (
      <div className="p-4 bg-destructive/10 text-destructive rounded-md">
        <p>Failed to load tab system: {error.message}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-destructive/20 rounded hover:bg-destructive/30 text-destructive"
        >
          Reload
        </button>
      </div>
    );
  }

  // Get available apps from state
  const getAvailableApps = useCallback(() => {
    return availableApps;
  }, [availableApps]);

  const handleCloseTab = useCallback((e, tabId) => {
    if (!e || !tabId) {
      console.warn('Invalid parameters for handleCloseTab:', { e, tabId });
      return;
    }
    e.stopPropagation();
    e.preventDefault();

    // Get the current active tab before closing
    const currentTab = tabs.find(tab => tab.path === location.pathname);
    const isClosingActiveTab = currentTab && currentTab.id === tabId;

    // Close the tab
    if (typeof closeTab === 'function') {
      closeTab(tabId);
    }

    // If we're closing the active tab, navigate to the first available tab or home
    if (isClosingActiveTab) {
      const remainingTabs = tabs.filter(tab => tab.id !== tabId);
      const newActiveTab = remainingTabs[0];

      if (newActiveTab) {
        navigate(newActiveTab.path, { replace: true });
      } else {
        navigate('/', { replace: true });
      }
    }
  }, [closeTab, tabs, location.pathname, navigate]);

  // Memoize the tab rendering to prevent unnecessary re-renders
  const renderTabs = useCallback(() => {
    if (!Array.isArray(tabs)) return null;

    // Create a stable reference for the current path to avoid recreation on each render
    const currentPath = location.pathname;

    return (
      <div className="flex items-center">
        {tabs
          .filter(tab => tab && tab.id && tab.path && tab.name) // Defensive filtering
          .map((tab) => {
            const isActive = currentPath === tab.path;
            const showCloseButton = !['dashboard', 'chat', 'tools', 'settings'].includes(tab.id);

            return (
              <div
                key={`tab-${tab.id}`}
                className={`relative group ${isActive ? 'z-10' : ''}`}
                data-tab-id={tab.id}
              >
                <Link
                  to={tab.path}
                  className={`flex items-center px-3 py-1.5 cursor-pointer transition-all duration-200 relative ${
                    isActive
                      ? 'text-primary'
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                  onClick={(e) => {
                    console.log('🔗 Tab clicked:', { tabId: tab.id, tabPath: tab.path, currentPath });
                    if (isMounted() && currentPath !== tab.path) {
                      console.log('🚀 Navigating to:', tab.path);
                      // Let React Router handle the navigation naturally
                    } else {
                      console.log('⚠️ Navigation blocked:', { isMounted: isMounted(), samePath: currentPath === tab.path });
                    }
                  }}
                >
                  {isActive && (
                    <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary"></div>
                  )}
                  <FontAwesomeIcon icon={tab.icon} className="mr-1.5 h-3 w-3 flex-shrink-0" />
                  <span className="text-xs font-medium truncate max-w-[120px]">{tab.name}</span>

                  {showCloseButton && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="ml-2 h-5 w-5 p-0 opacity-0 group-hover:opacity-100 transition-opacity hover:bg-muted"
                      onClick={(e) => {
                        e.stopPropagation();
                        e.nativeEvent.stopImmediatePropagation();
                        handleCloseTab(e, tab.id);
                      }}
                      aria-label={`Close ${tab.name}`}
                    >
                      <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
                    </Button>
                  )}
                </Link>
              </div>
            );
          })}
      </div>
    );
  }, [tabs, location.pathname, isMounted, navigate, handleCloseTab]);

  // Render the main tab system UI
  return (
    <div className="flex items-center bg-card text-card-foreground border-b border-border px-4 z-50">
      <div className="flex items-center flex-1 overflow-x-auto">
        {renderTabs()}
      </div>

      {/* Add tab button */}
      <div className="relative">
        <Button
          variant="ghost"
          size="sm"
          className="px-3 py-2 text-muted-foreground hover:text-foreground hover:bg-muted transition-colors"
          onClick={() => {
            if (isMounted()) {
              setShowAppMenu(!showAppMenu);
            }
          }}
          aria-label="Open quick menu"
        >
          <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
        </Button>
        <QuickMenu 
          isOpen={showAppMenu}
          onClose={() => setShowAppMenu(false)}
          onAppSelect={(app) => {
            console.log(`Opening app: ${app.name || app.label}`);
            addTab({
              id: app.id,
              name: app.name || app.label,
              path: app.path || `/app/${app.id}`,
              icon: app.icon || faGlobe,
              component: () => <PluginLoader pluginName={app.name || app.label} />,
            });
            setShowAppMenu(false);
          }}
          apps={getAvailableApps().map(app => ({
            ...app,
            icon: app.icon || faGlobe,
            path: app.path || `/app/${app.id}`,
            label: app.name || app.label
          }))}
        />
      </div>
    </div>
  );
});

TabSystem.displayName = 'TabSystem';

export default TabSystem;