import { test, expect } from '@playwright/test';

test('has title', async ({ page }) => {
  await page.goto('http://localhost:3000');

  // Expect a title "to contain" a substring.
  await expect(page).toHaveTitle(/The Collective/);
});

test('dashboard loads correctly', async ({ page }) => {
  await page.goto('http://localhost:3000');

  // Check that the dashboard title is present
  await expect(page.getByText('The Collective')).toBeVisible();
  
  // Check that core navigation tabs are present
  await expect(page.getByRole('link', { name: 'Dashboard' })).toBeVisible();
  await expect(page.getByRole('link', { name: 'Chat' })).toBeVisible();
  await expect(page.getByRole('link', { name: 'Tools' })).toBeVisible();
  await expect(page.getByRole('link', { name: 'Setting<PERSON>' })).toBeVisible();
});

test('navigation between tabs works', async ({ page }) => {
  await page.goto('http://localhost:3000');

  // Click on the Chat tab
  await page.getByRole('link', { name: 'Chat' }).click();
  
  // Verify we're on the chat page
  await expect(page).toHaveURL(/.*chat/);
  
  // Click on the Tools tab
  await page.getByRole('link', { name: 'Tools' }).click();
  
  // Verify we're on the tools page
  await expect(page).toHaveURL(/.*tools/);
  
  // Click on the Settings tab
  await page.getByRole('link', { name: 'Settings' }).click();
  
  // Verify we're on the settings page
  await expect(page).toHaveURL(/.*settings/);
});

test('settings page functionality', async ({ page }) => {
  await page.goto('http://localhost:3000/settings');

  // Check that settings sections are present
  await expect(page.getByText('System')).toBeVisible();
  await expect(page.getByText('Appearance')).toBeVisible();
  await expect(page.getByText('Notifications')).toBeVisible();
});