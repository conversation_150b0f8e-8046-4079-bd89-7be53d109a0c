import React, { forwardRef, memo } from 'react';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from './card';
import StatusIndicator from './status-indicator';
import { cn } from '../../lib/utils';

/**
 * LiveTile component for dashboard grid system with flexible sizing and interactive states
 * 
 * @param {Object} props
 * @param {string} props.title - Tile title
 * @param {string} props.value - Primary value/metric
 * @param {string} props.subtitle - Supporting text
 * @param {string} props.size - Tile size: 'small', 'medium', 'large', 'wide'
 * @param {React.ComponentType} props.icon - Icon component
 * @param {string} props.status - Status for StatusIndicator: 'online', 'offline', 'error', 'warning', 'loading', 'idle'
 * @param {Object} props.trend - Trend information: {direction: 'up'|'down'|'neutral', value: string, color: 'green'|'red'|'gray'}
 * @param {Function} props.onClick - Click handler for navigation
 * @param {boolean} props.loading - Loading state
 * @param {string} props.className - Additional CSS classes
 */
const LiveTile = memo(({
  title,
  value,
  subtitle,
  size = 'medium',
  icon: Icon,
  status,
  trend,
  onClick,
  loading = false,
  className,
  children,
  ...props
}) => {
  // Size configurations for grid layout
  const sizeConfig = {
    small: {
      gridClass: 'col-span-1 row-span-1',
      height: 'h-32',
      padding: 'p-4',
      titleSize: 'text-sm font-medium',
      valueSize: 'text-lg font-bold',
      subtitleSize: 'text-xs',
      iconSize: 'w-4 h-4'
    },
    medium: {
      gridClass: 'col-span-1 row-span-1',
      height: 'h-40',
      padding: 'p-6',
      titleSize: 'text-base font-medium',
      valueSize: 'text-2xl font-bold',
      subtitleSize: 'text-sm',
      iconSize: 'w-5 h-5'
    },
    large: {
      gridClass: 'col-span-2 row-span-1',
      height: 'h-40',
      padding: 'p-6',
      titleSize: 'text-lg font-medium',
      valueSize: 'text-3xl font-bold',
      subtitleSize: 'text-sm',
      iconSize: 'w-6 h-6'
    },
    wide: {
      gridClass: 'col-span-2 row-span-1',
      height: 'h-32',
      padding: 'p-6',
      titleSize: 'text-base font-medium',
      valueSize: 'text-xl font-bold',
      subtitleSize: 'text-sm',
      iconSize: 'w-5 h-5'
    }
  };

  const config = sizeConfig[size] || sizeConfig.medium;
  const isClickable = typeof onClick === 'function';

  // Trend icon component
  const TrendIcon = ({ trend }) => {
    if (!trend) return null;

    const iconProps = {
      className: cn(
        'w-3 h-3',
        trend.color === 'green' && 'text-green-500',
        trend.color === 'red' && 'text-red-500',
        trend.color === 'gray' && 'text-gray-500'
      )
    };

    switch (trend.direction) {
      case 'up':
        return <TrendingUp {...iconProps} />;
      case 'down':
        return <TrendingDown {...iconProps} />;
      case 'neutral':
      default:
        return <Minus {...iconProps} />;
    }
  };

  // Loading skeleton component
  const LoadingSkeleton = () => (
    <div className="animate-pulse">
      <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
      <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
      <div className="h-3 bg-muted rounded w-2/3"></div>
    </div>
  );

  return (
    <Card
      frosted={true}
      className={cn(
        // Grid positioning
        config.gridClass,
        config.height,
        
        // Base styles
        'relative overflow-hidden',
        'transition-all duration-300 ease-out',
        'ring-1 ring-white/10',
        'shadow-lg shadow-primary/5',
        
        // Interactive styles
        isClickable && [
          'cursor-pointer',
          'hover:scale-[1.02] hover:shadow-xl hover:shadow-primary/10',
          'active:scale-[0.98]',
          'focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
        ],
        
        // Loading state
        loading && 'pointer-events-none',
        
        className
      )}
      onClick={isClickable ? onClick : undefined}
      tabIndex={isClickable ? 0 : undefined}
      role={isClickable ? 'button' : 'article'}
      aria-label={isClickable ? `Navigate to ${title}` : `${title} information`}
      {...props}
    >
      <CardHeader className={cn('flex flex-row items-center justify-between space-y-0 pb-2', config.padding, 'pb-2')}>
        <CardTitle className={cn(config.titleSize, 'text-muted-foreground')}>
          {title}
        </CardTitle>
        
        <div className="flex items-center gap-2">
          {/* Status Indicator */}
          {status && (
            <StatusIndicator 
              status={status} 
              size="sm" 
              animated={status === 'online' || status === 'loading'} 
            />
          )}
          
          {/* Icon */}
          {Icon && (
            <Icon className={cn(config.iconSize, 'text-muted-foreground')} />
          )}
        </div>
      </CardHeader>

      <CardContent className={cn(config.padding, 'pt-0')}>
        {loading ? (
          <LoadingSkeleton />
        ) : (
          <>
            {/* Primary Value */}
            {value && (
              <div className={cn(config.valueSize, 'text-foreground mb-1')}>
                {value}
              </div>
            )}

            {/* Subtitle and Trend */}
            <div className="flex items-center justify-between">
              {subtitle && (
                <p className={cn(config.subtitleSize, 'text-muted-foreground')}>
                  {subtitle}
                </p>
              )}
              
              {/* Trend Indicator */}
              {trend && (
                <div className="flex items-center gap-1">
                  <TrendIcon trend={trend} />
                  <span className={cn(
                    'text-xs font-medium',
                    trend.color === 'green' && 'text-green-500',
                    trend.color === 'red' && 'text-red-500',
                    trend.color === 'gray' && 'text-gray-500'
                  )}>
                    {trend.value}
                  </span>
                </div>
              )}
            </div>

            {/* Custom Children Content */}
            {children && (
              <div className="mt-3">
                {children}
              </div>
            )}
          </>
        )}
      </CardContent>

      {/* Loading Overlay */}
      {loading && (
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </Card>
  );
});

LiveTile.displayName = 'LiveTile';



export default LiveTile;
