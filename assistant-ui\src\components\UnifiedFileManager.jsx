import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faFolder, 
  faFile, 
  faSearch, 
  faHome, 
  faArrowLeft, 
  faArrowRight, 
  faArrowUp,
  faList,
  faThLarge,
  faStar,
  faClock,
  faRefresh,
  faEye,
  faEdit,
  faTrash,
  faPlus,
  faDownload,
  faUpload,
  faCopy,
  faPaste,
  faCut
} from '@fortawesome/free-solid-svg-icons';
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Button } from './ui/button';
import { Input } from './ui/input';
import FileViewer from './FileViewer';

// Unified File Manager Component
const UnifiedFileManager = ({ 
  initialPath = 'C:\\', 
  onPathChange,
  allowFileOperations = true,
  allowDirectoryOperations = true,
  allowedFileTypes = null, // null means all files, otherwise array of extensions
  showHiddenFiles = false,
  viewMode = 'list', // 'list' or 'grid'
  enableBookmarks = true,
  enableSearch = true,
  enableNavigation = true,
  enableToolbar = true,
  className = ''
}) => {
  const [currentPath, setCurrentPath] = useState(initialPath);
  const [directories, setDirectories] = useState([]);
  const [files, setFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [viewModeState, setViewModeState] = useState(viewMode);
  const [selectedItems, setSelectedItems] = useState([]);
  const [navigationHistory, setNavigationHistory] = useState([initialPath]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [bookmarks, setBookmarks] = useState([]);
  const [recentLocations, setRecentLocations] = useState([]);
  const [showHidden, setShowHidden] = useState(showHiddenFiles);
  const [viewingFile, setViewingFile] = useState(null);
  const [editingFile, setEditingFile] = useState(null);

  useEffect(() => {
    // Load bookmarks and recent locations from storage
    loadBookmarks();
    loadRecentLocations();
    
    // Load initial directory
    browseDirectory(currentPath, false);
  }, []);

  const loadBookmarks = async () => {
    try {
      // In a real implementation, this would load from plugin storage
      const savedBookmarks = [];
      setBookmarks(savedBookmarks);
    } catch (error) {
      console.error('Error loading bookmarks:', error);
    }
  };

  const loadRecentLocations = async () => {
    try {
      // In a real implementation, this would load from plugin storage
      const savedRecent = [];
      setRecentLocations(savedRecent);
    } catch (error) {
      console.error('Error loading recent locations:', error);
    }
  };

  const browseDirectory = async (path, addToHistory = true) => {
    try {
      setLoading(true);
      setError(null);
      
      const result = await invoke('browse_directory', { path });
      
      setDirectories(result.directories || []);
      setFiles(result.files || []);
      
      if (addToHistory && path !== currentPath) {
        // Add to navigation history
        const newHistory = navigationHistory.slice(0, historyIndex + 1);
        newHistory.push(path);
        setNavigationHistory(newHistory);
        setHistoryIndex(newHistory.length - 1);
        
        // Add to recent locations
        addToRecentLocations(path);
      }
      
      setCurrentPath(path);
      setSelectedItems([]);
      
      // Notify parent of path change
      if (onPathChange) {
        onPathChange(path);
      }
    } catch (error) {
      console.error('Error browsing directory:', error);
      setError(`Failed to browse directory: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const addToRecentLocations = async (path) => {
    try {
      const newRecent = [path, ...recentLocations.filter(p => p !== path)].slice(0, 20);
      setRecentLocations(newRecent);
    } catch (error) {
      console.error('Error saving recent locations:', error);
    }
  };

  const navigateUp = () => {
    if (currentPath) {
      const parentPath = currentPath.split(/[\\/]/).slice(0, -1).join('\\');
      if (parentPath && parentPath !== currentPath) {
        browseDirectory(parentPath + '\\');
      }
    }
  };

  const navigateBack = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      browseDirectory(navigationHistory[newIndex], false);
    }
  };

  const navigateForward = () => {
    if (historyIndex < navigationHistory.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      browseDirectory(navigationHistory[newIndex], false);
    }
  };

  const toggleBookmark = async (path) => {
    try {
      const isBookmarked = bookmarks.some(b => b.path === path);
      let newBookmarks;
      
      if (isBookmarked) {
        newBookmarks = bookmarks.filter(b => b.path !== path);
      } else {
        const bookmark = {
          path,
          name: path.split(/[\\/]/).pop() || path,
          added_at: new Date().toISOString()
        };
        newBookmarks = [...bookmarks, bookmark];
      }
      
      setBookmarks(newBookmarks);
    } catch (error) {
      console.error('Error toggling bookmark:', error);
    }
  };

  const handleItemClick = (item, isDirectory) => {
    if (isDirectory) {
      browseDirectory(item.path);
    } else {
      // View file content
      setViewingFile(item);
    }
  };

  const handleItemSelect = (item) => {
    setSelectedItems(prev => {
      if (prev.includes(item)) {
        return prev.filter(i => i !== item);
      } else {
        return [...prev, item];
      }
    });
  };

  const formatFileSize = (size) => {
    if (size === undefined || size === null) return 'Unknown';
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  const filteredDirectories = directories.filter(dir => 
    dir.name.toLowerCase().includes(searchTerm.toLowerCase()) &&
    (showHidden || !dir.name.startsWith('.'))
  );

  const filteredFiles = files.filter(file => {
    const matchesSearch = file.name.toLowerCase().includes(searchTerm.toLowerCase());
    const isVisible = showHidden || !file.name.startsWith('.');
    
    // Filter by file type if specified
    if (allowedFileTypes && Array.isArray(allowedFileTypes)) {
      const fileExt = file.name.toLowerCase().split('.').pop();
      const isAllowed = allowedFileTypes.some(ext => 
        ext.toLowerCase() === fileExt || ext.toLowerCase() === `.${fileExt}`
      );
      return matchesSearch && isVisible && isAllowed;
    }
    
    return matchesSearch && isVisible;
  });

  const renderListView = () => (
    <div className="space-y-1">
      {filteredDirectories.map((dir) => (
        <div
          key={`dir-${dir.path || dir.name}`}
          className={`flex items-center gap-3 p-2 rounded cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 ${
            selectedItems.includes(dir) ? 'bg-blue-100 dark:bg-blue-900/30' : ''
          }`}
          onClick={() => handleItemClick(dir, true)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(dir);
          }}
        >
          <FontAwesomeIcon icon={faFolder} className="h-5 w-5 text-blue-500" />
          <span className="flex-1 text-sm">{dir.name}</span>
          <span className="text-xs text-slate-500">Folder</span>
        </div>
      ))}
      
      {filteredFiles.map((file) => (
        <div
          key={`file-${file.path || file.name}`}
          className={`flex items-center gap-3 p-2 rounded cursor-pointer hover:bg-slate-100 dark:hover:bg-slate-700 ${
            selectedItems.includes(file) ? 'bg-blue-100 dark:bg-blue-900/30' : ''
          }`}
          onClick={() => handleItemClick(file, false)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(file);
          }}
        >
          <FontAwesomeIcon icon={faFile} className="h-5 w-5 text-slate-500" />
          <span className="flex-1 text-sm">{file.name}</span>
          <span className="text-xs text-slate-500">
            {file.size ? formatFileSize(file.size) : 'File'}
          </span>
        </div>
      ))}
    </div>
  );

  const renderGridView = () => (
    <div className="grid grid-cols-4 md:grid-cols-6 lg:grid-cols-8 gap-4">
      {filteredDirectories.map((dir) => (
        <Card
          key={`dir-${dir.path || dir.name}`}
          className={`cursor-pointer hover:shadow-md transition-shadow ${
            selectedItems.includes(dir) ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => handleItemClick(dir, true)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(dir);
          }}
        >
          <CardContent className="p-3 text-center">
            <FontAwesomeIcon icon={faFolder} className="h-8 w-8 text-blue-500 mb-2" />
            <p className="text-xs truncate">{dir.name}</p>
          </CardContent>
        </Card>
      ))}
      
      {filteredFiles.map((file) => (
        <Card
          key={`file-${file.path || file.name}`}
          className={`cursor-pointer hover:shadow-md transition-shadow ${
            selectedItems.includes(file) ? 'ring-2 ring-blue-500' : ''
          }`}
          onClick={() => handleItemClick(file, false)}
          onContextMenu={(e) => {
            e.preventDefault();
            handleItemSelect(file);
          }}
        >
          <CardContent className="p-3 text-center">
            <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-slate-500 mb-2" />
            <p className="text-xs truncate">{file.name}</p>
            {file.size && (
              <p className="text-xs text-slate-500 mt-1">
                {formatFileSize(file.size)}
              </p>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  );

  // Close file viewer
  const closeFileViewer = () => {
    setViewingFile(null);
  };

  // Close file editor
  const closeFileEditor = () => {
    setEditingFile(null);
  };

  // Render file viewer when a file is selected for viewing
  if (viewingFile) {
    return (
      <div className={`unified-file-manager h-full flex flex-col bg-slate-50 dark:bg-slate-900 ${className}`}>
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={faFile} className="h-6 w-6 text-slate-500" />
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">
                {viewingFile.name}
              </h1>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={() => setEditingFile(viewingFile)}>
                <FontAwesomeIcon icon={faEdit} className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="outline" size="sm" onClick={closeFileViewer}>
                Close Viewer
              </Button>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-auto">
          <FileViewer filePath={viewingFile.path} />
        </div>
      </div>
    );
  }

  // Render file editor when a file is selected for editing
  if (editingFile) {
    return (
      <div className={`unified-file-manager h-full flex flex-col bg-slate-50 dark:bg-slate-900 ${className}`}>
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <FontAwesomeIcon icon={faEdit} className="h-6 w-6 text-slate-500" />
              <h1 className="text-xl font-bold text-slate-800 dark:text-slate-100">
                Editing: {editingFile.name}
              </h1>
            </div>
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={closeFileEditor}>
                Cancel
              </Button>
              <Button variant="default" size="sm">
                <FontAwesomeIcon icon={faSave} className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          </div>
        </div>
        <div className="flex-1 overflow-auto p-4">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-4 h-full">
            <textarea
              className="w-full h-full min-h-[400px] p-2 border rounded font-mono text-sm"
              placeholder="File content will be loaded here..."
              defaultValue={`Editing ${editingFile.name}...\n\nTODO: Implement file editing functionality`}
            />
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`unified-file-manager h-full flex flex-col bg-slate-50 dark:bg-slate-900 ${className}`}>
      {/* Toolbar */}
      {enableToolbar && (
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-3">
          <div className="flex flex-wrap items-center gap-2">
            {allowFileOperations && (
              <>
                <Button variant="outline" size="sm">
                  <FontAwesomeIcon icon={faPlus} className="h-4 w-4 mr-2" />
                  New
                </Button>
                <Button variant="outline" size="sm">
                  <FontAwesomeIcon icon={faUpload} className="h-4 w-4 mr-2" />
                  Upload
                </Button>
                <Button variant="outline" size="sm" disabled={selectedItems.length === 0}>
                  <FontAwesomeIcon icon={faDownload} className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </>
            )}
            
            {allowFileOperations && allowDirectoryOperations && (
              <div className="h-5 w-px bg-slate-300 dark:bg-slate-600 mx-1"></div>
            )}
            
            {allowFileOperations && (
              <>
                <Button variant="outline" size="sm" disabled={selectedItems.length === 0}>
                  <FontAwesomeIcon icon={faCopy} className="h-4 w-4 mr-2" />
                  Copy
                </Button>
                <Button variant="outline" size="sm" disabled={selectedItems.length === 0}>
                  <FontAwesomeIcon icon={faCut} className="h-4 w-4 mr-2" />
                  Cut
                </Button>
                <Button variant="outline" size="sm">
                  <FontAwesomeIcon icon={faPaste} className="h-4 w-4 mr-2" />
                  Paste
                </Button>
              </>
            )}
            
            <div className="flex-1"></div>
            
            {enableSearch && (
              <div className="relative">
                <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  type="text"
                  placeholder="Search files..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 w-48"
                />
              </div>
            )}
          </div>
        </div>
      )}

      {/* Navigation */}
      {enableNavigation && (
        <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-3">
          <div className="flex flex-wrap items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={navigateBack}
              disabled={historyIndex <= 0}
            >
              <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateForward}
              disabled={historyIndex >= navigationHistory.length - 1}
            >
              <FontAwesomeIcon icon={faArrowRight} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={navigateUp}
            >
              <FontAwesomeIcon icon={faArrowUp} className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => browseDirectory(currentPath)}
            >
              <FontAwesomeIcon icon={faRefresh} className="h-4 w-4" />
            </Button>
            
            <div className="flex-1 min-w-0 mx-2">
              <Input
                value={currentPath}
                onChange={(e) => setCurrentPath(e.target.value)}
                onKeyDown={(e) => {
                  if (e.key === 'Enter') {
                    browseDirectory(e.target.value);
                  }
                }}
                className="font-mono text-sm"
                placeholder="Enter path..."
              />
            </div>
            
            <Button
              variant={viewModeState === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewModeState('list')}
            >
              <FontAwesomeIcon icon={faList} className="h-4 w-4" />
            </Button>
            <Button
              variant={viewModeState === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewModeState('grid')}
            >
              <FontAwesomeIcon icon={faThLarge} className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}

      {/* Main Content */}
      <div className="flex-1 overflow-hidden flex">
        {/* Sidebar */}
        {(enableBookmarks || recentLocations.length > 0) && (
          <div className="w-56 bg-white dark:bg-slate-800 border-r border-slate-200 dark:border-slate-700 p-3 overflow-y-auto">
            <div className="space-y-4">
              {/* Quick Access */}
              <div>
                <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Quick Access</h3>
                <div className="space-y-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-xs"
                    onClick={() => browseDirectory('C:\\')}
                  >
                    <FontAwesomeIcon icon={faHome} className="h-3 w-3 mr-2" />
                    Home
                  </Button>
                  
                  {bookmarks.map((bookmark) => (
                    <Button
                      key={`bookmark-${bookmark.path}`}
                      variant="ghost"
                      size="sm"
                      className="w-full justify-start text-xs"
                      onClick={() => browseDirectory(bookmark.path)}
                    >
                      <FontAwesomeIcon icon={faStar} className="h-3 w-3 mr-2 text-yellow-500" />
                      {bookmark.name}
                    </Button>
                  ))}
                </div>
              </div>

              {/* Recent Locations */}
              {recentLocations.length > 0 && (
                <div>
                  <h3 className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-2">Recent</h3>
                  <div className="space-y-1 max-h-32 overflow-y-auto">
                    {recentLocations.slice(0, 5).map((location) => (
                      <Button
                        key={`recent-${location}`}
                        variant="ghost"
                        size="sm"
                        className="w-full justify-start text-xs"
                        onClick={() => browseDirectory(location)}
                      >
                        <FontAwesomeIcon icon={faClock} className="h-3 w-3 mr-2" />
                        {location.split(/[\\/]/).pop() || location}
                      </Button>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* File Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Header */}
          <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-3">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <FontAwesomeIcon icon={faFolder} className="h-5 w-5 text-blue-500" />
                <h2 className="text-lg font-semibold text-slate-800 dark:text-slate-100 truncate">
                  {currentPath}
                </h2>
              </div>
              <div className="text-sm text-slate-500">
                {filteredDirectories.length + filteredFiles.length} items
              </div>
            </div>
          </div>

          {/* File List */}
          <div className="flex-1 overflow-y-auto p-3">
            {loading && (
              <div className="text-center py-12">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-slate-600 dark:text-slate-400">Loading directory...</p>
              </div>
            )}
            
            {error && (
              <div className="text-center py-8">
                <p className="text-red-500 bg-red-100 dark:bg-red-900/30 p-4 rounded-lg max-w-2xl mx-auto">
                  {error}
                </p>
              </div>
            )}

            {!loading && !error && (
              <div>
                {filteredDirectories.length === 0 && filteredFiles.length === 0 ? (
                  <div className="text-center py-12">
                    <FontAwesomeIcon icon={faFolder} className="h-16 w-16 text-slate-400 mb-4" />
                    <p className="text-slate-600 dark:text-slate-400 text-lg">
                      {searchTerm ? 'No files found' : 'This folder is empty'}
                    </p>
                  </div>
                ) : (
                  viewModeState === 'list' ? renderListView() : renderGridView()
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnifiedFileManager;