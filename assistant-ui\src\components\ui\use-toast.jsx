import { useNotifications } from "../../contexts/NotificationContext";

export const useToast = () => {
  const { addNotification } = useNotifications();

  const toast = {
    success: ({ title, description }) => addNotification({ title, message: description, type: "success" }),
    error: ({ title, description }) => addNotification({ title, message: description, type: "error" }),
    warning: ({ title, description }) => addNotification({ title, message: description, type: "warning" }),
    info: ({ title, description }) => addNotification({ title, message: description, type: "info" }),
  };

  return { toast };
};