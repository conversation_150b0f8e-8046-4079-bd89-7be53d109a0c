import React, { useState, useEffect } from 'react';
import { safeInvoke } from '../utils/tauriHelpers';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faBell, faCog } from '@fortawesome/free-solid-svg-icons';
import NotificationPopout from './NotificationPopout';
import { useNotifications } from '../contexts/NotificationContext';
import { useNavigate } from 'react-router-dom';

const StatusBar = () => {
  const [isPopoutOpen, setIsPopoutOpen] = useState(false);
  const { notifications } = useNotifications();
  const unreadCount = notifications.filter(n => !n.read && !n.cleared).length;
  const navigate = useNavigate();

  // State for system metrics
  const [systemMetrics, setSystemMetrics] = useState(null);
  const [metricsLoading, setMetricsLoading] = useState(true);
  const [metricsError, setMetricsError] = useState(false);

  const togglePopout = () => {
    setIsPopoutOpen(!isPopoutOpen);
  };

  const goToSettings = () => {
    navigate('/settings');
  };

  // Fetch system metrics
  const fetchSystemMetrics = async () => {
    try {
      setMetricsLoading(true);
      setMetricsError(false);
      const metrics = await safeInvoke('get_system_metrics');
      setSystemMetrics(metrics);
    } catch (error) {
      console.error('Error fetching system metrics:', error);
      setMetricsError(true);
      setSystemMetrics(null);
    } finally {
      setMetricsLoading(false);
    }
  };

  useEffect(() => {
    fetchSystemMetrics();
    const interval = setInterval(fetchSystemMetrics, 5000); // Update every 5 seconds
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="fixed bottom-0 left-0 right-0 h-5 bg-card border-t border-border text-card-foreground text-xs flex items-center justify-between px-3 z-30">
      {/* Left Section - Status */}
      <div className="flex items-center space-x-3">
        <div className="flex items-center space-x-1">
          <div className={`w-2 h-2 rounded-full ${
            metricsLoading ? 'bg-yellow-400 animate-pulse' :
            metricsError ? 'bg-red-400' :
            systemMetrics ? 'bg-green-400' : 'bg-red-400'
          }`}></div>
          <span className="font-medium text-card-foreground">
            {metricsLoading ? 'Loading...' :
             metricsError ? 'Error' :
             systemMetrics ? 'Online' : 'Offline'}
          </span>
        </div>
      </div>

      {/* Right Section - System Info & Controls */}
      <div className="relative flex items-center space-x-3"> {/* Added relative positioning here */}
        {/* System Metrics */}
        {systemMetrics && !metricsError && (
          <>
            <span className="hover:bg-muted px-2 py-1 rounded cursor-pointer transition-colors text-card-foreground">
              CPU: {systemMetrics.cpu_usage?.toFixed(1) ?? 'N/A'}%
            </span>
            <span className="hover:bg-muted px-2 py-1 rounded cursor-pointer transition-colors text-card-foreground">
              RAM: {systemMetrics.memory_usage?.used_gb?.toFixed(1) ?? 'N/A'}GB
            </span>
          </>
        )}
        {metricsError && (
          <>
            <span className="text-card-foreground">CPU: N/A</span>
            <span className="text-card-foreground">RAM: N/A</span>
          </>
        )}
        
        {/* Settings and Notifications */}
        <div className="relative">
          <button onClick={togglePopout} className="text-white hover:text-gray-300 relative">
            <FontAwesomeIcon icon={faBell} className="h-5 w-5" />
            {unreadCount > 0 && (
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                {unreadCount}
              </span>
            )}
          </button>
          <NotificationPopout isOpen={isPopoutOpen} onClose={() => setIsPopoutOpen(false)} />
        </div>

        <button onClick={goToSettings} className="text-white hover:text-gray-300 ml-4">
          <FontAwesomeIcon icon={faCog} className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
};

export default StatusBar;
