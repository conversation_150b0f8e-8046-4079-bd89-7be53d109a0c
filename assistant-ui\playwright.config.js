// @ts-check
import { defineConfig, devices } from '@playwright/test';

/**
 * Read environment variables from file.
 * https://github.com/motdotla/dotenv
 */
// require('dotenv').config();

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  globalSetup: require.resolve('./global-setup'),
  globalTeardown: require.resolve('./global-teardown'),
  testDir: './tests',
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  timeout: 60000,
  expect: { timeout: 15000 },
  use: {
    // Use a consistent baseURL for browser projects
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
  },

  projects: [
    // Run only the dedicated Tauri spec with the tauri-driver
    {
      name: 'tauri',
      testMatch: /.*tauri\.spec\.js$/,
      use: {
        connectOptions: {
          wsEndpoint: 'ws://127.0.0.1:4446',
        },
      },
    },

    // Browser projects exclude the tauri-only spec
    {
      name: 'chromium',
      testIgnore: /.*tauri\.spec\.js$/,
      use: { ...devices['Desktop Chrome'] },
    },
  ],

  // webServer is managed externally by root scripts
  // webServer: {
  //   command: 'npm run dev',
  //   url: 'http://localhost:3000',
  //   cwd: '..',
  //   reuseExistingServer: !process.env.CI,
  //   timeout: 120 * 1000,
  // },
});