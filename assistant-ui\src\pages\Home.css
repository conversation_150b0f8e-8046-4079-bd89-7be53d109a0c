/* Home.css */
.home-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--primary-bg);
  color: var(--primary-text);
}

/* Top Bar */
.top-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  height: 50px; /* Fixed height for top bar */
}

.logo-title {
  font-size: 1.2rem;
  font-weight: bold;
  display: flex;
  align-items: center;
}

.app-logo-icon {
  margin-right: 8px;
  font-size: 1.5rem;
}

.settings-icon-button {
  background: none;
  border: none;
  color: var(--primary-text);
  font-size: 1.5rem;
  cursor: pointer;
  padding: 5px;
}

.settings-icon-button:hover {
  color: var(--accent-blue);
}

/* Main Content Area */
.main-content-area {
  display: flex;
  flex: 1; /* Takes remaining height */
  overflow: hidden; /* Prevents overflow from main area */
}

/* Side Panels */
.chat-history-panel {
  width: 280px;
  background-color: var(--secondary-bg);
  padding: 15px;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  border-left: 1px solid var(--border-color);
  transition: width 0.3s ease;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.panel-header h3 {
  margin: 0;
  font-size: 1.1rem;
}

.toggle-panel-button, .toggle-section-button {
  background: none;
  border: none;
  color: var(--secondary-text);
  cursor: pointer;
  font-size: 1rem;
}

.toggle-panel-button:hover, .toggle-section-button:hover {
  color: var(--primary-text);
}

.panel-content p {
  font-size: 0.9rem;
  color: var(--secondary-text);
  margin-bottom: 10px;
}

.chat-history-item {
  padding: 8px 10px;
  margin-bottom: 8px;
  background-color: var(--tertiary-bg);
  border-radius: 4px;
  font-size: 0.9rem;
  cursor: pointer;
}

.chat-history-item:hover {
  background-color: var(--accent-blue);
  color: white;
}

.chat-history-item span {
  display: block;
  font-size: 0.75rem;
  color: var(--secondary-text);
  margin-top: 4px;
}

.chat-history-item:hover span {
  color: rgba(255,255,255,0.8);
}

.open-panel-button {
    position: absolute;
    top: 60px; /* Adjust based on top-bar height */
    background-color: var(--accent-blue);
    color: white;
    border: none;
    padding: 10px;
    cursor: pointer;
    z-index: 10;
    border-radius: 0 5px 5px 0;
    writing-mode: vertical-rl;
    text-orientation: mixed;
}

.open-history-button {
    right: 0;
    border-radius: 5px 0 0 5px;
}

/* Center Chat Area */
.center-chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  overflow-y: auto;
  transition: margin-left 0.3s ease, margin-right 0.3s ease;
}

.center-chat-area.expanded-left {
    margin-left: 40px; /* Width of the collapsed panel button */
}
.center-chat-area.expanded-right {
    margin-right: 40px; /* Width of the collapsed panel button */
}

.messages-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.welcome-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  flex: 1;
  color: var(--secondary-text);
}

.assistant-icon-svg {
  font-size: 3rem; /* Adjust size as needed */
  margin-bottom: 15px;
  color: var(--accent-blue);
}

.welcome-prompt h2 {
  font-size: 1.5rem;
  color: var(--primary-text);
  margin-bottom: 10px;
}

.welcome-prompt p {
  font-size: 1rem;
  max-width: 400px;
}

.message {
  max-width: 70%;
  padding: 10px 15px;
  border-radius: 12px;
  word-wrap: break-word;
  line-height: 1.4;
}

.message.user {
  align-self: flex-end;
  background-color: var(--accent-blue);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant {
  align-self: flex-start;
  background-color: var(--secondary-bg);
  color: var(--primary-text);
  border-bottom-left-radius: 4px;
}

.message.system {
  align-self: center;
  background-color: var(--tertiary-bg);
  color: var(--secondary-text);
  font-style: italic;
  font-size: 0.85rem;
  border-radius: 8px;
  max-width: 85%;
  text-align: center;
}

.message.error {
  background-color: #5c2323; /* Darker red for dark theme */
  color: #ffcdd2;
}

.message-timestamp {
  font-size: 0.7rem;
  opacity: 0.6;
  margin-top: 5px;
  text-align: right;
}

.loading-indicator {
  display: flex;
  align-items: center;
  padding: 10px;
}

.dot {
  width: 7px;
  height: 7px;
  background-color: var(--secondary-text);
  border-radius: 50%;
  margin: 0 3px;
  animation: bounce 1.4s infinite ease-in-out both;
}

.dot:nth-child(1) { animation-delay: -0.32s; }
.dot:nth-child(2) { animation-delay: -0.16s; }
.dot:nth-child(3) { animation-delay: 0s; }

@keyframes bounce {
  0%, 80%, 100% { transform: scale(0); }
  40% { transform: scale(1.0); }
}

/* Bottom Input Bar */
.bottom-input-bar {
  display: flex;
  align-items: center;
  padding: 10px 20px;
  background-color: var(--secondary-bg);
  border-top: 1px solid var(--border-color);
  height: 60px; /* Fixed height for input bar */
}

.status-info {
  display: flex;
  gap: 15px;
  font-size: 0.8rem;
  color: var(--secondary-text);
  margin-right: auto; /* Pushes input form to the right */
}

.input-form-container {
  display: flex;
  align-items: center;
  flex: 1; /* Takes available space if status-info is small */
  max-width: 700px; /* Max width for the input area */
  margin-left: auto; /* Aligns to right if space allows */
}

.input-form-container input {
  flex: 1;
  padding: 10px 15px;
  background-color: var(--tertiary-bg);
  color: var(--primary-text);
  border: 1px solid var(--border-color);
  border-radius: 20px;
  font-size: 1rem;
  outline: none;
}

.input-form-container input::placeholder {
  color: var(--secondary-text);
}

.input-form-container input:focus {
  border-color: var(--accent-blue);
}

.icon-button, .send-button {
  background: none;
  border: none;
  color: var(--secondary-text);
  font-size: 1.3rem;
  cursor: pointer;
  padding: 8px;
  margin-left: 8px;
}

.icon-button:hover, .send-button:hover {
  color: var(--accent-blue);
}

.send-button {
  background-color: var(--accent-blue);
  color: white;
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.send-button:hover {
  background-color: #2563eb; /* Slightly darker blue */
  color: white;
}

.send-button:disabled, .icon-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}