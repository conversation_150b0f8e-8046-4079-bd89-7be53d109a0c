import React, { useState, useEffect, useRef } from 'react';
import { useLocation } from 'react-router-dom';
import ErrorPopup from './ErrorPopup';
import ErrorBoundary from './ErrorBoundary';
import ErrorIndicator from './ErrorIndicator';
import { useIsMounted } from '../hooks/useIsMounted.js';
import TabSystem from './TabSystem';
import StatusBar from '../components/StatusBar';
import PluginLoader from './PluginLoader.jsx';

// Remove the simple ErrorBoundary component as we have a dedicated file now

export const MainLayout = ({ children, tabs = [], addTab = () => {}, closeTab = () => {} }) => {
  const location = useLocation();
  const isChatPage = location.pathname === '/';

  return (
    <ErrorBoundary
      title="Application Error"
      message="An error occurred in the application. Please try reloading the page."
      onReload={() => window.location.reload()}
    >
      <div className="flex flex-col h-screen bg-background text-foreground">
        <div className="flex-1 flex flex-col overflow-hidden">
          <ErrorBoundary
            title="Tab System Error"
            message="An error occurred in the tab navigation. Please try reloading the page."
          >
            <TabSystem tabs={tabs} addTab={addTab} closeTab={closeTab} />
          </ErrorBoundary>
          <main className="flex-1 overflow-auto">
            <ErrorBoundary
              title="Content Error"
              message="An error occurred in this page. Other parts of the application should still work."
              wrapperClassName="h-full"
            >
              {children}
            </ErrorBoundary>
          </main>
        </div>
        <StatusBar />
      </div>
    </ErrorBoundary>
  );
};