// This is a basic integration test for The Collective app
// It tests the core functionality of the app using <PERSON><PERSON>'s testing framework

use tauri::Manager;

#[cfg(test)]
mod integration_tests {
    use super::*;
    use tauri::test::{mock_builder, mock_context};

    #[test]
    fn test_app_initialization() {
        // Create a mock context and app for testing
        let context = mock_context();
        let app = mock_builder(tauri::test::mock_assets())
            .invoke_handler(tauri::generate_handler![
                crate::greet
            ])
            .build(context)
            .expect("Failed to build app");

        // Verify the app initializes correctly
        assert!(app.handle().is_managed());
    }

    #[test]
    fn test_window_creation() {
        let context = mock_context();
        let app = mock_builder()
            .invoke_handler(tauri::generate_handler![])
            .build(context)
            .expect("Failed to build app");

        // Check that we can get the main window
        let window = app.get_webview_window("main");
        assert!(window.is_some(), "Main window should be created");
    }

    #[test]
    fn test_app_configuration() {
        let context = mock_context();
        let app = mock_builder()
            .invoke_handler(tauri::generate_handler![])
            .build(context)
            .expect("Failed to build app");

        // Test that the app has the correct configuration
        let config = app.config();
        assert_eq!(config.product_name, Some("assistant-ui".to_string()));
    }

    #[test]
    fn test_greet_command() {
        let context = mock_context();
        let app = mock_builder()
            .invoke_handler(tauri::generate_handler![
                crate::greet
            ])
            .build(context)
            .expect("Failed to build app");

        // Test the greet command
        let result = tauri::test::invoke(&app, "greet", serde_json::json!({ "name": "World" }));
        assert!(result.is_ok());
        assert_eq!(result.unwrap(), "Hello, World! You've been greeted from Rust!");
    }
}