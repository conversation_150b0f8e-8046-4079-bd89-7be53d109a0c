import React, { useState, useEffect, useRef, useMemo, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faPowerOff, 
  faExpand, 
  faLaptop, 
  faCog,
  faGlobe,
  faComments,
  faHistory,
  faChevronLeft,
  faNoteSticky,
  faTimes,
  faFolderOpen,
  faSearch
} from '@fortawesome/free-solid-svg-icons';
import CastMenu from './CastMenu';
import PropTypes from 'prop-types';

const QuickMenu = React.memo(({ isOpen, onClose, onAppSelect, apps = [] }) => {
  const menuRef = useRef(null);
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(0);
  const [recentActivity] = useState([
    { id: 'act1', type: 'opened', app: 'Chat', time: '2 min ago', icon: faComments },
    { id: 'act2', type: 'created', app: 'Document', time: '10 min ago', icon: faNoteSticky },
    { id: 'act3', type: 'updated', app: 'Settings', time: '25 min ago', icon: faCog },
  ]);

  // Process apps prop to ensure it has all required fields
  const defaultApps = useMemo(() => {
    if (!Array.isArray(apps)) return [];
    
    return apps.map(app => ({
      id: app.id || `app-${Math.random().toString(36).substr(2, 9)}`,
      name: app.name || app.label || 'Unnamed App',
      icon: app.icon || faGlobe,
      path: app.path || `/app/${app.id || (app.name || '').toLowerCase().replace(/\s+/g, '-')}`,
      description: app.description || '',
      ...app
    }));
  }, [apps]);

  // Cast menu state
  const [isCastMenuOpen, setIsCastMenuOpen] = useState(false);
  const isMounted = useRef(true);
  
  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Quick actions with our design colors and proper state management
  const [activeAction, setActiveAction] = useState(null);
  const actionTimeoutRef = useRef(null);
  
  // Reset active action when modal closes or action changes
  useEffect(() => {
    if (!isOpen) {
      setActiveAction(null);
      if (actionTimeoutRef.current) {
        clearTimeout(actionTimeoutRef.current);
        actionTimeoutRef.current = null;
      }
    }
    
    return () => {
      if (actionTimeoutRef.current) {
        clearTimeout(actionTimeoutRef.current);
      }
    };
  }, [isOpen]);
  
  // Handle action with visual feedback
  const handleAction = useCallback((actionId, actionFn) => {
    setActiveAction(prev => prev === actionId ? null : actionId);
    
    // Clear any existing timeout
    if (actionTimeoutRef.current) {
      clearTimeout(actionTimeoutRef.current);
      actionTimeoutRef.current = null;
    }
    
    // Execute the action
    if (actionFn) {
      actionFn();
    }
    
    // Auto-reset active state after animation completes
    actionTimeoutRef.current = setTimeout(() => {
      setActiveAction(null);
      actionTimeoutRef.current = null;
    }, 1000);
    
    // Cleanup function for the effect
    return () => {
      if (actionTimeoutRef.current) {
        clearTimeout(actionTimeoutRef.current);
        actionTimeoutRef.current = null;
      }
    };
  }, []);
  
  const quickActions = useMemo(() => [
    { 
      id: 'server', 
      icon: faPowerOff, 
      label: 'Server', 
      action: () => {
        handleAction('server', () => {
          console.log('Server action');
          // Add any server-specific logic here
        });
      },
      className: (isActive) => `transition-all duration-300 flex flex-col items-center justify-center p-2 rounded-xl ${
        isActive 
          ? 'bg-cyan-600/30 ring-2 ring-cyan-500/50' 
          : 'bg-cyan-600/10 hover:bg-cyan-600/20'
      } text-cyan-600 dark:text-cyan-400`
    },
    { 
      id: 'cast', 
      icon: faExpand, 
      label: 'Cast', 
      action: (e) => {
        handleAction('cast', () => {
          setIsCastMenuOpen(true);
          // Prevent event from bubbling up to parent elements
          e?.stopPropagation();
        });
      },
      buttonProps: { 'data-cast-button': true },
      className: (isActive) => `transition-all duration-300 flex flex-col items-center justify-center p-2 rounded-xl ${
        isActive 
          ? 'bg-indigo-600/30 ring-2 ring-indigo-500/50' 
          : 'bg-indigo-600/10 hover:bg-indigo-600/20'
      } text-indigo-600 dark:text-indigo-400`
    },
    { 
      id: 'devices', 
      icon: faLaptop, 
      label: 'Devices', 
      action: () => {
        handleAction('devices', () => {
          console.log('Devices action');
          // Add any devices-specific logic here
        });
      },
      className: (isActive) => `transition-all duration-300 flex flex-col items-center justify-center p-2 rounded-xl ${
        isActive 
          ? 'bg-blue-600/30 ring-2 ring-blue-500/50' 
          : 'bg-blue-600/10 hover:bg-blue-600/20'
      } text-blue-600 dark:text-blue-400`
    },
    { 
      id: 'settings', 
      icon: faCog,
      label: 'Settings', 
      action: () => {
        navigate('/settings');
        onClose();
      },
      className: 'bg-gray-500/10 hover:bg-gray-500/20 text-gray-600 dark:text-gray-300'
    }
  ], [navigate, onClose]);
  // Close menu when clicking outside or pressing Escape
  useEffect(() => {
    if (!isOpen) return;
    
    const handleClickOutside = (event) => {
      // Don't close if clicking on the cast menu or its trigger button
      const isCastMenu = document.querySelector('[role="dialog"][data-cast-menu]');
      const isCastButton = event.target.closest('[data-cast-button]');
      
      if (isCastMenu || isCastButton) {
        event.stopPropagation();
        return;
      }
      
      if (menuRef.current && !menuRef.current.contains(event.target)) {
        onClose();
      }
    };
    
    const handleEscape = (event) => {
      if (event.key === 'Escape') {
        // Only close if we're not in the cast menu
        const isCastMenu = document.querySelector('[role="dialog"][data-cast-menu]');
        if (!isCastMenu) {
          onClose();
        } else {
          // Let the cast menu handle its own Escape key
          event.stopPropagation();
        }
      }
    };

    // Use capture phase to ensure we catch the event before it bubbles up
    document.addEventListener('mousedown', handleClickOutside, true);
    document.addEventListener('keydown', handleEscape, true);
    
    return () => {
      document.removeEventListener('mousedown', handleClickOutside, true);
      document.removeEventListener('keydown', handleEscape, true);
    };
  }, [isOpen, onClose]);

  // Memoize filtered apps to prevent recalculation
  // Filter apps based on search term
  const filteredApps = useMemo(() => {
    // Skip filtering if no search term
    if (!searchTerm.trim()) return defaultApps;
    
    const term = searchTerm.toLowerCase();
    return defaultApps.filter(app => 
      (app.name && app.name.toLowerCase().includes(term)) ||
      (app.description && app.description.toLowerCase().includes(term))
    );
  }, [defaultApps, searchTerm]);

  // Memoize app item to prevent unnecessary re-renders
  const AppItem = React.memo(({ app, onClose, className = '', buttonProps = {} }) => {
    const handleClick = useCallback((e) => {
      if (!e) return;
      
      // Prevent event from bubbling up to parent elements
      e.stopPropagation();
      e.preventDefault();
      
      try {
        // Execute the app's action if it exists
        if (typeof app.action === 'function') {
          app.action(e);
        } else {
          // Default behavior for apps without custom actions
          if (typeof onAppSelect === 'function') {
            onAppSelect(app);
          }
          if (typeof onClose === 'function') {
            onClose();
          }
        }
      } catch (err) {
        console.error('Error in app action:', err);
      }
    }, [app, onAppSelect, onClose]);

    // Memoize the icon component to prevent re-renders
    const icon = useMemo(() => {
      const iconProps = {
        className: "text-lg",
        icon: app.icon || faGlobe
      };
      return <FontAwesomeIcon {...iconProps} />;
    }, [app.icon]);

    // Calculate dynamic class names once
    const containerClass = `flex flex-col items-center p-2 m-1 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors cursor-pointer w-20 ${className}`;
    const iconClass = `w-10 h-10 rounded-lg flex items-center justify-center mb-1 ${app.className || (
      app.id === 'browser' || app.icon === faGlobe ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400' :
      app.id === 'chat' || app.icon === faComments ? 'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400' :
      app.id === 'files' || app.icon === faFolderOpen ? 'bg-yellow-100 dark:bg-yellow-900/50 text-yellow-600 dark:text-yellow-400' :
      'bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300'
    )}`;

    return (
      <div 
        onClick={handleClick}
        className={containerClass}
        title={app.description || app.label}
        role="button"
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            handleClick(e);
          }
        }}
        {...(buttonProps || {})}
      >
        <div className={iconClass}>
          {icon}
        </div>
        <span className="text-xs text-center text-gray-700 dark:text-gray-300 truncate w-full">
          {app.label || app.name}
        </span>
      </div>
    );
  }, (prevProps, nextProps) => {
    // Custom comparison function for React.memo
    return (
      prevProps.app.id === nextProps.app.id &&
      prevProps.app.label === nextProps.app.label &&
      prevProps.app.icon === nextProps.app.icon &&
      prevProps.className === nextProps.className
    );
  });

  // Memoize the quick actions section
  const quickActionsSection = useMemo(() => (
    <div className="flex flex-col space-y-4">
      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Quick Actions</h3>
      <div className="flex flex-wrap">
        {quickActions.map((action) => (
          <AppItem 
            key={`action-${action.id}`} 
            app={action} 
            onClose={onClose} 
            className="w-16"
            buttonProps={action.buttonProps}
          />
        ))}
      </div>
    </div>
  ), [quickActions, onClose]);

  // Memoize the apps section
  const appsSection = useMemo(() => (
    <div className="flex flex-col space-y-4">
      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400">Applications</h3>
      <div className="flex flex-wrap">
        {filteredApps.map((app) => (
          <AppItem 
            key={`app-${app.id}`} 
            app={app} 
            onClose={onClose} 
            className="w-16" 
          />
        ))}
      </div>
    </div>
  ), [filteredApps, onClose]);

  // Page 1: Quick Actions and Apps
  const renderAppsPage = useCallback(() => (
    <>
      <div className="p-4">
        <div className="relative">
          <input
            type="text"
            placeholder="Search apps and actions..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full px-4 py-2 pl-10 bg-white/50 dark:bg-gray-800/50 border border-gray-200 dark:border-gray-700 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
            autoFocus
          />
          <FontAwesomeIcon 
            icon={faSearch} 
            className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4"
          />
        </div>
      </div>

      {/* Quick Actions */}
      <div className="flex justify-center space-x-4 px-4 mb-4">
        {quickActions.map((action) => (
          <button
            key={action.id}
            onClick={(e) => {
              e.stopPropagation();
              action.action();
            }}
            className={`flex flex-col items-center justify-center p-3 rounded-xl ${typeof action.className === 'function' ? action.className(activeAction === action.id) : action.className}`}
            aria-label={action.label}
          >
            <div className="w-8 h-8 flex items-center justify-center rounded-xl mb-1">
              <FontAwesomeIcon icon={action.icon} className="w-3.5 h-3.5" />
            </div>
            <span className="text-xs font-medium text-center">
              {action.label}
            </span>
          </button>
        ))}
      </div>

      {/* Apps Grid */}
      <div className="border-t border-gray-200 dark:border-gray-700 my-2" />
      <h3 className="text-sm font-medium text-gray-500 dark:text-gray-400 mb-3 px-5">Applications</h3>
      <div className="grid grid-cols-4 gap-4 p-4">
        {defaultApps.map((app) => (
          <button
            key={app.id}
            onClick={() => {
              if (app.action) {
                app.action();
              } else {
                navigate(app.path || `/${app.id}`);
              }
              onClose();
            }}
            className="flex flex-col items-center justify-center p-4 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group"
            aria-label={app.name}
          >
            <div className="w-10 h-10 flex items-center justify-center rounded-xl mb-1.5 bg-gray-100 dark:bg-gray-800 group-hover:bg-white dark:group-hover:bg-gray-700 transition-colors">
              <FontAwesomeIcon icon={app.icon} className="w-4 h-4 text-gray-700 dark:text-gray-300" />
            </div>
            <span className="text-xs font-medium text-gray-700 dark:text-gray-300 text-center line-clamp-1">
              {app.name}
            </span>
          </button>
        ))}
      </div>
    </>
  ), [searchTerm, defaultApps, navigate, onClose, quickActions, activeAction]);

  // Memoize recent activity items
  const recentActivityItems = useMemo(() => {
    if (recentActivity.length === 0) {
      return (
        <div className="text-center py-4 text-gray-500 dark:text-gray-400">
          <p>No recent activity</p>
        </div>
      );
    }

    return (
      <div className="space-y-2">
        {recentActivity.map((activity) => {
          const activityType = activity.type.charAt(0).toUpperCase() + activity.type.slice(1);
          return (
            <React.Fragment key={activity.id}>
              <div 
                className="flex items-center p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700 transition-colors"
              >
                <div className="w-8 h-8 rounded-full bg-gray-200 dark:bg-gray-700 flex items-center justify-center mr-3">
                  <FontAwesomeIcon icon={activity.icon || faHistory} className="text-gray-500" />
                </div>
                <div className="flex-1">
                  <p className="text-sm font-medium text-gray-800 dark:text-gray-200">
                    {activityType} {activity.app}
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-400">{activity.time}</p>
                </div>
              </div>
            </React.Fragment>
          );
        })}
      </div>
    );
  }, [recentActivity]);

  // Page 2: Recent Activity
  const renderRecentActivityPage = useCallback(() => (
    <div className="flex flex-col space-y-4 p-4">
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => setCurrentPage(0)}
          className="flex items-center text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 text-sm"
          aria-label="Back to apps"
        >
          <FontAwesomeIcon icon={faChevronLeft} className="mr-2" />
          Back to Apps
        </button>
        <h3 className="text-sm font-medium text-gray-700 dark:text-gray-200">Recent Activity</h3>
        <div className="w-6" aria-hidden="true" />
      </div>
      {recentActivityItems}
    </div>
  ), [recentActivityItems]);

  if (!isOpen) return null;

  return (
    <>
      <div className="fixed inset-0 bg-black/40 dark:bg-black/60 backdrop-blur-sm z-50 flex items-start justify-center pt-16">
        <div 
          ref={menuRef}
          className="w-full max-w-2xl bg-white/90 dark:bg-gray-900/95 rounded-t-lg shadow-2xl border border-gray-200/50 dark:border-gray-700/50 overflow-hidden backdrop-blur-lg"
        >
          <div className="flex items-center justify-between px-6 py-3 border-b border-gray-200/50 dark:border-gray-700/50 bg-white/50 dark:bg-gray-800/50">
            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
              {currentPage === 0 ? 'Quick Menu' : 'Recent Activity'}
            </h3>
            <div className="flex items-center">
              {currentPage === 1 && (
                <button 
                  onClick={() => setCurrentPage(0)}
                  className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 p-1 mx-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  aria-label="Back to apps"
                >
                  <FontAwesomeIcon icon={faChevronLeft} className="w-4 h-4" />
                </button>
              )}
              <button 
                onClick={onClose}
                className="text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 p-1 rounded-full hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                aria-label="Close menu"
              >
                <FontAwesomeIcon icon={faTimes} className="w-4 h-4" />
              </button>
            </div>
          </div>
          
          <div className="max-h-[70vh] overflow-y-auto p-2">
            {currentPage === 0 ? renderAppsPage() : renderRecentActivityPage()}
          </div>
          
          {/* Page indicators */}
          <div className="flex justify-center space-x-2 pb-2">
            {[0, 1].map((page) => (
              <button
                key={page}
                onClick={() => setCurrentPage(page)}
                className={`w-2 h-2 rounded-full transition-colors ${
                  currentPage === page 
                    ? 'bg-blue-500' 
                    : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
                }`}
                aria-label={`Page ${page + 1}`}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Cast Menu */}
      <CastMenu 
        isOpen={isCastMenuOpen} 
        onClose={() => setIsCastMenuOpen(false)} 
      />
    </>
  );
});

QuickMenu.displayName = 'QuickMenu';

// Add prop types for better type checking
QuickMenu.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onAppSelect: PropTypes.func,
  apps: PropTypes.arrayOf(PropTypes.shape({
    id: PropTypes.string,
    name: PropTypes.string,
    label: PropTypes.string,
    icon: PropTypes.any,
    path: PropTypes.string,
    description: PropTypes.string,
  })),
};

QuickMenu.defaultProps = {
  onAppSelect: undefined,
  apps: [],
};

// Export the component with React.memo for performance
export default React.memo(QuickMenu);
