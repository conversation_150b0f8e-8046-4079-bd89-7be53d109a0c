import React, { createContext, useContext, useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { safeInvoke } from '../utils/tauriHelpers';
import { safeInjectCSS } from '../utils/domSafety';
import saveManager, { deepMerge } from '../utils/SaveManager';
import { useNotifications } from './NotificationContext';

// Default settings to use when backend is not available
const DEFAULT_SETTINGS = {
  // Version for migration handling
  config_version: 2,
  appearance: {
    theme: 'voidCircuit',
    dark_mode: window.matchMedia('(prefers-color-scheme: dark)').matches,
    layout: 'default',
    color_schemes: [
      { id: 'voidCircuit', name: 'Void Circuit', is_dark: true, colors: { primary: '#00BCD4', background: '#121212', surface: '#1E1E1E', accent: '#FF4081', text: '#E0E0E0' } },
    ],
    backgrounds: [
      { id: 'default', name: 'Default', type: 'color', value: '#121212', opacity: 1.0 },
    ],
    font_family: 'sans-serif',
    font_size: '16px',
    line_height: 1.5,
    ui_scale: 1.0,
    animations: true,
    reduced_motion: false,
    high_contrast: false,
    color_blind_mode: 'none',
    custom_css: '',
  },
  plugin_states: {},
  system_log_path: './Storage/System/logs/',
  indexed_directory: './Storage/',
  plugins_path: './Storage/Addons/Plugins/',
  mcps_path: './Storage/Addons/MCP/',
  apis_path: './Storage/Addons/API/',
  models_path: './Storage/System/Models/',
  servers_path: './Storage/System/Servers/',
  system_prompts_path: './Storage/Addons/Logic/',
  date_format: 'YYYY-MM-DD',
  time_format: '24h',
  timezone: 'UTC',
  auto_save: true,
  startup_tab: 'chat',
  window_maximized: false,
  notifications: {
    enabled: true,
    position: 'bottom-right',
    duration: 5000,
    showSaveNotifications: true,
    showLoadNotifications: false,
    showErrorNotifications: true,
    showWarningNotifications: true,
    showInfoNotifications: true,
    showSuccessNotifications: true,
    soundEnabled: false,
    maxNotifications: 5,
    defaultSource: 'system',
    defaultCategory: 'general',
    defaultPriority: 'medium',
  },
  browser_homepage: 'https://www.google.com',
  browser_zoom_level: 100,
  browser_enable_javascript: true,
  browser_enable_images: true,
  browser_enable_cookies: true,
  browser_block_popups: true,
  server_url: 'http://127.0.0.1:11435',
  server_host: '127.0.0.1',
  server_port: 11435,
  network_protocol: 'http',
  proxy_mode: 'none',
  proxy_host: '',
  proxy_port: 0,
  connection_timeout: 30,
  max_retries: 3,
  enable_ssl: false,
  verify_certificates: true,
  default_model: 'llama2',
  auto_start_server: false,
  ios_projection_enabled: false,
  ios_device_name: 'The Collective',
  ios_projection_quality: 'auto',
  android_projection_enabled: false,
  android_device_name: 'The Collective',
  android_projection_quality: 'auto',
  miracast_enabled: false,
  dlna_enabled: false,
  projection_local_only: true,
  projection_port: 8080,
  projection_protocol: 'auto'
};

const SettingsContext = createContext();

export const useSettings = () => {
  const context = useContext(SettingsContext);
  if (!context) {
    console.warn('useSettings must be used within a SettingsProvider. Using default settings.');
    return {
      userSettings: DEFAULT_SETTINGS,
      isLoading: false,
      error: null,
      isBackendAvailable: false,
      saveSettings: async () => {
        console.warn('Cannot save settings - SettingsProvider not found');
        return false;
      },
      saveUserSetting: async () => {
        console.warn('Cannot save setting - SettingsProvider not found');
        return false;
      },
      updateUserSettings: async () => {
        console.warn('Cannot update settings - SettingsProvider not found');
        return false;
      },
      refreshSettings: () => {}
    };
  }
  return context;
};

export function SettingsProvider({ children }) {
  // Use the main DEFAULT_SETTINGS object to avoid duplication
  const memoizedDefaultSettings = useMemo(() => DEFAULT_SETTINGS, []);
  const { addNotification } = useNotifications();

  const [settings, setSettings] = useState(memoizedDefaultSettings);
  const [isLoading, setIsLoading] = useState(false); // Start with false to prevent loading screen
  const [error, setError] = useState(null);
  const isMounted = useRef(true);
  const settingsRef = useRef(memoizedDefaultSettings);

  // Memoize the settings object to prevent unnecessary re-renders
  const memoizedSettings = useMemo(() => settings, [settings]);

  // Initialize SaveManager with notification callback
  useEffect(() => {
    saveManager.setNotificationCallback(addNotification);
    console.log('🚀 SettingsContext: SaveManager initialized');
  }, [addNotification]);

  // Initialize with default settings immediately to prevent loading screen
  useEffect(() => {
    console.log('🚀 SettingsContext: Initializing with default settings');
    setSettings(memoizedDefaultSettings);
    settingsRef.current = memoizedDefaultSettings;
    setIsLoading(false);
  }, [memoizedDefaultSettings]);

  // Load settings using SaveManager
  const loadSettings = useCallback(async () => {
    if (!isMounted.current) return;

    try {
      console.log('📥 SettingsContext: Loading settings via SaveManager');
      const loadedSettings = await saveManager.load('settings', memoizedDefaultSettings);

      if (!isMounted.current) return;

      console.log('✅ SettingsContext: Settings loaded successfully', JSON.stringify(loadedSettings, null, 2));
      setSettings(loadedSettings);
      settingsRef.current = loadedSettings;
      setError(null);

    } catch (err) {
      console.error('❌ SettingsContext: Failed to load settings:', err);
      if (isMounted.current) {
        setError('Failed to load settings. Using defaults.');
        addNotification({
          type: 'warning',
          message: 'Failed to load settings, using defaults'
        });
      }
    }
  }, [memoizedDefaultSettings, addNotification]);

  // Mount/unmount lifecycle and initial load
  useEffect(() => {
    isMounted.current = true;
    // Load settings from backend immediately
    loadSettings();

    return () => {
      isMounted.current = false;
    };
  }, [loadSettings]);

  // Apply only custom CSS - let ThemeContext handle theme application
  useEffect(() => {
    if (!settings || !settings.appearance) return;

    const applyAppearance = () => {
      const { appearance } = settings;

      // Apply custom CSS if defined with safety checks
      if (appearance.custom_css) {
        safeInjectCSS(appearance.custom_css, 'custom-theme-styles');
      }
    };

    applyAppearance();

    // Cleanup function to remove any injected styles if component unmounts
    return () => {
      const style = document.getElementById('custom-theme-styles');
      if (style) {
        style.remove();
      }
    };
  }, [settings?.appearance?.custom_css]);

  // Save settings using SaveManager - NO DEBOUNCING, IMMEDIATE SAVE
  const saveSettings = useCallback(async (newSettings) => {
    if (!isMounted.current) return false;

    try {
      // Update local state immediately
      const updatedSettings = { ...settingsRef.current, ...newSettings };
      setSettings(updatedSettings);
      settingsRef.current = updatedSettings;

      // Save immediately using SaveManager
      console.log('💾 SettingsContext: Saving settings immediately');
      const success = await saveManager.save('settings', updatedSettings, {
        immediate: true,
        showNotification: true
      });

      if (!success) {
        setError('Failed to save settings');
      }

      return success;
    } catch (err) {
      console.error('❌ SettingsContext: Error saving settings:', err);
      setError('Failed to save settings');
      addNotification({
        type: 'error',
        message: 'Failed to save settings'
      });
      return false;
    }
  }, [addNotification]);

  // Update a specific setting with deep merge for nested objects
  const updateSetting = useCallback(async (key, value) => {
    if (!isMounted.current) return false;

    try {
      // Create a deep clone of current settings to avoid direct state mutation
      const currentSettings = JSON.parse(JSON.stringify(settingsRef.current));

      // Handle nested updates (like appearance)
      if (key.includes('.')) {
        const keys = key.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((obj, k) => (obj[k] = obj[k] || {}), currentSettings);
        target[lastKey] = value;
      }
      // Handle appearance updates (special case)
      else if (key === 'appearance' && value && typeof value === 'object') {
        currentSettings.appearance = deepMerge(currentSettings.appearance, value);
      }
      // Regular top-level update
      else {
        currentSettings[key] = value;
      }

      return await saveSettings(currentSettings);
    } catch (err) {
      console.error('Error updating setting:', err);
      return false;
    }
  }, [saveSettings]);

  // (moved loadSettings above for proper initialization)


  // Refresh settings from backend
  const refreshSettings = async () => {
    await loadSettings();
  };

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    userSettings: memoizedSettings,  // Components expect userSettings, not settings
    settings: memoizedSettings,      // Keep both for compatibility
    isLoading,
    error,
    updateSetting,
    saveSettings,
    saveUserSetting: updateSetting,  // Alias for compatibility
    updateUserSettings: saveSettings, // Alias for compatibility
    loadSettings,
    refreshSettings: loadSettings,   // Alias for compatibility
  }), [memoizedSettings, isLoading, error, updateSetting, saveSettings, loadSettings]);

  return (
    <SettingsContext.Provider value={contextValue}>
      {children}
    </SettingsContext.Provider>
  );
};
