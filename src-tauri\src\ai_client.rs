use serde::{Deserialize, Serialize};
use std::process::{Child, Command};
use std::sync::{Arc, Mutex};
use tokio::time::{sleep, Duration};
use reqwest::Client;
use tauri::{Manager, Emitter};
use tokio_stream::StreamExt;
use crate::settings_manager::UserPreferences; // Import UserPreferences
use log::{info, debug};

#[derive(Debug, <PERSON>lone, Serialize, Deserialize)]
pub struct ModelPullProgress {
    pub model_name: String,
    pub status: String,
    pub total: u64,
    pub completed: u64,
}

#[derive(Debug)]
pub enum AiError {
    Reqwest(reqwest::Error),
    Io(std::io::Error),
    <PERSON><PERSON>(serde_json::Error),
    Custom(String),
}

impl From<reqwest::Error> for AiError {
    fn from(err: reqwest::Error) -> Self {
        AiError::Reqwest(err)
    } 
}

impl From<std::io::Error> for AiError {
    fn from(err: std::io::Error) -> Self {
        AiError::Io(err)
    }
}

impl From<serde_json::Error> for AiError {
    fn from(err: serde_json::Error) -> Self {
        AiError::Json(err)
    }
}

impl std::fmt::Display for AiError {
    fn fmt(&self, f: &mut std::fmt::Formatter<'_>) -> std::fmt::Result {
        match self {
            AiError::Reqwest(err) => write!(f, "Reqwest error: {}", err),
            AiError::Io(err) => write!(f, "IO error: {}", err),
            AiError::Json(err) => write!(f, "JSON error: {}", err),
            AiError::Custom(err) => write!(f, "Custom error: {}", err),
        }
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct AiStatus {
    pub status: String,
}

pub struct AiClient {
    client: Client,
    language_server_process: Arc<Mutex<Option<Child>>>,
    base_url: String,
    prefs: UserPreferences, // Add UserPreferences to AiClient state
}

impl AiClient {
    pub fn new(base_url: String, prefs: UserPreferences) -> Self {
        AiClient {
            client: Client::new(),
            language_server_process: Arc::new(Mutex::new(None)),
            base_url,
            prefs, // Initialize prefs
        }
    }

    pub async fn start_server(&self, server_path: Option<&str>) -> Result<(), AiError> {
        info!("Starting server with path: {:?}", server_path);
        println!("🚀 === STARTING EMBEDDED AI SERVER ===");

        // Stop any running server instance first
        let _ = std::process::Command::new("taskkill").args(&["/F", "/IM", "llama-server.exe"]).output();
        let _ = std::process::Command::new("taskkill").args(&["/F", "/IM", "ollama.exe"]).output();

        if self.language_server_process.lock().unwrap().is_some() {
            println!("🛑 Embedded server process already running, stopping existing process first");
            self.stop_server().await?;
        }

        let server_path = server_path.ok_or_else(|| AiError::Custom("No server profile configured.".to_string()))?;
        let server_dir = std::path::Path::new(server_path);
        println!("📁 Server directory: {}", server_dir.display());

        let detected_executables = crate::settings_manager::detect_server_executables(server_dir);
        let primary_executable = detected_executables.first().ok_or_else(|| AiError::Custom(format!("No executable found in server profile directory: {}", server_dir.display())))?;
        
        let server_type = crate::settings_manager::identify_server_type("", &detected_executables);

        println!("🔍 Detected server type: {:?}", server_type);
        println!("🚀 Launching primary executable: {}", primary_executable.path);

        let mut cmd = Command::new(&primary_executable.path);
        cmd.stdout(std::process::Stdio::piped()).stderr(std::process::Stdio::piped());

        // Set the correct working directory for the server process
        if let Some(server_dir) = std::path::Path::new(&primary_executable.path).parent() {
            cmd.current_dir(server_dir);
            println!("📂 Set server working directory to: {}", server_dir.display());
        }

        let prefs = crate::settings_manager::UserPreferences::load();
        let models_path = prefs.models_path;
        let active_model = prefs.active_model_profile;


        if let Some(st) = server_type.as_deref() {
            match st {
                "llamacpp" => {
                    println!("🔧 Configuring for llama.cpp server...");
                    if active_model.is_empty() {
                        println!("⚠️ No active model selected, llama.cpp server might not start correctly.");
                        //return; // Or handle this case appropriately
                    }
                    let model_path = std::path::Path::new(&models_path).join(format!("{}.gguf", active_model));
                    println!("💡 Using model path: {}", model_path.display());

                    cmd.arg("--host")
                        .arg("127.0.0.1")
                        .arg("--port")
                        .arg("11435")
                        .arg("--model")
                        .arg(model_path);
                    // Add other relevant llama.cpp args here if needed
                    // e.g., "--ctx-size",
                }
                "ollama" | _ => { // Default to Ollama configuration
                    println!("🔧 Configuring for Ollama server...");
                    cmd.env("AI_HOST", "127.0.0.1:11435");
                    cmd.env("AI_MODELS", &models_path);
                    cmd.env("AI_ORIGINS", "http://127.0.0.1:11435,http://localhost:11435");
                    cmd.env("AI_DEBUG", "1");
                    cmd.env("AI_FLASH_ATTENTION", "1");
                    cmd.env("AI_NOPRUNE", "1");
                    cmd.env("AI_KEEP_ALIVE", "5m");

                    if let Some(server_dir) = std::path::Path::new(&primary_executable.path).parent() {
                        let data_path = server_dir.join("data");
                        if let Err(e) = std::fs::create_dir_all(&data_path) {
                            println!("Warning: Failed to create server data directory: {}", e);
                        } else {
                            cmd.env("AI_HOME", data_path.clone());
                            println!("   ENV AI_HOME={}", data_path.display());
                        }
                    }
                }
            }
        }

        let mut child = cmd.spawn().map_err(|e| AiError::Custom(format!("Failed to start server from {}: {}", primary_executable.path, e)))?;
        
        // --- Start of corrected logging logic ---
        use std::io::{BufRead, BufReader};

        if let Some(stdout) = child.stdout.take() {
            tokio::spawn(async move {
                let mut reader = BufReader::new(stdout);
                let mut line = String::new();
                while reader.read_line(&mut line).unwrap_or(0) > 0 {
                    print!("[server-stdout] {}", line);
                    line.clear();
                }
            });
        }

        if let Some(stderr) = child.stderr.take() {
            tokio::spawn(async move {
                let mut reader = BufReader::new(stderr);
                let mut line = String::new();
                while reader.read_line(&mut line).unwrap_or(0) > 0 {
                    eprint!("[server-stderr] {}", line);
                    line.clear();
                }
            });
        }
        // --- End of corrected logging logic ---

        *self.language_server_process.lock().unwrap() = Some(child);

        for i in 0..60 {
            if self.is_server_running().await {
                println!("✅ Embedded server is ready after {} seconds", i + 1);
                if let Ok(models) = self.get_models_internal().await {
                    if let Some(first_model) = models.first() {
                        let _ = tokio::spawn({
                            let model_name = first_model.clone();
                            let base_url = self.base_url.clone();
                            async move {
                                let client = reqwest::Client::new();
                                let url = format!("{}/api/generate", base_url);
                                let body = serde_json::json!({
                                    "model": model_name,
                                    "prompt": "Hi",
                                    "stream": false,
                                    "options": {
                                        "num_predict": 1
                                    }
                                });
                                let _ = client.post(&url).json(&body).send().await;
                            }
                        });
                    }
                }

                return Ok(());
            }
            sleep(Duration::from_secs(1)).await;
        }

        Err(AiError::Custom("Embedded server did not start in time".to_string()))
    }

    pub async fn stop_server(&self) -> Result<(), AiError> {
        let mut process_guard = self.language_server_process.lock().unwrap();
        if let Some(mut child) = process_guard.take() {
            child.kill().map_err(AiError::Io)?;
            child.wait().map_err(AiError::Io)?;
        }
        Ok(())
    }

    pub async fn get_server_status_internal(&self) -> Result<AiStatus, AiError> {
        use crate::settings_manager::UserPreferences;
        let prefs = UserPreferences::load();
        let has_active_server = prefs.server_profiles.iter().any(|(_, profile)| profile.enabled);

        if !has_active_server {
            return Ok(AiStatus { status: "no_server_profile".to_string() });
        }

        let has_process = {
            let mut process_guard = self.language_server_process.lock().unwrap();
            if let Some(ref mut child) = *process_guard {
                match child.try_wait() {
                    Ok(Some(_)) => {
                        *process_guard = None;
                        false
                    },
                    Ok(None) => true,
                    Err(_) => {
                        *process_guard = None;
                        false
                    }
                }
            } else {
                false
            }
        };

        if has_process {
            if self.is_server_running().await {
                Ok(AiStatus { status: "running".to_string() })
            } else {
                Ok(AiStatus { status: "starting".to_string() })
            }
        } else {
            Ok(AiStatus { status: "stopped".to_string() })
        }
    }

    pub async fn is_server_running(&self) -> bool {
        // First, check the process list to avoid unnecessary HTTP requests
        let has_process = self.language_server_process.lock().unwrap().is_some();
        if !has_process {
            return false;
        }

        // Use a generic health endpoint for llama.cpp or a known Ollama endpoint
        // This makes the check more robust for different server types.
        let health_url = format!("{}/health", self.base_url); // Common for llama.cpp
        let ollama_url = format!("{}/api/tags", self.base_url); // Specific to Ollama

        // Try llama.cpp health check first
        if let Ok(response) = self.client.get(&health_url).send().await {
            if response.status().is_success() {
                if let Ok(json) = response.json::<serde_json::Value>().await {
                    if json.get("status").and_then(|s| s.as_str()) == Some("ok") {
                        return true;
                    }
                }
            }
        }

        // Fallback to Ollama check
        if let Ok(response) = self.client.get(&ollama_url).send().await {
            if response.status().is_success() {
                return true;
            }
        }

        false
    }

    pub async fn get_models_internal(&self) -> Result<Vec<String>, AiError> {
        // Use the appropriate adapter based on active server profile
        match crate::server_adapters::create_adapter_from_active_profile().await {
            Ok(adapter) => {
                match adapter.get_models().await {
                    Ok(models) => Ok(models),
                    Err(e) => {
                        println!("Adapter error getting models: {}", e);
                        // Fallback for backward compatibility
                        self.get_models_fallback().await
                    }
                }
            },
            Err(e) => {
                println!("Failed to create adapter: {}", e);
                // Fallback for backward compatibility
                self.get_models_fallback().await
            }
        }
    }
    
    // Fallback method for backward compatibility
    async fn get_models_fallback(&self) -> Result<Vec<String>, AiError> {
        // Try Ollama API first
        let ollama_url = format!("{}/api/tags", self.base_url);
        if let Ok(response) = self.client.get(&ollama_url).send().await {
            if response.status().is_success() {
                if let Ok(json) = response.json::<serde_json::Value>().await {
                    let models: Vec<String> = json["models"].as_array()
                        .unwrap_or(&vec![])
                        .iter()
                        .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
                        .collect();
                    if !models.is_empty() {
                        return Ok(models);
                    }
                }
            }
        }

        // Fallback for llama.cpp server - scan models directory
        use crate::settings_manager::UserPreferences;
        let prefs = UserPreferences::load();
        let models_path = std::path::Path::new(&prefs.models_path);
        
        if models_path.exists() && models_path.is_dir() {
            if let Ok(entries) = std::fs::read_dir(models_path) {
                let models: Vec<String> = entries
                    .filter_map(|entry| entry.ok())
                    .filter(|entry| {
                        entry.path().extension()
                            .and_then(|ext| ext.to_str()) == Some("gguf")
                    })
                    .filter_map(|entry| {
                        entry.path().file_stem()
                            .and_then(|stem| stem.to_str())
                            .map(|s| s.to_string())
                    })
                    .collect();
                
                if !models.is_empty() {
                    return Ok(models);
                }
            }
        }
        
        // Final fallback
        Ok(vec!["default-model".to_string()])
    }

    pub async fn pull_model_internal(&self, app_handle: &tauri::AppHandle, model_name: &str) -> Result<(), AiError> {
        if !self.is_server_running().await {
            return Err(AiError::Custom("Server is not running. Please start the server first.".to_string()));
        }

        let url = format!("{}/api/pull", self.base_url);
        let body = serde_json::json!({ "name": model_name, "stream": true });

        let mut stream = self.client.post(&url).json(&body).send().await?.bytes_stream();

        app_handle.emit("model_pull_start", model_name).unwrap();

        while let Some(item) = stream.next().await {
            if let Ok(chunk) = item {
                let s = String::from_utf8_lossy(&chunk);
                for line in s.lines() {
                    if let Ok(json) = serde_json::from_str::<serde_json::Value>(line) {
                        let status = json.get("status").and_then(|s| s.as_str()).unwrap_or("downloading").to_string();
                        let total = json.get("total").and_then(|t| t.as_u64()).unwrap_or(0);
                        let completed = json.get("completed").and_then(|c| c.as_u64()).unwrap_or(0);

                        let progress = ModelPullProgress {
                            model_name: model_name.to_string(),
                            status,
                            total,
                            completed,
                        };

                        app_handle.emit("model_pull_progress", progress).unwrap();
                    }
                }
            }
        }

        app_handle.emit("model_pull_success", model_name).unwrap();
        Ok(())
    }
}



#[tauri::command]
pub async fn start_server_command(app_handle: tauri::AppHandle) -> Result<(), String> {
    println!("Received start_server_command");
    let client = app_handle.state::<AiClient>();
    if client.is_server_running().await {
        println!("Server is already running");
        return Ok(());
    }
    let profiles = match crate::settings_manager::get_server_profiles().await {
        Ok(profiles) => profiles,
        Err(e) => return Err(format!("Failed to get server profiles: {}", e)),
    };
    let server_path_option = if let Some(enabled_profile) = profiles.iter().find(|p| p.enabled) {
        Some(enabled_profile.folder_path.clone())
    } else if let Some(first_profile) = profiles.first() {
        if let Err(e) = crate::settings_manager::toggle_server_profile(first_profile.name.clone(), true).await {
            println!("Failed to enable server profile: {}", e);
        }
        Some(first_profile.folder_path.clone())
    } else {
        return Err("No server profiles found. Please configure a server profile first.".to_string());
    };
    match client.start_server(server_path_option.as_deref()).await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn stop_server_command(app_handle: tauri::AppHandle) -> Result<(), String> {
    let client = app_handle.state::<AiClient>();
    match client.stop_server().await {
        Ok(_) => Ok(()),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_server_status(app_handle: tauri::AppHandle) -> Result<AiStatus, String> {
    let client = app_handle.state::<AiClient>();
    match client.get_server_status_internal().await {
        Ok(status) => Ok(status),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn get_models_command(app_handle: tauri::AppHandle) -> Result<Vec<String>, String> {
    let client = app_handle.state::<AiClient>();
    match client.get_models_internal().await {
        Ok(models) => Ok(models),
        Err(e) => Err(e.to_string()),
    }
}

#[tauri::command]
pub async fn pull_model_command(app_handle: tauri::AppHandle, model_name: String) -> Result<(), String> {
    tokio::spawn(async move {
        let client = app_handle.state::<AiClient>();
        match client.pull_model_internal(&app_handle, &model_name).await {
            Ok(_) => {
                app_handle.emit("model_pull_success", &model_name).unwrap();
            }
            Err(e) => {
                app_handle.emit("model_pull_error", e.to_string()).unwrap();
            }
        }
    });
    Ok(())
}

#[tauri::command]
pub async fn check_installation_command(_app_handle: tauri::AppHandle) -> Result<String, String> {
    use crate::settings_manager::{get_models_path, UserPreferences};
    let prefs = UserPreferences::load();
    let models_path = get_models_path().await.unwrap_or_default();

    if models_path.is_empty() {
        return Err("Server path not configured.".to_string());
    }

    let active_server = prefs.server_profiles.iter()
        .find(|(_, profile)| profile.enabled)
        .map(|(name, _)| name.clone());

    if let Some(active_server_name) = active_server {
        let server_dir = std::path::Path::new(&active_server_name);
        let detected_executables = crate::settings_manager::detect_server_executables(&server_dir);

        if let Some(primary_executable) = detected_executables.first() {
            let exe_path = std::path::Path::new(&primary_executable.path);
            let mut status = format!("✓ Server executable found at: {}\n", exe_path.display());
            status.push_str(&format!("✓ Executable type: {}\n", primary_executable.executable_type));
            status.push_str(&format!("✓ Active server profile: {}\n", active_server_name));
            status.push_str(&format!("✓ Server path: {}\n", active_server_name));
            status.push_str(&format!("✓ Models path: {}\n", models_path));

            if detected_executables.len() > 1 {
                status.push_str(&format!("ℹ Additional executables found ({})\n", detected_executables.len() - 1));
                for exec in detected_executables.iter().skip(1) {
                    status.push_str(&format!("  - {} ({})\n", exec.file_name, exec.executable_type));
                }
            }

            if std::path::Path::new(&models_path).exists() {
                status.push_str("✓ Models directory exists\n");
            } else {
                status.push_str("⚠ Models directory does not exist (will be created)\n");
            }

            Ok(status)
        } else {
            Err(format!(
                "✗ No executable found in: {}\n\nActive server profile: {}\nCurrent server path: {}\nCurrent models path: {}",
                server_dir.display(),
                active_server_name,
                active_server_name,
                models_path
            ))
        }
    } else {
        Err("No active server profile found.".to_string())
    }
}

#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ChatMessagePart {
    pub content: String,
}

#[tauri::command]
pub async fn preload_model_command(app_handle: tauri::AppHandle, model_name: String) -> Result<(), String> {
    let start_time = std::time::Instant::now();
    println!("🔵 [{}ms] Starting preload for model: {}", start_time.elapsed().as_millis(), &model_name);

    tokio::spawn(async move {
        let client = reqwest::Client::new();
        let url = "http://127.0.0.1:11435/api/generate";
        let request_body = serde_json::json!({
            "model": model_name,
            "prompt": "Hi",
            "stream": true,
            "options": { "num_predict": 1 }
        });

        let _ = app_handle.emit("preload_start", &model_name);
        println!("🔵 [{}ms] Emitted preload_start event for {}", start_time.elapsed().as_millis(), &model_name);

        match client.post(url).json(&request_body).send().await {
            Ok(mut response) => {
                println!("🔵 [{}ms] Received response for preload: {}", start_time.elapsed().as_millis(), &model_name);
                while let Some(_) = response.chunk().await.unwrap_or(None) {
                    // Consume the stream to ensure the model is loaded
                }
                println!("✅ [{}ms] Model preloaded successfully: {}", start_time.elapsed().as_millis(), &model_name);
                let _ = app_handle.emit("preload_success", &model_name);
            }
            Err(e) => {
                println!("❌ [{}ms] Failed to preload model {}: {}", start_time.elapsed().as_millis(), &model_name, e);
                let _ = app_handle.emit("preload_error", format!("Failed to preload {}: {}", &model_name, e));
            }
        }
    });

    Ok(())
}

#[tauri::command]
pub async fn generate_welcome_message_command(model_name: String) -> Result<String, String> {
    // Use the appropriate adapter based on active server profile
    match crate::server_adapters::create_adapter_from_active_profile().await {
        Ok(adapter) => {
            match adapter.generate_welcome_message(&model_name).await {
                Ok(message) => Ok(message),
                Err(e) => {
                    println!("Adapter error generating welcome message: {}", e);
                    // Fallback for backward compatibility
                    generate_welcome_message_fallback(model_name).await
                }
            }
        },
        Err(e) => {
            println!("Failed to create adapter: {}", e);
            // Fallback for backward compatibility
            generate_welcome_message_fallback(model_name).await
        }
    }
}

// Fallback function for backward compatibility
async fn generate_welcome_message_fallback(model_name: String) -> Result<String, String> {
    let prompt = "As a friendly and helpful AI assistant, introduce yourself to the user in a single, welcoming sentence.";
    let client = reqwest::Client::new();
    
    // Try llama.cpp server format first
    let llamacpp_url = "http://127.0.0.1:11435/completion";
    let llamacpp_body = serde_json::json!({
        "prompt": prompt,
        "n_predict": 50,
        "stream": false
    });

    match client.post(llamacpp_url).json(&llamacpp_body).send().await {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(json) => {
                        if let Some(content) = json.get("content").and_then(|r| r.as_str()) {
                            return Ok(content.trim().to_string());
                        }
                    },
                    Err(_) => {}
                }
            }
        },
        Err(_) => {}
    }
    
    // Fallback to Ollama API format
    let ollama_url = "http://127.0.0.1:11435/api/generate";
    let ollama_body = serde_json::json!({
        "model": model_name,
        "prompt": prompt,
        "stream": false,
        "options": { "num_predict": 50 }
    });

    match client.post(ollama_url).json(&ollama_body).send().await {
        Ok(response) => {
            if response.status().is_success() {
                match response.json::<serde_json::Value>().await {
                    Ok(json) => {
                        if let Some(content) = json.get("response").and_then(|r| r.as_str()) {
                            return Ok(content.trim().to_string());
                        }
                    },
                    Err(_) => {}
                }
            }
        },
        Err(_) => {}
    }
    
    // Default fallback
    Ok("Hello! How can I help you today?".to_string())
}

#[tauri::command]
pub async fn send_chat_message_command(
    app_handle: tauri::AppHandle,
    model_name: String,
    prompt: String,
) -> Result<(), String> {
    if model_name.trim().is_empty() || prompt.trim().is_empty() {
        return Err("Model and prompt cannot be empty".to_string());
    }

    tokio::spawn(async move {
        let start_time = std::time::Instant::now();
        println!("🔵 [{}ms] Spawning chat message task for model: {}", start_time.elapsed().as_millis(), &model_name);

        let client = reqwest::Client::new();
        
        // Try llama.cpp server format first (since that's what we're running)
        let llamacpp_url = format!("http://127.0.0.1:11435/completion");
        let llamacpp_body = serde_json::json!({
            "prompt": prompt,
            "n_predict": 256,
            "stream": true,
            "temperature": 0.3,
            "top_p": 0.8,
            "top_k": 20,
            "repeat_penalty": 1.05,
        });

        println!("🔵 [{}ms] Sending request to llama.cpp server...", start_time.elapsed().as_millis());
        
        match client.post(&llamacpp_url).json(&llamacpp_body).send().await {
            Ok(response) => {
                println!("🔵 [{}ms] Received response from llama.cpp server.", start_time.elapsed().as_millis());
                if response.status().is_success() {
                    let mut stream = response.bytes_stream();
                    println!("🔵 [{}ms] Starting to stream response...", start_time.elapsed().as_millis());

                    while let Some(item) = stream.next().await {
                        match item {
                            Ok(chunk) => {
                                if let Ok(text) = std::str::from_utf8(&chunk) {
                                    // Handle llama.cpp server-sent events format
                                    for line in text.lines() {
                                        if line.starts_with("data: ") {
                                            let json_str = &line[6..]; // Remove "data: " prefix
                                            if json_str == "[DONE]" {
                                                println!("✅ [{}ms] Stream ended.", start_time.elapsed().as_millis());
                                                let _ = app_handle.emit("chat_token_end", "Stream ended");
                                                return;
                                            }
                                            if let Ok(json) = serde_json::from_str::<serde_json::Value>(json_str) {
                                                if let Some(content) = json.get("content").and_then(|r| r.as_str()) {
                                                    let _ = app_handle.emit("chat_token", ChatMessagePart { content: content.to_string() });
                                                }
                                                if let Some(stop) = json.get("stop").and_then(|d| d.as_bool()) {
                                                    if stop {
                                                        println!("✅ [{}ms] Stream ended.", start_time.elapsed().as_millis());
                                                        let _ = app_handle.emit("chat_token_end", "Stream ended");
                                                        return;
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                            Err(e) => {
                                println!("❌ [{}ms] Stream error: {}", start_time.elapsed().as_millis(), e);
                                let _ = app_handle.emit("chat_error", format!("Stream error: {}", e));
                                break;
                            }
                        }
                    }
                    println!("✅ [{}ms] Stream finished.", start_time.elapsed().as_millis());
                    let _ = app_handle.emit("chat_token_end", "Stream finished");
                } else {
                    // If llama.cpp fails, try Ollama API format as fallback
                    println!("🔄 [{}ms] llama.cpp failed, trying Ollama API format...", start_time.elapsed().as_millis());
                    
                    let ollama_url = "http://127.0.0.1:11435/api/generate";
                    let ollama_body = serde_json::json!({
                        "model": model_name,
                        "prompt": prompt,
                        "stream": true,
                        "options": {
                            "temperature": 0.3,
                            "top_p": 0.8,
                            "top_k": 20,
                            "num_predict": 256,
                            "num_ctx": 1024,
                            "repeat_penalty": 1.05,
                        }
                    });
                    
                    match client.post(ollama_url).json(&ollama_body).send().await {
                        Ok(response) => {
                            if response.status().is_success() {
                                let mut stream = response.bytes_stream();
                                while let Some(item) = stream.next().await {
                                    match item {
                                        Ok(chunk) => {
                                            if let Ok(text) = std::str::from_utf8(&chunk) {
                                                for line in text.lines() {
                                                    if let Ok(json) = serde_json::from_str::<serde_json::Value>(line) {
                                                        if let Some(content) = json.get("response").and_then(|r| r.as_str()) {
                                                            let _ = app_handle.emit("chat_token", ChatMessagePart { content: content.to_string() });
                                                        }
                                                        if let Some(done) = json.get("done").and_then(|d| d.as_bool()) {
                                                            if done {
                                                                let _ = app_handle.emit("chat_token_end", "Stream ended");
                                                                return;
                                                            }
                                                        }
                                                    }
                                                }
                                            }
                                        }
                                        Err(e) => {
                                            let _ = app_handle.emit("chat_error", format!("Stream error: {}", e));
                                            break;
                                        }
                                    }
                                }
                            } else {
                                let error_msg = format!("Server error: {}", response.status());
                                println!("❌ [{}ms] {}", start_time.elapsed().as_millis(), &error_msg);
                                let _ = app_handle.emit("chat_error", error_msg);
                            }
                        }
                        Err(e) => {
                            let error_msg = format!("Request failed: {}", e);
                            println!("❌ [{}ms] {}", start_time.elapsed().as_millis(), &error_msg);
                            let _ = app_handle.emit("chat_error", error_msg);
                        }
                    }
                }
            }
            Err(e) => {
                let error_msg = format!("Request failed: {}", e);
                println!("❌ [{}ms] {}", start_time.elapsed().as_millis(), &error_msg);
                let _ = app_handle.emit("chat_error", error_msg);
            }
        }
    });

    Ok(())
}
