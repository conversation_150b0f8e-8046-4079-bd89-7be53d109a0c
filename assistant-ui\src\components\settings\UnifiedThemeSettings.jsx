import React, { useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from '../ui/card';
import { Button } from '../ui/button';
import { Switch } from '../ui/switch';
import { motion } from 'framer-motion';

// Color scheme preview component
const ColorSchemeCard = ({ scheme, isSelected, onClick }) => (
  <motion.div
    whileHover={{ scale: 1.02 }}
    whileTap={{ scale: 0.98 }}
    className={`relative p-4 rounded-lg border-2 cursor-pointer transition-all ${
      isSelected 
        ? 'border-primary bg-primary/5' 
        : 'border-border hover:border-primary/50'
    }`}
    onClick={onClick}
  >
    <div className="flex items-center justify-between mb-3">
      <h3 className="font-medium">{scheme.name}</h3>
      {isSelected && (
        <div className="w-2 h-2 bg-primary rounded-full"></div>
      )}
    </div>
    
    {/* Color preview */}
    <div className="flex space-x-1 mb-2">
      {Object.entries(scheme.colors).map(([key, color]) => (
        <div
          key={key}
          className="w-6 h-6 rounded border"
          style={{ backgroundColor: color }}
          title={`${key}: ${color}`}
        />
      ))}
    </div>
    
    <p className="text-xs text-muted-foreground">
      {Object.keys(scheme.colors).length} colors
    </p>
  </motion.div>
);

// Theme editor modal
const ThemeEditor = ({ scheme, isOpen, onClose, onSave }) => {
  const [editedScheme, setEditedScheme] = useState(scheme || {
    id: `custom_${Date.now()}`,
    name: 'New Theme',
    colors: {
      background: '#0F1115',
      surface: '#1C1F26',
      primary: '#8E24AA',
      accent: '#FF7043',
      text: '#CFD8DC',
    }
  });

  const handleColorChange = (colorKey, value) => {
    setEditedScheme(prev => ({
      ...prev,
      colors: {
        ...prev.colors,
        [colorKey]: value
      }
    }));
  };

  const handleSave = () => {
    onSave(editedScheme);
    onClose();
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
      <div className="bg-card border rounded-lg p-6 w-full max-w-md">
        <h2 className="text-lg font-semibold mb-4">
          {scheme ? 'Edit Theme' : 'Create Theme'}
        </h2>
        
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">Theme Name</label>
            <input
              type="text"
              value={editedScheme.name}
              onChange={(e) => setEditedScheme(prev => ({ ...prev, name: e.target.value }))}
              className="w-full px-3 py-2 border rounded-md bg-background"
            />
          </div>
          
          {Object.entries(editedScheme.colors).map(([key, value]) => (
            <div key={key}>
              <label className="block text-sm font-medium mb-1 capitalize">
                {key} Color
              </label>
              <div className="flex space-x-2">
                <input
                  type="color"
                  value={value}
                  onChange={(e) => handleColorChange(key, e.target.value)}
                  className="w-12 h-10 border rounded"
                />
                <input
                  type="text"
                  value={value}
                  onChange={(e) => handleColorChange(key, e.target.value)}
                  className="flex-1 px-3 py-2 border rounded-md bg-background font-mono text-sm"
                />
              </div>
            </div>
          ))}
        </div>
        
        <div className="flex justify-end space-x-2 mt-6">
          <Button variant="outline" onClick={onClose}>Cancel</Button>
          <Button onClick={handleSave}>Save Theme</Button>
        </div>
      </div>
    </div>
  );
};

export default function UnifiedThemeSettings() {
  const {
    isDarkMode,
    toggleDarkMode,
    colorScheme,
    selectColorScheme,
    availableColorSchemes,
    addColorScheme,
    updateColorScheme,
    deleteColorScheme
  } = useTheme();

  const [editorOpen, setEditorOpen] = useState(false);
  const [editingScheme, setEditingScheme] = useState(null);

  const handleCreateTheme = () => {
    setEditingScheme(null);
    setEditorOpen(true);
  };

  const handleEditTheme = (scheme) => {
    setEditingScheme(scheme);
    setEditorOpen(true);
  };

  const handleSaveTheme = (scheme) => {
    if (editingScheme) {
      updateColorScheme(scheme.id, scheme);
    } else {
      addColorScheme(scheme);
    }
    
    // Apply the theme immediately
    selectColorScheme(scheme.id);
  };

  const handleDeleteTheme = (schemeId) => {
    if (confirm('Are you sure you want to delete this theme?')) {
      deleteColorScheme(schemeId);
    }
  };

  return (
    <div className="space-y-6">
      {/* Dark Mode Toggle */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Dark Mode</h3>
              <p className="text-sm text-muted-foreground">
                {isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled'}
              </p>
            </div>
            <Switch
              checked={isDarkMode}
              onCheckedChange={toggleDarkMode}
            />
          </div>
        </CardContent>
      </Card>

      {/* Current Theme Display */}
      <Card>
        <CardHeader>
          <CardTitle>Current Theme</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="p-4 border rounded-lg bg-card">
            <div className="flex items-center justify-between mb-3">
              <h3 className="font-medium">{colorScheme?.name || 'Unknown Theme'}</h3>
              <Button 
                size="sm" 
                variant="outline"
                onClick={() => handleEditTheme(colorScheme)}
              >
                Edit
              </Button>
            </div>
            
            {/* Current theme colors */}
            <div className="flex space-x-2 mb-3">
              {colorScheme?.colors && Object.entries(colorScheme.colors).map(([key, color]) => (
                <div
                  key={key}
                  className="w-8 h-8 rounded border"
                  style={{ backgroundColor: color }}
                  title={`${key}: ${color}`}
                />
              ))}
            </div>
            
            <p className="text-sm text-muted-foreground">
              This theme is currently applied to your application
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Available Themes */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <CardTitle>Available Themes</CardTitle>
            <Button onClick={handleCreateTheme}>Create New Theme</Button>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {availableColorSchemes.map((scheme) => (
              <div key={scheme.id} className="relative">
                <ColorSchemeCard
                  scheme={scheme}
                  isSelected={colorScheme?.id === scheme.id}
                  onClick={() => selectColorScheme(scheme.id)}
                />
                
                {/* Delete button for custom themes */}
                {!['voidCircuit', 'midnight', 'cyberpunk', 'forest', 'ocean'].includes(scheme.id) && (
                  <Button
                    size="sm"
                    variant="destructive"
                    className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleDeleteTheme(scheme.id);
                    }}
                  >
                    ×
                  </Button>
                )}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Theme Editor Modal */}
      <ThemeEditor
        scheme={editingScheme}
        isOpen={editorOpen}
        onClose={() => setEditorOpen(false)}
        onSave={handleSaveTheme}
      />
    </div>
  );
}
