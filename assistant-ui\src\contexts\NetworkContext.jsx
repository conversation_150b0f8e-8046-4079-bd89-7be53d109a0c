import React, { createContext, useContext, useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/tauri';

const NetworkContext = createContext();

export const NetworkProvider = ({ children }) => {
  const [networkInterfaces, setNetworkInterfaces] = useState([]);
  const [activeInterface, setActiveInterface] = useState(null);
  const [networkStats, setNetworkStats] = useState({
    uploadSpeed: 0,
    downloadSpeed: 0,
    packetsSent: 0,
    packetsReceived: 0,
    totalUpload: 0,
    totalDownload: 0,
  });
  const [isScanning, setIsScanning] = useState(false);
  const [devices, setDevices] = useState([]);
  const [error, setError] = useState(null);

  // Load network interfaces on mount
  useEffect(() => {
    const loadNetworkInfo = async () => {
      try {
        const interfaces = await invoke('get_network_interfaces');
        setNetworkInterfaces(interfaces);
        if (interfaces.length > 0) {
          setActiveInterface(interfaces[0]);
        }
      } catch (err) {
        console.error('Failed to load network interfaces:', err);
        setError('Failed to load network information');
      }
    };

    loadNetworkInfo();
  }, []);

  // Update network stats periodically
  useEffect(() => {
    if (!activeInterface) return;

    const updateStats = async () => {
      try {
        const stats = await invoke('get_network_stats', {
          interfaceName: activeInterface.name,
        });
        setNetworkStats(prev => ({
          ...prev,
          ...stats,
        }));
      } catch (err) {
        console.error('Failed to update network stats:', err);
      }
    };

    const interval = setInterval(updateStats, 1000);
    return () => clearInterval(interval);
  }, [activeInterface]);

  const scanNetwork = async () => {
    if (!activeInterface) return;
    
    setIsScanning(true);
    setError(null);
    
    try {
      const discoveredDevices = await invoke('scan_network_devices', {
        interfaceName: activeInterface.name,
      });
      setDevices(discoveredDevices);
    } catch (err) {
      console.error('Network scan failed:', err);
      setError('Failed to scan network. Please check your connection.');
    } finally {
      setIsScanning(false);
    }
  };

  const value = {
    networkInterfaces,
    activeInterface,
    setActiveInterface,
    networkStats,
    isScanning,
    devices,
    error,
    scanNetwork,
  };

  return (
    <NetworkContext.Provider value={value}>
      {children}
    </NetworkContext.Provider>
  );
};

export const useNetwork = () => {
  const context = useContext(NetworkContext);
  if (!context) {
    throw new Error('useNetwork must be used within a NetworkProvider');
  }
  return context;
};
