import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App.jsx';
import reportWebVitals from './reportWebVitals';

console.log('index.jsx loaded');

// Enhanced global error handler for DOM manipulation errors
window.addEventListener('error', (event) => {
  const errorMessage = event.error?.message || '';
  const errorName = event.error?.name || '';

  // Handle various DOM manipulation errors including React internal errors
  if (errorMessage.includes('removeChild') ||
      errorMessage.includes('insertBefore') ||
      errorMessage.includes('appendChild') ||
      errorMessage.includes('removeChildFromContainer') ||
      errorMessage.includes('commitDeletionEffectsOnFiber') ||
      errorName === 'NotFoundError' ||
      errorMessage.includes('The node to be removed is not a child')) {
    console.warn('DOM manipulation error caught and suppressed:', {
      message: errorMessage,
      name: errorName,
      stack: event.error?.stack,
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      timestamp: new Date().toISOString(),
      source: 'React DOM manipulation'
    });

    // Log additional context for debugging
    console.warn('This error was likely caused by React fiber reconciliation or portal cleanup during HMR. This is now handled by our stable portal system.');

    event.preventDefault(); // Prevent the error from crashing the app
    return false;
  }
});

// Enhanced global unhandled promise rejection handler
window.addEventListener('unhandledrejection', (event) => {
  const reason = event.reason;
  const reasonMessage = reason?.message || reason?.toString() || '';
  const reasonName = reason?.name || '';

  if (reasonMessage.includes('removeChild') ||
      reasonMessage.includes('insertBefore') ||
      reasonMessage.includes('appendChild') ||
      reasonMessage.includes('removeChildFromContainer') ||
      reasonMessage.includes('commitDeletionEffectsOnFiber') ||
      reasonName === 'NotFoundError' ||
      reasonMessage.includes('The node to be removed is not a child')) {
    console.warn('DOM manipulation promise rejection caught and suppressed:', {
      message: reasonMessage,
      name: reasonName,
      stack: reason?.stack,
      timestamp: new Date().toISOString(),
      source: 'React DOM manipulation'
    });

    // Log additional context for debugging
    console.warn('This promise rejection was likely caused by React fiber reconciliation or portal cleanup during HMR. This is now handled by our stable portal system.');

    event.preventDefault();
    return false;
  }
});

try {
  const rootElement = document.getElementById('root');
  if (!rootElement) {
    throw new Error('Root element not found');
  }

  const root = ReactDOM.createRoot(rootElement);
  console.log('React root created');

  // Temporarily disable StrictMode to prevent double-rendering issues
  // that can cause DOM manipulation errors
  root.render(
    // <React.StrictMode>
      <App />
    // </React.StrictMode>
  );
  console.log('App rendered');
} catch (error) {
  console.error('Error in index.jsx:', error);
  const rootElement = document.getElementById('root');
  if (rootElement) {
    rootElement.innerHTML = '<div class="loading-fallback"><h2>React Error</h2><p>' + error.message + '</p></div>';
  }
}

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();

// Add HMR-specific cleanup for development
if (process.env.NODE_ENV === 'development' && import.meta.hot) {
  import.meta.hot.dispose(() => {
    // Clean up any remaining portal containers before HMR reload
    const portalRoot = document.getElementById('portal-root');
    if (portalRoot) {
      // Clear content but don't remove the container
      portalRoot.innerHTML = '';
      console.log('HMR: Cleaned portal container for reload');
    }
  });
}