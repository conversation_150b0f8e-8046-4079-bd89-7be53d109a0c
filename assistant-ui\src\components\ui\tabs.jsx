import React from 'react';

const Tabs = ({ defaultValue, value, onValueChange, className, children, ...props }) => {
  // Use controlled state if value is provided, otherwise use internal state
  const [internalActiveTab, setInternalActiveTab] = React.useState(defaultValue || '');
  const activeTab = value !== undefined ? value : internalActiveTab;

  const handleTabChange = (newValue) => {
    if (value === undefined) {
      setInternalActiveTab(newValue);
    }
    if (onValueChange) {
      onValueChange(newValue);
    }
  };

  // Pass the activeTab state and handler to children
  const childrenWithProps = React.Children.map(children, child => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { activeTab, onValueChange: handleTabChange });
    }
    return child;
  });

  return (
    <div className={className} {...props}>
      {childrenWithProps}
    </div>
  );
};

const TabsList = ({ className, children, activeTab, onValueChange, ...props }) => {
  // Propagate activeTab and onValueChange to direct children (e.g., TabsTrigger)
  const childrenWithProps = React.Children.map(children, (child) => {
    if (React.isValidElement(child)) {
      return React.cloneElement(child, { activeTab, onValueChange });
    }
    return child;
  });

  return (
    <div
      className={`inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground ${className || ''}`}
      {...props}
    >
      {childrenWithProps}
    </div>
  );
};

const TabsTrigger = ({ value, activeTab, onValueChange, className, children, ...props }) => {
  const isActive = activeTab === value;
  
  return (
    <button
      type="button"
      className={`inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm ${
        isActive 
          ? 'bg-background text-foreground shadow-sm' 
          : 'hover:bg-gray-100 dark:hover:bg-gray-800'
      } ${className || ''}`}
      onClick={() => onValueChange && onValueChange(value)}
      {...props}
    >
      {children}
    </button>
  );
};

const TabsContent = ({ value, activeTab, className, children, ...props }) => {
  if (activeTab !== value) return null;
  
  return (
    <div
      className={`mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 ${className || ''}`}
      {...props}
    >
      {children}
    </div>
  );
};

export { Tabs, TabsList, TabsTrigger, TabsContent };