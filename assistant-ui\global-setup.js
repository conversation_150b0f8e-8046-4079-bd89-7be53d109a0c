const { spawn } = require('child_process');

module.exports = async config => {
  console.log('global-setup.js is running!');
  // Start the tauri-driver process
  console.log(`Spawning tauri-driver with command: tauri-driver ${['--port', '4446', '--native-port', '4444'].join(' ')}`);
  const tauriDriver = spawn(
    'tauri-driver',
    ['--port', '4446', '--native-port', '4444'],
    {
      stdio: ['pipe', 'pipe', 'pipe'],
    }
  );

  // Store the process reference globally
  global.__TAURI_DRIVER__ = tauriDriver;

  // Wait for tauri-driver to start and log its output
  tauriDriver.stderr.on('data', (data) => {
    console.log(`tauri-driver stderr: ${data}`);
  });

  tauriDriver.stdout.on('data', (data) => {
    console.log(`tauri-driver stdout: ${data}`);
  });

  // Give tauri-driver some time to start up
  await new Promise(resolve => setTimeout(resolve, 10000));
};