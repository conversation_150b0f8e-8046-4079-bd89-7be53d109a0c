import React from 'react';

const Slider = React.forwardRef(({ className, min = 0, max = 100, step = 1, value, onValueChange, ...props }, ref) => {
  const handleChange = (e) => {
    if (onValueChange) {
      onValueChange([parseFloat(e.target.value)]);
    }
  };

  const displayValue = Array.isArray(value) ? value[0] : value || min;

  return (
    <div className={`flex items-center ${className || ''}`} {...props}>
      <input
        ref={ref}
        type="range"
        min={min}
        max={max}
        step={step}
        value={displayValue}
        onChange={handleChange}
        className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
      />
      <span className="ml-3 text-sm text-gray-500 dark:text-gray-400">
        {displayValue}
      </span>
    </div>
  );
});

export { Slider };