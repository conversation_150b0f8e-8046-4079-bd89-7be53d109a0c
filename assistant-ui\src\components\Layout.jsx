import React, { useState, useEffect } from 'react';
import { useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faRobot, faBroadcastTower } from '@fortawesome/free-solid-svg-icons';
import TabSystem from './TabSystem.jsx';
import StatusBar from './StatusBar.jsx';
import PluginLoader from './PluginLoader.jsx';

const Layout = ({ children }) => {
  const location = useLocation();
  const isChatPage = location.pathname === '/';
  const [showCastPopup, setShowCastPopup] = useState(false);
  const [nearbyDevices] = useState([
    { id: 1, name: 'Living Room TV', type: 'Chromecast' },
    { id: 2, name: 'Bedroom Speaker', type: 'AirPlay' },
    { id: 3, name: 'Office Display', type: 'Miracast' },
    { id: 4, name: 'Kitchen Tablet', type: 'DLNA' }
  ]);

  useEffect(() => {
    console.log("Layout component rendered");
  }, []);

  return (
    <div className="flex flex-col bg-background text-foreground min-h-screen">
      {/* Header */}
      <header className="flex items-center justify-between px-2 py-1 bg-slate-100 dark:bg-slate-900 border-b border-slate-200 dark:border-slate-700 h-6">
        <div className="flex items-center space-x-1">
          <div className="bg-gradient-to-r from-blue-600 to-purple-700 p-1 rounded-sm">
            <FontAwesomeIcon icon={faRobot} className="h-3 w-3 text-white" />
          </div>
          <div className="text-xs font-medium text-slate-700 dark:text-slate-300">The Collective</div>
        </div>
        
        <div className="flex items-center space-x-2">
          {/* Cast Button */}
          <div className="relative">
            <button 
              onClick={() => setShowCastPopup(!showCastPopup)}
              className="flex items-center text-xs text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
            >
              <FontAwesomeIcon icon={faBroadcastTower} className="h-3 w-3 mr-1" />
              Cast
            </button>
            
            {showCastPopup && (
              <div className="absolute right-0 mt-1 w-64 bg-white dark:bg-slate-800 rounded-md shadow-lg border border-slate-200 dark:border-slate-700 z-50">
                <div className="p-2 border-b border-slate-200 dark:border-slate-700">
                  <h3 className="text-sm font-medium text-slate-900 dark:text-slate-100">Cast to Device</h3>
                </div>
                <div className="max-h-60 overflow-y-auto">
                  {nearbyDevices.length > 0 ? (
                    nearbyDevices.map((device) => (
                      <button
                        key={device.id}
                        className="flex items-center w-full px-4 py-2 text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-100 dark:hover:bg-slate-700"
                      >
                        <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                        <div>
                          <div className="font-medium">{device.name}</div>
                          <div className="text-xs text-slate-500 dark:text-slate-400">{device.type}</div>
                        </div>
                      </button>
                    ))
                  ) : (
                    <div className="px-4 py-2 text-sm text-slate-500 dark:text-slate-400">
                      No devices found
                    </div>
                  )}
                </div>
                <div className="p-2 border-t border-slate-200 dark:border-slate-700">
                  <button className="w-full text-left px-2 py-1 text-xs text-slate-500 dark:text-slate-400 hover:bg-slate-100 dark:hover:bg-slate-700 rounded">
                    Add device
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </header>

      {/* Tab System */}
      <div className="flex-1 flex flex-col">
        <TabSystem>
          {children}
        </TabSystem>
      </div>

      {/* VS Code Style Status Bar - Always at bottom */}
      <StatusBar />
    </div>
  );
};

export default Layout;