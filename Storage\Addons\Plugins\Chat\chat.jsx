import React, { useState, useEffect, useRef, useCallback, useMemo } from "react";
import { colors, typography, spacing } from "./components/design-system";
import { useContext } from "react";
import { ChatContext } from "./contexts/ChatProvider.jsx";
import ConversationSidebar from "./components/ConversationSidebar";
import MessageBubble from "./components/MessageBubble";

// Mock data for the enhanced chat system
const mockConversations = [
  {
    id: 'conv_1',
    title: 'Code Review Discussion',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:30:00Z',
    model: 'gpt-4'
  },
  {
    id: 'conv_2', 
    title: 'Creative Writing Help',
    created_at: '2024-01-14T15:20:00Z',
    updated_at: '2024-01-14T16:45:00Z',
    model: 'claude-3'
  },
  {
    id: 'conv_3',
    title: 'Technical Documentation',
    created_at: '2024-01-13T09:15:00Z', 
    updated_at: '2024-01-13T11:30:00Z',
    model: 'gpt-4'
  }
];

const mockMessages = [
  {
    id: 'msg_1',
    content: 'Hello! I need help reviewing this React component. Can you take a look?',
    sender: 'user',
    timestamp: '2024-01-15T10:00:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1'
  },
  {
    id: 'msg_2',
    content: `I'd be happy to help you review your React component! Please share the code you'd like me to look at.

Here are some things I typically look for in React components:

- **Component structure** and organization
- **Props validation** and TypeScript usage
- **Performance optimizations** (useMemo, useCallback, etc.)
- **Accessibility** considerations
- **Code readability** and maintainability

Please paste your component code, and I'll provide detailed feedback!`,
    sender: 'assistant',
    timestamp: '2024-01-15T10:01:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1'
  }
];

const mockModels = [
  'gpt-4',
  'gpt-3.5-turbo',
  'claude-3-opus',
  'claude-3-sonnet',
  'llama-2-70b',
  'mistral-7b'
];

// Use shared ChatContext to avoid circular imports

// Simple Button Component
const Button = ({ children, onClick, disabled, className = '', variant = 'default', size = 'default', type = 'button', title }) => {
  const baseClasses = 'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background';
  
  const variantClasses = {
    default: 'bg-primary text-primary-foreground hover:bg-primary/90',
    outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
    ghost: 'hover:bg-accent hover:text-accent-foreground',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80'
  };
  
  const sizeClasses = {
    default: 'h-10 py-2 px-4',
    sm: 'h-9 px-3 rounded-md',
    lg: 'h-11 px-8 rounded-md',
    icon: 'h-10 w-10'
  };

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled}
      title={title}
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
    >
      {children}
    </button>
  );
};

// Simple Input Component
const Input = ({ value, onChange, placeholder, disabled, className = '', onKeyPress }) => {
  return (
    <input
      type="text"
      value={value}
      onChange={onChange}
      onKeyPress={onKeyPress}
      placeholder={placeholder}
      disabled={disabled}
      className={`flex h-10 w-full rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`}
    />
  );
};

// Simple Select Component
const Select = ({ value, onValueChange, disabled, children }) => {
  const [isOpen, setIsOpen] = useState(false);
  
  return (
    <div className="relative">
      <button
        onClick={() => !disabled && setIsOpen(!isOpen)}
        disabled={disabled}
        className="flex h-10 w-full items-center justify-between rounded-md border border-input bg-transparent px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
      >
        <span>{value || 'Select...'}</span>
        <svg className="h-4 w-4 opacity-50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>
      
      {isOpen && (
        <div className="absolute top-full left-0 right-0 z-50 mt-1 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-md shadow-lg">
          {React.Children.map(children, child => 
            React.cloneElement(child, {
              onClick: (val) => {
                onValueChange(val);
                setIsOpen(false);
              }
            })
          )}
        </div>
      )}
      
      {isOpen && (
        <div className="fixed inset-0 z-40" onClick={() => setIsOpen(false)} />
      )}
    </div>
  );
};

const SelectItem = ({ value, children, onClick }) => (
  <div
    onClick={() => onClick(value)}
    className="cursor-pointer px-3 py-2 text-sm hover:bg-gray-100 dark:hover:bg-gray-700"
  >
    {children}
  </div>
);

// Chat Provider Component
const ChatProvider = ({ children }) => {
  const [conversations, setConversations] = useState(mockConversations);
  const [activeConversationId, setActiveConversationId] = useState(mockConversations[0]?.id);
  const [messages, setMessages] = useState(mockMessages.filter(m => m.conversationId === mockConversations[0]?.id));
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [availableModels, setAvailableModels] = useState(mockModels);
  const [selectedModel, setSelectedModel] = useState(mockModels[0]);
  const [isLoadingModels, setIsLoadingModels] = useState(false);
  const [isPreloading, setIsPreloading] = useState(false);
  const [showSidebar, setShowSidebar] = useState(true);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef(null);

  // Auto-scroll to bottom
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (!input.trim() || isLoading) return;

    const userMessage = {
      id: `msg_${Date.now()}`,
      content: input.trim(),
      sender: 'user',
      timestamp: new Date().toISOString(),
      model_used: selectedModel,
      conversationId: activeConversationId
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiMessage = {
        id: `msg_${Date.now() + 1}`,
        content: `Thank you for your message! I'm the **Enhanced Chat v2.0.0** system with all the modern features:

🤖 **AI Features:**
- Multiple model selection (${selectedModel})
- Real-time streaming responses
- Context-aware conversations

💬 **Chat Features:**
- Beautiful message bubbles with gradients
- Conversation management and history
- Message search and filtering
- File upload and voice recording support

🎨 **UI Features:**
- Modern responsive design
- Dark/light theme support
- Focus mode for distraction-free chatting
- Smooth animations and transitions

⚙️ **Advanced Features:**
- Comprehensive settings panel
- Export conversations (Markdown/JSON)
- Message actions (copy, regenerate, bookmark)
- Keyboard shortcuts and accessibility

Try exploring the sidebar, settings, or ask me anything else!`,
        sender: 'assistant',
        timestamp: new Date().toISOString(),
        model_used: selectedModel,
        conversationId: activeConversationId
      };
      
      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1500);
  };

  const switchConversation = (convId) => {
    setActiveConversationId(convId);
    setMessages(mockMessages.filter(m => m.conversationId === convId));
  };

  const createNewConversation = () => {
    const newConv = {
      id: `conv_${Date.now()}`,
      title: 'New Chat',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      model: selectedModel
    };
    setConversations(prev => [newConv, ...prev]);
    setActiveConversationId(newConv.id);
    setMessages([]);
  };

  const value = {
    conversations,
    activeConversationId,
    messages,
    input,
    setInput,
    isLoading,
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    showSidebar,
    setShowSidebar,
    showSettings,
    setShowSettings,
    messagesEndRef,
    handleSubmit,
    switchConversation,
    createNewConversation
  };

  return (
    <ChatContext.Provider value={value}>
      {children}
    </ChatContext.Provider>
  );
};

// Conversation Sidebar Component





// Settings Panel Component
const SettingsPanel = () => {
  const { setShowSettings } = useContext(ChatContext);
  
  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowSettings(false)}
              className="h-8 w-8 p-0"
            >
              <span>←</span>
            </Button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Chat Settings
            </h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-2xl space-y-8">
          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Chat Behavior
            </h3>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Auto-welcome message
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Show a welcome message when starting new conversations
                  </p>
                </div>
                <input
                  type="checkbox"
                  defaultChecked
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Enable streaming responses
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Show responses as they are generated in real-time
                  </p>
                </div>
                <input
                  type="checkbox"
                  defaultChecked
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
              </div>

              <div className="flex items-center justify-between">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    Show model information
                  </label>
                  <p className="text-xs text-gray-500 dark:text-gray-400">
                    Display current model and conversation stats in header
                  </p>
                </div>
                <input
                  type="checkbox"
                  defaultChecked
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              AI Settings
            </h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Temperature: 0.7
                </label>
                <input
                  type="range"
                  min="0"
                  max="2"
                  step="0.1"
                  defaultValue="0.7"
                  className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
                />
                <div className="flex justify-between text-xs text-gray-500 mt-1">
                  <span>Focused</span>
                  <span>Balanced</span>
                  <span>Creative</span>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Max Tokens
                </label>
                <Input
                  type="number"
                  min="1"
                  max="8192"
                  defaultValue="2048"
                  className="w-full"
                />
              </div>
            </div>
          </div>

          <div>
            <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Export & Data
            </h3>
            <div className="space-y-4">
              <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                  Export Conversations
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  Download your conversations in your preferred format
                </p>
                <div className="flex gap-2">
                  <Button size="sm" variant="outline">
                    <span className="mr-2">⬇️</span>
                    Markdown
                  </Button>
                  <Button size="sm" variant="outline">
                    <span className="mr-2">⬇️</span>
                    JSON
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main Chat Component
const Chat = ({ addTab }) => {
  const {
    conversations,
    activeConversationId,
    messages,
    input,
    setInput,
    isLoading,
    availableModels,
    selectedModel,
    setSelectedModel,
    isLoadingModels,
    isPreloading,
    showSidebar,
    setShowSidebar,
    showSettings,
    setShowSettings,
    messagesEndRef,
    handleSubmit
  } = useContext(ChatContext);

  const [showQuickActions, setShowQuickActions] = useState(false);

  // Suggested prompts for welcome screen
  const suggestedPrompts = [
    {
      icon: '💡',
      title: "Help me write a professional email",
      description: "Draft formal communication"
    },
    {
      icon: '💻',
      title: "Explain quantum computing in simple terms",
      description: "Break down complex concepts"
    },
    {
      icon: '📄',
      title: "Create a meal plan for this week",
      description: "Healthy meal suggestions"
    },
    {
      icon: '🔍',
      title: "Debug this code snippet",
      description: "Find and fix coding issues"
    },
    {
      icon: '🤖',
      title: "Brainstorm creative project ideas",
      description: "Generate innovative concepts"
    }
  ];

  const handleSuggestedPrompt = (prompt) => {
    setInput(prompt.title);
  };

  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const renderHeader = () => {
    const activeConversation = conversations?.find(c => c.id === activeConversationId);
    
    return (
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-3 flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSidebar(!showSidebar)}
            className="h-8 w-8 p-0"
          >
            <span>☰</span>
          </Button>
          
          <div className="flex items-center gap-2">
            <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm">🤖</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                {activeConversation?.title || 'Enhanced Chat v2.0.0'}
              </h1>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                {selectedModel} • {messages?.length || 0} messages
              </p>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 w-8 p-0"
          >
            <span>🔍</span>
          </Button>

          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowQuickActions(!showQuickActions)}
              className="h-8 w-8 p-0"
            >
              <span>⋮</span>
            </Button>
            
            {showQuickActions && (
              <div className="absolute right-0 top-full mt-1 w-48 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg z-50">
                <div className="p-1">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start"
                  >
                    <span className="mr-2">⬇️</span>
                    Export as Markdown
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => setShowSettings(true)}
                    className="w-full justify-start"
                  >
                    <span className="mr-2">⚙️</span>
                    Settings
                  </Button>
                  <div className="border-t border-gray-200 dark:border-gray-700 my-1"></div>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full justify-start text-red-600 hover:text-red-700"
                  >
                    <span className="mr-2">🗑️</span>
                    Delete Chat
                  </Button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  const renderWelcomeScreen = () => (
    <div className="flex-1 flex flex-col items-center justify-center px-6 py-12">
      <div className="text-center mb-12">
        <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-xl">
          <span className="text-4xl">🤖</span>
        </div>
        <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          Welcome to Enhanced Chat v2.0.0
        </h2>
        <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          Your AI-powered assistant with advanced features. Start a conversation or choose from these suggestions.
        </p>
      </div>

      <div className="w-full max-w-4xl">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
          {suggestedPrompts.map((prompt, index) => (
            <button
              key={index}
              onClick={() => handleSuggestedPrompt(prompt)}
              className="group p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg transition-all duration-200 text-left"
            >
              <div className="flex items-start gap-4">
                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                  <span className="text-lg">{prompt.icon}</span>
                </div>
                <div className="flex-1">
                  <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                    {prompt.title}
                  </h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {prompt.description}
                  </p>
                </div>
              </div>
            </button>
          ))}
        </div>

        <div className="text-center">
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
            Powered by {selectedModel || 'AI'} • Ready to assist you
          </p>
          <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
            <span>Press</span>
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-gray-600 dark:text-gray-300">Enter</kbd>
            <span>to send •</span>
            <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-gray-600 dark:text-gray-300">Shift + Enter</kbd>
            <span>for new line</span>
          </div>
        </div>
      </div>
    </div>
  );

  const renderMessages = () => (
    <div className="flex-1 overflow-hidden">
      <div className="h-full overflow-y-auto px-4 py-4 space-y-4">
        {messages?.length === 0 && isPreloading && (
          <div className="flex flex-col items-center justify-center h-full">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"></div>
            <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
              Warming up the model...
            </h3>
            <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
              This may take a moment while we prepare {selectedModel} for you.
            </p>
          </div>
        )}

        {messages?.length === 0 && !isPreloading && renderWelcomeScreen()}

        {messages?.map((message, index) => (
          <MessageBubble key={message.id} message={message} />
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="max-w-[80%] rounded-2xl p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-3">
                <div className="w-8 h-8 rounded-full bg-gradient-to-r from-purple-500 to-blue-500 flex items-center justify-center">
                  <span className="text-sm">🤖</span>
                </div>
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
                  <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
                </div>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} className="h-px" />
      </div>
    </div>
  );

  if (showSettings) {
    return <SettingsPanel />;
  }

  return (
    <div className="h-full flex bg-gray-50 dark:bg-gray-900">
      {/* Conversation Sidebar */}
      <ConversationSidebar />

      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        {renderHeader()}

        {/* Messages */}
        {renderMessages()}

        {/* Enhanced Input Area */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          {/* Model Selector */}
          <div className="flex items-center gap-2 mb-4">
            <div className="flex-1">
              <Select 
                value={selectedModel || ''} 
                onValueChange={setSelectedModel}
                disabled={isLoadingModels || isLoading || isPreloading}
              >
                <div className="flex items-center gap-2">
                  <span>🤖</span>
                  <span>{selectedModel || 'Select AI Model'}</span>
                  {isPreloading && <span className="animate-spin">⟳</span>}
                </div>
                {availableModels?.map((model) => (
                  <SelectItem key={model} value={model}>
                    <div className="flex items-center justify-between w-full">
                      <span>{model}</span>
                      {model === selectedModel && !isPreloading && (
                        <span className="text-green-500 text-xs ml-2">Ready</span>
                      )}
                    </div>
                  </SelectItem>
                ))}
              </Select>
            </div>
            
            <Button 
              variant="outline" 
              size="icon" 
              disabled={isLoadingModels || isLoading || isPreloading}
              title="Refresh models"
            >
              <span className={isLoadingModels ? 'animate-spin' : ''}>🔄</span>
            </Button>
          </div>

          {/* Message Input */}
          <form onSubmit={handleSubmit}>
            <div className="flex items-end gap-3">
              {/* File Upload Button */}
              <Button
                type="button"
                variant="outline"
                size="icon"
                disabled={isLoading || isPreloading}
                className="h-12 w-12 rounded-xl flex-shrink-0"
                title="Attach files"
              >
                <span>📎</span>
              </Button>

              {/* Voice Recording Button */}
              <Button
                type="button"
                variant="outline"
                size="icon"
                disabled={isLoading || isPreloading}
                className="h-12 w-12 rounded-xl flex-shrink-0"
                title="Voice recording"
              >
                <span>🎤</span>
              </Button>

              {/* Message Input */}
              <div className="flex-1 relative">
                <Input
                  value={input || ''}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyPress={handleKeyPress}
                  placeholder="Type your message... (Enter to send, Shift+Enter for new line)"
                  disabled={isLoading || isPreloading || !selectedModel}
                  className="h-12 pr-12 rounded-xl border-2 focus:border-blue-500 transition-colors"
                />
              </div>

              {/* Send Button */}
              <Button
                type="submit"
                disabled={!input?.trim() || isLoading || isPreloading || !selectedModel}
                className="h-12 px-6 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex-shrink-0"
              >
                <span className="mr-2">
                  {isLoading ? <span className="animate-spin">⟳</span> : '✈️'}
                </span>
                {isLoading ? 'Sending...' : 'Send'}
              </Button>
            </div>
          </form>

          {/* Status Info */}
          <div className="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400">
            <div className="flex items-center gap-4">
              <span>Enhanced Chat v2.0.0</span>
              {selectedModel && <span>Model: {selectedModel}</span>}
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close quick actions */}
      {showQuickActions && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setShowQuickActions(false)}
        />
      )}
    </div>
  );
};

// Main Export - Self-contained Enhanced Chat System
const EnhancedChat = ({ addTab }) => {
  return (
    <div className="h-full w-full">
      <ChatProvider>
        <Chat addTab={addTab} />
      </ChatProvider>
    </div>
  );
};

export default EnhancedChat;