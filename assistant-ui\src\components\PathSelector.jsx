import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';

import { Button } from "./ui/button";
import { Label } from "./ui/label";
import { StandardInput } from "./ui/form-fields";

const PathSelector = ({
  id,
  label,
  description,
  fetchCommand,
  saveCommand,
  dialogType = 'openDirectory', // 'openDirectory' or 'openFile'
  placeholderText = 'Enter path',
  saveButtonText = 'Save Path',
  onPathSelected
}) => {
  const [currentPath, setCurrentPath] = useState('');
  const [newPath, setNewPath] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });

  const fetchData = async () => {
    if (!fetchCommand) return;
    setIsLoading(true);
    try {
      const pathValue = await invoke(fetchCommand);
      setCurrentPath(pathValue || '');
      setNewPath(pathValue || '');
      setMessage({ text: '', type: '' });
    } catch (error) {
      console.error(`Error fetching path with command ${fetchCommand}:`, error);
      setMessage({ text: `Error fetching current path. Command may not be implemented yet.`, type: 'error' });
      // Set empty values as fallback
      setCurrentPath('');
      setNewPath('');
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [fetchCommand]);

  const handleBrowse = async () => {
    try {
      const result = await invoke('dialog_open', { dialog_type: dialogType });
      if (result && !result.canceled && result.file_paths && result.file_paths.length > 0) {
        setNewPath(result.file_paths[0]);
        if (onPathSelected) {
            onPathSelected(result.file_paths[0]);
        }
        setMessage({ text: '', type: '' });
      }
    } catch (error) {
      console.error('Error opening dialog:', error);
      setMessage({ text: 'Failed to open browse dialog.', type: 'error' });
    }
  };

  const handleSave = async () => {
    if (!saveCommand) return;
    setIsLoading(true);
    setMessage({ text: '', type: '' });
    try {
      await invoke(saveCommand, { path: newPath });
      setCurrentPath(newPath);
      setMessage({ text: 'Path updated successfully. A restart may be needed for some changes to take effect.', type: 'success' });
    } catch (error) {
      console.error(`Error saving path with command ${saveCommand}:`, error);
      setMessage({ text: `Error saving path. Command may not be implemented yet.`, type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="space-y-3">
      <div>
        <Label htmlFor={id || 'pathInput'} className="text-sm font-medium">
          {label}
        </Label>
        {description && (
          <p className="text-xs text-muted-foreground mb-1">
            {description}
          </p>
        )}
        <div className="flex items-center space-x-2">
          <StandardInput 
            id={id || 'pathInput'}
            type="text" 
            value={newPath}
            onChange={(e) => setNewPath(e.target.value)}
            placeholder={placeholderText}
            className="flex-grow"
            disabled={isLoading}
            containerClassName="m-0 flex-grow"
          />
          <Button onClick={handleSave} disabled={isLoading}>
            {saveButtonText}
          </Button>
        </div>
      </div>
      {message.text && (
        <div className={`p-2 rounded-md text-xs ${message.type === 'success' ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'}`}>
          {message.text}
        </div>
      )}
    </div>
  );
};

export default PathSelector;