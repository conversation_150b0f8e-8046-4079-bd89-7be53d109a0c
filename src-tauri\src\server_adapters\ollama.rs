use async_trait::async_trait;
use reqwest::Client;
use serde_json;
use crate::settings_manager::ServerProfile;
use super::{ServerAdapter, AdapterError};

pub struct OllamaAdapter {
    client: Client,
    base_url: String,
}

impl OllamaAdapter {
    pub fn new(base_url: String) -> Self {
        Self {
            client: Client::new(),
            base_url,
        }
    }
}

#[async_trait]
impl ServerAdapter for OllamaAdapter {
    fn get_base_url(&self) -> String {
        self.base_url.clone()
    }
    
    async fn is_running(&self) -> bool {
        let url = format!("{}/api/tags", self.base_url);
        match self.client.get(&url).send().await {
            Ok(response) => response.status().is_success(),
            Err(_) => false,
        }
    }
    
    async fn get_models(&self) -> Result<Vec<String>, AdapterError> {
        let url = format!("{}/api/tags", self.base_url);
        let response = self.client.get(&url).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        let json: serde_json::Value = response.json().await?;
        let models: Vec<String> = json["models"].as_array()
            .unwrap_or(&vec![])
            .iter()
            .filter_map(|m| m["name"].as_str().map(|s| s.to_string()))
            .collect();
        
        Ok(models)
    }
    
    async fn send_message(&self, model: &str, prompt: &str) -> Result<reqwest::Response, AdapterError> {
        let url = format!("{}/api/generate", self.base_url);
        let body = serde_json::json!({
            "model": model,
            "prompt": prompt,
            "stream": true,
            "options": {
                "num_predict": 256,
                "temperature": 0.3,
                "top_p": 0.8,
                "top_k": 20,
                "repeat_penalty": 1.05
            }
        });
        
        let response = self.client.post(&url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        Ok(response)
    }
    
    async fn generate_welcome_message(&self, model: &str) -> Result<String, AdapterError> {
        let prompt = "As a friendly and helpful AI assistant, introduce yourself to the user in a single, welcoming sentence.";
        let url = format!("{}/api/generate", self.base_url);
        let body = serde_json::json!({
            "model": model,
            "prompt": prompt,
            "stream": false,
            "options": { "num_predict": 50 }
        });
        
        let response = self.client.post(&url).json(&body).send().await?;
        
        if !response.status().is_success() {
            return Err(AdapterError::Custom(format!("Server returned status: {}", response.status())));
        }
        
        let json: serde_json::Value = response.json().await?;
        let content = json.get("response")
            .and_then(|r| r.as_str())
            .unwrap_or("Hello! How can I help you today?")
            .trim()
            .to_string();
        
        Ok(content)
    }
    
    fn get_health_endpoint(&self) -> String {
        format!("{}/api/tags", self.base_url)
    }
    
    fn get_server_type(&self) -> &'static str {
        "ollama"
    }
    
    fn validate_profile(&self, profile: &ServerProfile) -> Result<(), AdapterError> {
        if profile.server_type.as_deref() != Some("ollama") {
            return Err(AdapterError::InvalidProfile);
        }
        
        // Check for ollama executable
        let has_ollama = profile.detected_executables.iter()
            .any(|exe| exe.file_name.to_lowercase().contains("ollama"));
        
        if !has_ollama {
            return Err(AdapterError::Custom(
                "No Ollama executable found in server profile".to_string()
            ));
        }
        
        Ok(())
    }
}