import React, { useContext, useEffect, useRef, useState } from 'react';
import { ChatContext } from '../contexts/ChatProvider';
import ModernChat from '../components/ModernChat';
import { FontAwesomeIcon } from '../components/icons/FontAwesome';
import { faPlus, faRobot } from '../components/icons/FontAwesome';

// ChatView Component
const ChatView = () => {
  const {
    messages,
    sendMessage,
    isLoading,
    // Add other necessary context values here
  } = useContext(ChatContext);

  const handleSendMessage = (message) => {
    sendMessage(message);
  };

  return (
    <div className="w-80 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col h-full">
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
            Conversations
          </h2>
          <Button
            onClick={onNewConversation}
            size="sm"
            className="h-8 w-8 p-0 bg-blue-600 hover:bg-blue-700"
          >
            <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
          </Button>
        </div>

        {/* Search */}
        <div className="relative">
          <FontAwesomeIcon
            icon={faSearch}
            className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
          />
          <Input
            type="text"
            placeholder="Search conversations..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10 h-9"
          />
        </div>
      </div>

      {/* Conversations List */}
      <div className="flex-1 overflow-y-auto p-2">
        {filteredConversations.length === 0 ? (
          <div className="p-4 text-center">
            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-3">
              <FontAwesomeIcon icon={faRobot} className="h-8 w-8 text-gray-400" />
            </div>
            <p className="text-gray-500 dark:text-gray-400 text-sm">
              {searchQuery ? 'No conversations found' : 'No conversations yet'}
            </p>
          </div>
        ) : (
          filteredConversations.map(conv => (
            <div
              key={conv.id}
              className={`group relative mb-1 rounded-lg transition-all duration-200 cursor-pointer ${
                conv.id === activeConversationId
                  ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
              }`}
              onClick={() => onSelectConversation(conv.id)}
            >
              <div className="p-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1 min-w-0">
                    <h3 className={`font-medium text-sm truncate ${
                      conv.id === activeConversationId
                        ? 'text-blue-900 dark:text-blue-100'
                        : 'text-gray-900 dark:text-gray-100'
                    }`}>
                      {conv.title}
                    </h3>
                    <div className="flex items-center gap-2 mt-2">
                      <span className="text-xs text-gray-400">
                        {formatTime(conv.updated_at)}
                      </span>
                      {conv.model && (
                        <>
                          <span className="text-xs text-gray-300">•</span>
                          <span className="text-xs text-gray-400">
                            {conv.model}
                          </span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
};

// MessageBubble Component
const MessageBubble = ({ message, onAction }) => {
  const [showActions, setShowActions] = useState(false);
  const isUser = message.sender === 'user';
  const isAI = message.sender === 'ai' || message.sender === 'assistant';
  const isTyping = message.isTyping;

  const renderMessageContent = () => {
    if (isTyping) {
      return (
        <div className="flex items-center space-x-2 py-1">
          <div className="flex space-x-1.5">
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
            <div className="w-2 h-2 bg-gray-400 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
          </div>
        </div>
      );
    }

    // Simple markdown-like formatting
    let content = message.content;
    
    // Bold text
    content = content.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
    
    // Code blocks
    content = content.replace(/```([\s\S]*?)```/g, '<pre class="bg-gray-100 dark:bg-gray-800 p-3 rounded-lg mt-2 mb-2 overflow-x-auto"><code>$1</code></pre>');
    
    // Inline code
    content = content.replace(/`([^`]+)`/g, '<code class="bg-gray-100 dark:bg-gray-800 px-1 py-0.5 rounded text-sm">$1</code>');
    
    // Line breaks
    content = content.replace(/\n/g, '<br>');

    return (
      <div 
        className="text-sm leading-relaxed whitespace-pre-wrap break-words"
        dangerouslySetInnerHTML={{ __html: content }}
      />
    );
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div
        className={`group max-w-[85%] rounded-2xl p-4 shadow-sm relative transition-all duration-200 ${
          isUser
            ? 'bg-gradient-to-r from-blue-500 to-blue-600 text-white ml-4'
            : isError
            ? 'bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300 border border-red-200 dark:border-red-800 mr-4'
            : isSystem
            ? 'bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-300 border border-yellow-200 dark:border-yellow-800 mr-4'
            : 'bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 mr-4 border border-gray-200 dark:border-gray-700'
        }`}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {/* Message bubble tail */}
        <div className={`absolute top-4 w-3 h-3 transform rotate-45 ${
          isUser
            ? 'bg-blue-500 -right-1'
            : isError
            ? 'bg-red-50 dark:bg-red-900/20 -left-1 border-l border-b border-red-200 dark:border-red-800'
            : isSystem
            ? 'bg-yellow-50 dark:bg-yellow-900/20 -left-1 border-l border-b border-yellow-200 dark:border-yellow-800'
            : 'bg-white dark:bg-gray-800 -left-1 border-l border-b border-gray-200 dark:border-gray-700'
        }`} />

        <div className="flex items-start gap-3">
          {/* Avatar */}
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            isUser
              ? 'bg-white/20'
              : isError
                ? 'bg-red-100 dark:bg-red-800'
                : isSystem
                ? 'bg-yellow-100 dark:bg-yellow-800'
                : 'bg-gradient-to-r from-purple-500 to-blue-500'
          }`}>
            <FontAwesomeIcon
              icon={isUser ? faUser : faRobot}
              className={`h-4 w-4 ${
                isUser ? 'text-white' :
                isError ? 'text-red-600 dark:text-red-300' :
                isSystem ? 'text-yellow-600 dark:text-yellow-300' :
                'text-white'
              }`}
            />
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {renderContent()}

            {/* Message metadata and actions */}
            <div className="flex items-center justify-between mt-3">
              <div className={`text-xs ${
                isUser ? 'text-white/70' :
                isError ? 'text-red-500 dark:text-red-400' :
                isSystem ? 'text-yellow-500 dark:text-yellow-400' :
                'text-gray-500 dark:text-gray-400'
              }`}>
                {message.timestamp && formatTimestamp(message.timestamp)}
                {message.model_used && ` • ${message.model_used}`}
                {message.bookmarked && (
                  <>
                    {' • '}
                    <FontAwesomeIcon icon={faBookmark} className="h-3 w-3" />
                  </>
                )}
              </div>

              {/* Quick Actions */}
              {showActions && (
                <div className="flex items-center gap-1 opacity-100 transition-opacity">
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => navigator.clipboard.writeText(message.content)}
                    className="h-6 w-6 p-0"
                    title="Copy message"
                  >
                    <FontAwesomeIcon icon={faCopy} className="h-3 w-3" />
                  </Button>
                  
                  {!isUser && !isSystem && (
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onAction && onAction('regenerate', message.id)}
                      className="h-6 w-6 p-0"
                      title="Regenerate response"
                    >
                      <FontAwesomeIcon icon={faRedo} className="h-3 w-3" />
                    </Button>
                  )}

                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => onAction && onAction('bookmark', message.id)}
                    className={`h-6 w-6 p-0 ${message.bookmarked ? 'text-yellow-500' : ''}`}
                    title="Bookmark message"
                  >
                    <FontAwesomeIcon icon={faBookmark} className="h-3 w-3" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

// Main ChatView Component
const ChatView = ({ addTab }) => {
  const { 
    messages, 
    sendMessage, 
    isLoading, 
    conversations, 
    activeConversationId,
    setActiveConversation,
    createNewConversation,
    settings,
    messagesEndRef,
    fetchAvailableModels
  } = useContext(ChatContext);

  const messagesContainerRef = useRef(null);
  const [showSettings, setShowSettings] = useState(false);

  const handleSendMessage = async (content) => {
    await sendMessage(content);
  };

  const handleNewConversation = () => {
    createNewConversation();
  };

  const handleSelectConversation = (conversationId) => {
    setActiveConversation(conversationId);
  };

  // Fetch available models on mount
  useEffect(() => {
    fetchAvailableModels();
  }, []);

  // Auto-scroll to bottom when messages change
  useEffect(() => {
    if (settings?.autoScroll && messagesEndRef?.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages, settings?.autoScroll]);

  if (showSettings) {
    return <ChatSettings onClose={() => setShowSettings(false)} />;
  }

  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex flex-col">
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <button
            onClick={handleNewConversation}
            className="w-full flex items-center justify-center px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg text-sm font-medium transition-colors"
          >
            <FontAwesomeIcon icon={faPlus} className="mr-2" />
            New Chat
          </button>
        </div>
        
        <div className="flex-1 overflow-y-auto p-2">
          {conversations?.map(conv => (
            <div
              key={conv.id}
              className={`p-3 mb-1 rounded-lg cursor-pointer ${
                conv.id === activeConversationId
                  ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-900 dark:text-blue-100'
                  : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700/50'
              }`}
              onClick={() => handleSelectConversation(conv.id)}
            >
              <div className="flex items-center">
                <FontAwesomeIcon 
                  icon={faRobot} 
                  className={`mr-2 text-sm ${
                    conv.id === activeConversationId 
                      ? 'text-blue-500' 
                      : 'text-gray-400'
                  }`} 
                />
                <span className="truncate text-sm">
                  {conv.title || 'New Chat'}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      {/* Main Chat Area */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <ModernChat 
          messages={messages}
          onSendMessage={handleSendMessage}
          isLoading={isLoading}
        />
      </div>
    </div>
  );
};

const renderWelcomeScreen = () => (
  <div className="flex-1 flex flex-col items-center justify-center px-6 py-12">
    <div className="text-center mb-12">
      <div className="w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center mb-6 mx-auto shadow-xl">
        <FontAwesomeIcon icon={faRobot} className="h-12 w-12 text-white" />
      </div>
      <h2 className="text-4xl font-bold text-gray-900 dark:text-gray-100 mb-4">
        Welcome to Enhanced Chat v2.0.0
      </h2>
      <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
        Your AI-powered assistant with advanced features. Start a conversation or choose from these suggestions.
      </p>
    </div>

    <div className="w-full max-w-4xl">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        {suggestedPrompts.map((prompt, index) => (
          <button
            key={index}
            onClick={() => handleSuggestedPrompt(prompt)}
            className="group p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl hover:border-blue-300 dark:hover:border-blue-600 hover:shadow-lg transition-all duration-200 text-left"
          >
            <div className="flex items-start gap-4">
              <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800 transition-colors">
                <FontAwesomeIcon icon={prompt.icon} className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-gray-900 dark:text-gray-100 mb-1 group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">
                  {prompt.title}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {prompt.description}
                </p>
              </div>
            </div>
          </button>
        ))}
      </div>

      <div className="text-center">
        <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
          Powered by {selectedModel || 'AI'} • Ready to assist you
        </p>
        <div className="flex items-center justify-center gap-2 text-xs text-gray-400">
          <span>Press</span>
          <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-gray-600 dark:text-gray-300">Enter</kbd>
          <span>to send •</span>
          <kbd className="px-2 py-1 bg-gray-100 dark:bg-gray-700 rounded text-gray-600 dark:text-gray-300">Shift + Enter</kbd>
          <span>for new line</span>
        </div>
      </div>
    </div>
  </div>
);

const renderMessages = () => (
  <div className="flex-1 overflow-hidden">
    <div
      ref={messagesContainerRef}
      className="h-full overflow-y-auto px-4 py-4 space-y-4"
    >
      {messages?.length === 0 && isPreloading && (
        <div className="flex flex-col items-center justify-center h-full">
          <FontAwesomeIcon icon={faSpinner} className="h-12 w-12 animate-spin text-blue-500 mb-4" />
          <h3 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            Warming up the model...
          </h3>
          <p className="text-gray-600 dark:text-gray-400 text-center max-w-md">
            This may take a moment while we prepare {selectedModel} for you.
          </p>
        </div>
      )}

      {messages?.length === 0 && preloadingError && (
        <div className="flex flex-col items-center justify-center h-full text-red-600 dark:text-red-400">
          <h3 className="text-xl font-semibold mb-2">Error</h3>
          <p className="text-center max-w-md">{preloadingError}</p>
          <Button
            onClick={() => preloadModel && preloadModel(selectedModel)}
            className="mt-4"
            variant="outline"
          >
            <FontAwesomeIcon icon={faRefresh} className="h-4 w-4 mr-2" />
            Retry
          </Button>
        </div>
      )}

      {messages?.length === 0 && !isPreloading && !preloadingError && renderWelcomeScreen()}

      {messages?.map((message, index) => (
        <MessageBubble
          key={message.id}
          message={message}
          onAction={handleMessageAction}
        />
      ))}
      
      <div ref={messagesEndRef} className="h-px" />
    </div>
  </div>
);

export default ChatView;