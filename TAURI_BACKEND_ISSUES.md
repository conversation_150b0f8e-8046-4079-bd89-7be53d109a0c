# Tauri Backend Issues

## Issue 1: "Tauri backend not available" and persistent initialization screen

**Description:** The frontend application consistently reports "Tauri backend not available" errors in the console, leading to `SaveManager` falling back to `localStorage` or default data. This prevents user settings from loading and keeps the application stuck on an initialization screen.

**Root Cause:** The frontend is unable to establish a connection with the Tauri backend.
- `tauriHelpers.js` checks for `window.__TAURI__`, which is consistently `undefined`.
- The presence of an Electron-specific `preload.js` file suggests a potential conflict or misconfiguration, as Tauri injects its own API (`window.__TAURI__`) and does not use Electron's `contextBridge` or `ipcRenderer`.
- The `preload.js` file is attempting to expose `electronAPI`, which is not compatible with the Tauri environment.
- **New Finding:** The `src-tauri/tauri.conf.json` file is missing the `app.withGlobalTauri: true` setting, which is essential for Tauri to inject its API into the frontend.

**Prevention:**
- Ensure that the project is consistently configured for either Tauri or Electron, but not both.
- Verify that no Electron-specific preload scripts are present or being loaded in a Tauri environment.
- Confirm that <PERSON>ri's API injection is not being inadvertently blocked or overridden.
- **New Prevention:** Ensure `app.withGlobalTauri: true` is set in `tauri.conf.json` for Tauri v2 projects.

**Testing:**
- Verify that `window.__TAURI__` is defined in the frontend console when running the Tauri application.
- Ensure that `safeInvoke` calls in `tauriHelpers.js` successfully communicate with the backend.
- Confirm that user settings and UI state are loaded correctly from the Tauri backend, not just `localStorage`.

## Issue 2: React Router Future Flag Warnings

**Description:** The console displays warnings regarding React Router future flags (`v7_startTransition`, `v7_relativeSplatPath`).

**Root Cause:** The application is using an older version of React Router or is not explicitly opting into the new v7 behaviors.

**Prevention:**
- Update React Router to the latest stable version.
- Explicitly enable future flags if desired, or address any breaking changes introduced in v7.

**Testing:**
- Verify that React Router warnings no longer appear in the console.
- Ensure all routing functionality works as expected after addressing the warnings.