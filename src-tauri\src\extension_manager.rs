use crate::settings_manager::SYSTEM_DIR;
use serde::{Deserialize, Serialize};
use std::collections::HashMap;
use std::fs;
use std::path::PathBuf;
use log::error;
use lazy_static::lazy_static;

// Path to the extensions registry file
lazy_static! {
    pub static ref EXTENSIONS_REGISTRY_PATH: PathBuf = SYSTEM_DIR.join("Extensions").join("extensions_registry.json");
}

// Extension status enum
#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
#[serde(rename_all = "lowercase")]
pub enum ExtensionStatus {
    Active,
    Development,
    Disabled,
}

// Handler type
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct HandlerInfo {
    pub name: String,
    pub description: String,
    pub version: String,
}

// Extension definition
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct Extension {
    pub name: String,
    pub extension: String,  // The file extension (e.g., ".prompt")
    pub description: String,
    pub file_type: String,  // E.g., "Text", "JSON", "Binary"
    pub handler: String,    // Name of the handler app/component
    pub status: ExtensionStatus,
    pub icon: String,       // Icon identifier (e.g., "file", "database", "box-open", "robot")
    pub color: String,      // Color theme (e.g., "blue", "green", "purple")
    #[serde(default)]
    pub metadata: HashMap<String, String>,  // Additional metadata
}

// Registry structure
#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ExtensionsRegistry {
    pub extensions: Vec<Extension>,
    pub handlers: Vec<HandlerInfo>,
    #[serde(default)]
    pub version: u32,
}

impl Default for ExtensionsRegistry {
    fn default() -> Self {
        // Default extensions from the existing UI
        let default_extensions = vec![
            Extension {
                name: "Prompt".to_string(),
                extension: ".prompt".to_string(),
                description: "System prompts that define AI behavior and personality".to_string(),
                file_type: "Text".to_string(),
                handler: "Prompt Editor".to_string(),
                status: ExtensionStatus::Active,
                icon: "file".to_string(),
                color: "blue".to_string(),
                metadata: HashMap::new(),
            },
            Extension {
                name: "Context".to_string(),
                extension: ".context".to_string(),
                description: "Contextual information and knowledge base entries".to_string(),
                file_type: "JSON".to_string(),
                handler: "Context Manager".to_string(),
                status: ExtensionStatus::Active,
                icon: "database".to_string(),
                color: "green".to_string(),
                metadata: HashMap::new(),
            },
            Extension {
                name: "Modal".to_string(),
                extension: ".modal".to_string(),
                description: "Specialized nano-algorithms for expertise domains".to_string(),
                file_type: "Binary".to_string(),
                handler: "Modal Engine".to_string(),
                status: ExtensionStatus::Development,
                icon: "box-open".to_string(),
                color: "purple".to_string(),
                metadata: HashMap::new(),
            },
            Extension {
                name: "Agent".to_string(),
                extension: ".agent".to_string(),
                description: "AI agent profiles with task specializations".to_string(),
                file_type: "JSON".to_string(),
                handler: "Agent Manager".to_string(),
                status: ExtensionStatus::Active,
                icon: "robot".to_string(),
                color: "orange".to_string(),
                metadata: HashMap::new(),
            },
        ];

        // Default handlers
        let default_handlers = vec![
            HandlerInfo {
                name: "Prompt Editor".to_string(),
                description: "Edits system prompts with syntax highlighting".to_string(),
                version: "1.0.0".to_string(),
            },
            HandlerInfo {
                name: "Context Manager".to_string(),
                description: "Manages contextual knowledge entries".to_string(),
                version: "1.0.0".to_string(),
            },
            HandlerInfo {
                name: "Modal Engine".to_string(),
                description: "Executes specialized nano-algorithms".to_string(),
                version: "0.5.0".to_string(),
            },
            HandlerInfo {
                name: "Agent Manager".to_string(),
                description: "Configures AI agent profiles".to_string(),
                version: "1.0.0".to_string(),
            },
        ];

        Self {
            extensions: default_extensions,
            handlers: default_handlers,
            version: 1,
        }
    }
}

impl ExtensionsRegistry {
    // Load the registry from file
    pub fn load() -> Self {
        // Ensure the directory exists
        if let Some(parent) = EXTENSIONS_REGISTRY_PATH.parent() {
            if !parent.exists() {
                if let Err(e) = fs::create_dir_all(parent) {
                    error!("Failed to create extensions directory: {}", e);
                    return Self::default();
                }
            }
        }

        // Try to read the registry file
        if EXTENSIONS_REGISTRY_PATH.exists() {
            match fs::read_to_string(EXTENSIONS_REGISTRY_PATH.as_path()) {
                Ok(contents) => {
                    match serde_json::from_str::<ExtensionsRegistry>(&contents) {
                        Ok(registry) => return registry,
                        Err(e) => {
                            error!("Failed to parse extensions registry: {}", e);
                        }
                    }
                }
                Err(e) => {
                    error!("Failed to read extensions registry: {}", e);
                }
            }
        }

        // If we get here, either the file doesn't exist or there was an error
        // Create a new default registry and save it
        let default_registry = Self::default();
        if let Err(e) = default_registry.save() {
            error!("Failed to save default extensions registry: {}", e);
        }
        default_registry
    }

    // Save the registry to file
    pub fn save(&self) -> Result<(), String> {
        // Ensure the directory exists
        if let Some(parent) = EXTENSIONS_REGISTRY_PATH.parent() {
            if !parent.exists() {
                fs::create_dir_all(parent)
                    .map_err(|e| format!("Failed to create extensions directory: {}", e))?;
            }
        }

        // Serialize and save the registry
        let json = serde_json::to_string_pretty(&self)
            .map_err(|e| format!("Failed to serialize extensions registry: {}", e))?;
        
        fs::write(EXTENSIONS_REGISTRY_PATH.as_path(), json)
            .map_err(|e| format!("Failed to write extensions registry file: {}", e))?;
        
        Ok(())
    }

    // Add a new extension
    pub fn add_extension(&mut self, extension: Extension) -> Result<(), String> {
        // Check if an extension with the same name or extension already exists
        if self.extensions.iter().any(|e| e.extension == extension.extension) {
            return Err(format!("Extension {} already exists", extension.extension));
        }

        self.extensions.push(extension);
        self.save()?;
        Ok(())
    }

    // Update an existing extension
    pub fn update_extension(&mut self, extension: Extension) -> Result<(), String> {
        let index = self.extensions.iter().position(|e| e.extension == extension.extension)
            .ok_or_else(|| format!("Extension {} not found", extension.extension))?;
        
        self.extensions[index] = extension;
        self.save()?;
        Ok(())
    }

    // Remove an extension
    pub fn remove_extension(&mut self, extension_name: &str) -> Result<(), String> {
        let index = self.extensions.iter().position(|e| e.extension == extension_name)
            .ok_or_else(|| format!("Extension {} not found", extension_name))?;
        
        self.extensions.remove(index);
        self.save()?;
        Ok(())
    }

    // Get statistics
    pub fn get_stats(&self) -> HashMap<String, u32> {
        let mut stats = HashMap::new();
        
        // Count active extensions
        stats.insert(
            "active".to_string(), 
            self.extensions.iter().filter(|e| e.status == ExtensionStatus::Active).count() as u32
        );
        
        // Count development extensions
        stats.insert(
            "development".to_string(), 
            self.extensions.iter().filter(|e| e.status == ExtensionStatus::Development).count() as u32
        );
        
        // Count disabled extensions
        stats.insert(
            "disabled".to_string(), 
            self.extensions.iter().filter(|e| e.status == ExtensionStatus::Disabled).count() as u32
        );
        
        // Count all extensions
        stats.insert("total".to_string(), self.extensions.len() as u32);
        
        // Count handlers
        stats.insert("handlers".to_string(), self.handlers.len() as u32);
        
        stats
    }
}

// Tauri commands for extension management

#[tauri::command]
pub async fn get_extensions_registry() -> Result<ExtensionsRegistry, String> {
    Ok(ExtensionsRegistry::load())
}

#[tauri::command]
pub async fn add_extension(extension: Extension) -> Result<(), String> {
    let mut registry = ExtensionsRegistry::load();
    registry.add_extension(extension)
}

#[tauri::command]
pub async fn update_extension(extension: Extension) -> Result<(), String> {
    let mut registry = ExtensionsRegistry::load();
    registry.update_extension(extension)
}

#[tauri::command]
pub async fn remove_extension(extension_name: String) -> Result<(), String> {
    let mut registry = ExtensionsRegistry::load();
    registry.remove_extension(&extension_name)
}

#[tauri::command]
pub async fn get_extension_stats() -> Result<HashMap<String, u32>, String> {
    Ok(ExtensionsRegistry::load().get_stats())
}