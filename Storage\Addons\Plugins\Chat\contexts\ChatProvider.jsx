import React, { createContext, useState, useEffect, useRef, useCallback } from 'react';
import { mockConversations, mockMessages, mockModels, mockSettings } from '../mockData';

export const ChatContext = createContext();

// Mock <PERSON> functions for preview
const mockInvoke = async (command, args) => {
  console.log('Mock invoke:', command, args);
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 100));
  
  switch (command) {
    case 'get_conversations_command':
      return mockConversations;
    case 'get_conversation_messages_command':
      return mockMessages.filter(m => m.conversationId === args?.conversationId);
    case 'get_available_models_command':
      return mockModels;
    case 'preload_model_command':
      // Simulate model preloading
      await new Promise(resolve => setTimeout(resolve, 1000));
      return { success: true };
    case 'send_chat_message_command':
      // Simulate sending message and getting response
      setTimeout(() => {
        // Simulate streaming response
        const responses = [
          "Thank you for your message! ",
          "I'm the enhanced chat system v2.0.0 with all the modern features you requested. ",
          "I can help you with code reviews, creative writing, technical documentation, and much more!\n\n",
          "**Features available:**\n",
          "- 🤖 Multiple AI model selection\n",
          "- 💬 Conversation management\n", 
          "- 🔍 Message search and filtering\n",
          "- ⚙️ Advanced settings\n",
          "- 🎨 Beautiful modern UI\n",
          "- 📱 Responsive design\n\n",
          "Try exploring the settings or creating a new conversation!"
        ];
        
        responses.forEach((chunk, index) => {
          setTimeout(() => {
            window.dispatchEvent(new CustomEvent('mock_chat_token', { 
              detail: { content: chunk } 
            }));
          }, index * 200);
        });
        
        setTimeout(() => {
          window.dispatchEvent(new CustomEvent('mock_chat_token_end'));
        }, responses.length * 200 + 500);
      }, 500);
      return { success: true };
    case 'save_conversation_command':
    case 'save_message_command':
    case 'update_conversation_command':
    case 'delete_conversation_command':
    case 'delete_message_command':
    case 'update_message_command':
    case 'search_messages_command':
      return { success: true };
    case 'export_conversation_command':
      return `# Chat Export\n\nThis is a sample export of your conversation.\n\n## Messages\n\n${mockMessages.map(m => `**${m.sender}:** ${m.content}`).join('\n\n')}`;
    default:
      return { success: true };
  }
};

const mockListen = async (event, callback) => {
  console.log('Mock listen:', event);
  
  // Set up mock event listeners
  const handleMockEvent = (e) => {
    if (event === 'chat_token' && e.type === 'mock_chat_token') {
      callback({ payload: e.detail });
    } else if (event === 'chat_token_end' && e.type === 'mock_chat_token_end') {
      callback({ payload: 'Stream ended' });
    }
  };
  
  window.addEventListener('mock_chat_token', handleMockEvent);
  window.addEventListener('mock_chat_token_end', handleMockEvent);
  
  return () => {
    window.removeEventListener('mock_chat_token', handleMockEvent);
    window.removeEventListener('mock_chat_token_end', handleMockEvent);
  };
};

// Use mock functions by default
let invoke = mockInvoke;
let listen = mockListen;