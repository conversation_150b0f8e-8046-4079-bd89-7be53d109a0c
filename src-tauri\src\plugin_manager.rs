use serde::{Deserialize, Serialize};
use std::fs;
use std::env;
use tauri::State;
use crate::settings_manager::UserPreferences;

// Helper function to resolve relative paths from app directory
fn resolve_path(path: &str) -> Result<std::path::PathBuf, String> {
    if path.starts_with("./") {
        let mut current_dir = env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))?;
        while !current_dir.join("src-tauri").exists() {
            if !current_dir.pop() {
                return Err("Could not find project root containing 'src-tauri'".to_string());
            }
        }
        let resolved = current_dir.join(&path[2..]);
        Ok(resolved)
    } else {
        Ok(std::path::PathBuf::from(path))
    }
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Plugin {
    pub id: i64,
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    pub entry_path: Option<String>,
    pub enabled: bool,
    pub path: String,
    pub app_type: Option<String>,
    pub plugin_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
struct PluginJson {
    pub name: String,
    pub description: Option<String>,
    pub version: String,
    #[serde(default = "default_enabled")]
    pub enabled: bool,
    pub main: Option<String>,
    pub class_name: Option<String>,
    pub plugin_type: Option<String>,
    pub app_type: Option<String>,
    pub features: Option<Vec<String>>,
    pub author: Option<String>,
}

fn default_enabled() -> bool {
    true
}

#[tauri::command]
pub async fn get_plugins(prefs: State<'_, UserPreferences>) -> Result<Vec<Plugin>, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    let mut plugins = Vec::new();
    let mut plugin_id = 1;

    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                if let Ok(contents) = fs::read_to_string(&plugin_json_path) {
                    if let Ok(plugin_data) = serde_json::from_str::<PluginJson>(&contents) {
                        let main_file = plugin_data.main.clone().unwrap_or_else(|| "main.jsx".to_string());
                        let entry_path = plugin_path.join(&main_file);

                        if entry_path.exists() {
                            let fs_entry_path = entry_path.to_string_lossy().replace("\\", "/");
                            println!("DEBUG: Entry path for {}: {}", plugin_data.name, fs_entry_path);

                            plugins.push(Plugin {
                                id: plugin_id,
                                name: plugin_data.name.clone(),
                                description: plugin_data.description.clone(),
                                version: plugin_data.version.clone(),
                                entry_path: Some(fs_entry_path),
                                enabled: plugin_data.enabled,
                                path: plugin_path.to_string_lossy().to_string().replace("\\", "/"),
                                app_type: plugin_data.app_type.clone(),
                                plugin_type: plugin_data.plugin_type.clone(),
                                features: plugin_data.features.clone(),
                                author: plugin_data.author.clone(),
                            });
                            plugin_id += 1;
                        } else {
                            println!("WARN: Entry file not found for plugin {}: {}", plugin_data.name, entry_path.display());
                        }
                    }
                }
            }
        }
    }

    println!("DEBUG: Total plugins found: {}", plugins.len());
    Ok(plugins)
}

#[tauri::command]
pub async fn toggle_plugin(plugin_name: String, enabled: bool, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;

    if !plugins_dir.exists() {
        return Err(format!("Plugins directory does not exist: {}", plugins_dir.display()));
    }

    for entry in fs::read_dir(&plugins_dir).map_err(|e| format!("Failed to read plugins directory: {}", e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let plugin_path = entry.path();

        if plugin_path.is_dir() {
            let plugin_json_path = plugin_path.join("plugin.json");
            if plugin_json_path.exists() {
                let contents = fs::read_to_string(&plugin_json_path)
                    .map_err(|e| format!("Failed to read plugin.json: {}", e))?;
                let mut plugin_data: PluginJson = serde_json::from_str(&contents)
                    .map_err(|e| format!("Failed to parse plugin.json: {}", e))?;

                if plugin_data.name == plugin_name {
                    plugin_data.enabled = enabled;

                    let updated_contents = serde_json::to_string_pretty(&plugin_data)
                        .map_err(|e| format!("Failed to serialize plugin.json: {}", e))?;
                    fs::write(&plugin_json_path, updated_contents)
                        .map_err(|e| format!("Failed to write plugin.json: {}", e))?;

                    return Ok(format!("Plugin {} {}", plugin_name, if enabled { "enabled" } else { "disabled" }));
                }
            }
        }
    }

    Err(format!("Plugin {} not found", plugin_name))
}

#[tauri::command]
pub async fn save_plugin_state(plugin_name: String, state_data: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");

    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }

    let state_file = data_dir.join("state.json");
    fs::write(&state_file, state_data)
        .map_err(|e| format!("Failed to save plugin state: {}", e))?;

    Ok(format!("Saved state for {}", plugin_name))
}

#[tauri::command]
pub async fn load_plugin_state(plugin_name: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    let state_file = data_dir.join("state.json");

    if state_file.exists() {
        fs::read_to_string(&state_file).map_err(|e| format!("Failed to read state: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn get_plugin_settings(plugin_name: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let settings_file = plugin_dir.join("settings.json");

    if settings_file.exists() {
        fs::read_to_string(&settings_file).map_err(|e| format!("Failed to read settings: {}", e))
    } else {
        Ok("{}".to_string())
    }
}

#[tauri::command]
pub async fn save_plugin_data(plugin_name: String, data_key: String, data_value: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");

    if !data_dir.exists() {
        fs::create_dir_all(&data_dir)
            .map_err(|e| format!("Failed to create data directory: {}", e))?;
    }

    let file_path = data_dir.join(format!("{}.json", data_key));
    fs::write(&file_path, data_value)
        .map_err(|e| format!("Failed to save data: {}", e))?;

    Ok(format!("Saved data for {}", plugin_name))
}

#[tauri::command]
pub async fn get_plugin_data(plugin_name: String, data_key: String, prefs: State<'_, UserPreferences>) -> Result<String, String> {
    let plugins_dir = resolve_path(&prefs.plugins_path)?;
    let plugin_dir = plugins_dir.join(&plugin_name);
    let data_dir = plugin_dir.join("data");
    let file_path = data_dir.join(format!("{}.json", data_key));

    if file_path.exists() {
        fs::read_to_string(&file_path).map_err(|e| format!("Failed to read data: {}", e))
    } else {
        Ok("{}".to_string())
    }
}
