import React, { useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Grid, 
  Paper, 
  LinearProgress, 
  Button,
  Divider,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  SettingsEthernet as NetworkIcon,
  Speed as SpeedIcon,
  Wifi as WifiIcon,
  Lan as LanIcon,
  Dns as DnsIcon,
  Router as RouterIcon,
  Refresh as RefreshIcon,
  DeviceHub as DeviceHubIcon,
  SignalCellularAlt as SignalIcon,
  Security as SecurityIcon,
  Cloud as CloudIcon,
} from '@mui/icons-material';
import { useNetwork } from '../../../contexts/NetworkContext';

const formatBytes = (bytes, decimals = 2) => {
  if (!+bytes) return '0 Bytes';
  const k = 1024;
  const dm = decimals < 0 ? 0 : decimals;
  const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
};

const formatSpeed = (bytesPerSecond) => {
  return `${formatBytes(bytesPerSecond)}/s`;
};

const NetworkDetails = () => {
  const { 
    networkInterfaces, 
    activeInterface, 
    setActiveInterface, 
    networkStats, 
    isScanning, 
    devices, 
    error, 
    scanNetwork 
  } = useNetwork();

  useEffect(() => {
    if (activeInterface) {
      scanNetwork();
    }
  }, [activeInterface]);

  if (!activeInterface) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Network Interface Selection */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            <NetworkIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Network Interface
          </Typography>
          <Button 
            variant="outlined" 
            size="small" 
            startIcon={<RefreshIcon />}
            onClick={scanNetwork}
            disabled={isScanning}
          >
            {isScanning ? 'Scanning...' : 'Rescan'}
          </Button>
        </Box>
        
        <Grid container spacing={2}>
          <Grid item xs={12} md={6}>
            <List dense>
              <ListItem>
                <ListItemIcon><WifiIcon /></ListItemIcon>
                <ListItemText 
                  primary={activeInterface?.name || 'Unknown'} 
                  secondary={activeInterface?.type === 'wireless' ? 'WiFi' : 'Ethernet'} 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SignalIcon /></ListItemIcon>
                <ListItemText 
                  primary="Status" 
                  secondary={
                    <Chip 
                      label={activeInterface?.status === 'up' ? 'Connected' : 'Disconnected'} 
                      size="small" 
                      color={activeInterface?.status === 'up' ? 'success' : 'error'}
                    />
                  } 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SpeedIcon /></ListItemIcon>
                <ListItemText 
                  primary="Link Speed" 
                  secondary={activeInterface?.speed ? `${activeInterface.speed} Mbps` : 'Unknown'} 
                />
              </ListItem>
            </List>
          </Grid>
          <Grid item xs={12} md={6}>
            <List dense>
              <ListItem>
                <ListItemIcon><DnsIcon /></ListItemIcon>
                <ListItemText 
                  primary="IPv4" 
                  secondary={activeInterface?.ip4 || 'Not connected'} 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><RouterIcon /></ListItemIcon>
                <ListItemText 
                  primary="Gateway" 
                  secondary={activeInterface?.gateway || 'Unknown'} 
                />
              </ListItem>
              <ListItem>
                <ListItemIcon><SecurityIcon /></ListItemIcon>
                <ListItemText 
                  primary="MAC Address" 
                  secondary={activeInterface?.mac || 'Unknown'} 
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Network Statistics */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" component="h2" sx={{ mb: 2 }}>
          <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
          Network Statistics
        </Typography>
        
        <Grid container spacing={3}>
          <Grid item xs={12} md={6}>
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Download: {formatSpeed(networkStats.downloadSpeed)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatBytes(networkStats.totalDownload)} total
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={Math.min(100, (networkStats.downloadSpeed / (10 * 1024 * 1024)) * 100)} 
                color="primary"
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
            
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="body2" color="text.secondary">
                  Upload: {formatSpeed(networkStats.uploadSpeed)}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  {formatBytes(networkStats.totalUpload)} total
                </Typography>
              </Box>
              <LinearProgress 
                variant="determinate" 
                value={Math.min(100, (networkStats.uploadSpeed / (10 * 1024 * 1024)) * 100)} 
                color="secondary"
                sx={{ height: 8, borderRadius: 4 }}
              />
            </Box>
          </Grid>
          
          <Grid item xs={12} md={6}>
            <List dense>
              <ListItem>
                <ListItemText 
                  primary="Packets Sent" 
                  secondary={networkStats.packetsSent.toLocaleString()} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Packets Received" 
                  secondary={networkStats.packetsReceived.toLocaleString()} 
                />
              </ListItem>
              <ListItem>
                <ListItemText 
                  primary="Active Connections" 
                  secondary={devices.length} 
                />
              </ListItem>
            </List>
          </Grid>
        </Grid>
      </Paper>

      {/* Network Devices */}
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" component="h2">
            <DeviceHubIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Network Devices
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {devices.length} devices found
          </Typography>
        </Box>
        
        {isScanning ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
            <CircularProgress />
          </Box>
        ) : devices.length > 0 ? (
          <Grid container spacing={2}>
            {devices.map((device, index) => (
              <Grid item xs={12} sm={6} md={4} key={index}>
                <Paper sx={{ p: 2, height: '100%' }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <DeviceHubIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="subtitle1" noWrap>
                      {device.name || 'Unknown Device'}
                    </Typography>
                  </Box>
                  <List dense>
                    <ListItem disableGutters>
                      <ListItemText 
                        primary="IP Address" 
                        secondary={device.ip} 
                        primaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                    <ListItem disableGutters>
                      <ListItemText 
                        primary="MAC Address" 
                        secondary={device.mac || 'Unknown'} 
                        primaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                    <ListItem disableGutters>
                      <ListItemText 
                        primary="Vendor" 
                        secondary={device.vendor || 'Unknown'} 
                        primaryTypographyProps={{ variant: 'caption' }}
                      />
                    </ListItem>
                    {device.os && (
                      <ListItem disableGutters>
                        <ListItemText 
                          primary="OS" 
                          secondary={device.os} 
                          primaryTypographyProps={{ variant: 'caption' }}
                        />
                      </ListItem>
                    )}
                  </List>
                </Paper>
              </Grid>
            ))}
          </Grid>
        ) : (
          <Box sx={{ textAlign: 'center', p: 4 }}>
            <CloudIcon sx={{ fontSize: 48, opacity: 0.3, mb: 1 }} />
            <Typography color="text.secondary">No devices found on the network</Typography>
          </Box>
        )}
      </Paper>
    </Box>
  );
};

export default NetworkDetails;
