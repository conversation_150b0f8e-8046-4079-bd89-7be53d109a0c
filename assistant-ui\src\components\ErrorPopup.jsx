import React from 'react';
import { safeCopyToClipboard } from '../utils/domSafety';

/**
 * Error Popup component to display errors in a consistent, user-friendly way.
 * This component can be used throughout the application for error notifications.
 */
const ErrorPopup = ({
  title = "Something went wrong",
  message = "An error occurred in the application.",
  error = null,
  onReload = null,
  onRetry = null,
  showDetails = false,
  className = ""
}) => {
  const [showErrorDetails, setShowErrorDetails] = React.useState(showDetails);
  const [copySuccess, setCopySuccess] = React.useState(false);

  const handleReload = () => {
    if (onReload) {
      onReload();
    } else {
      window.location.reload();
    }
  };

  const handleRetry = () => {
    if (onRetry) {
      onRetry();
    }
  };

  const handleCopyError = async () => {
    const errorReport = `
Error Report - The Collective
=============================
Title: ${title}
Message: ${message}
Timestamp: ${new Date().toISOString()}

${error ? `Error Details:
${error.toString()}

${error.stack ? `Stack Trace:
${error.stack}` : ''}` : 'No additional error details available.'}
    `.trim();

    const success = await safeCopyToClipboard(errorReport);

    if (success) {
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000);
    } else {
      console.error('Failed to copy error report');
      // If all else fails, just log the error report for manual copying
      console.log('Error Report for manual copying:', errorReport);
    }
  };

  return (
    <div className={`flex items-center justify-center h-full min-h-screen bg-background p-4 ${className}`}>
      <div className="max-w-md w-full bg-card text-card-foreground rounded-lg shadow-lg p-6 border border-border">
        <div className="text-center">
          <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-destructive/10 mb-4">
            <svg className="h-6 w-6 text-destructive" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
          </div>
          <h2 className="text-xl font-bold text-foreground mb-2">{title}</h2>
          <p className="text-muted-foreground mb-4">
            {message}
          </p>
          
          {error && (
            <div className="bg-muted rounded-lg p-4 mb-4 text-left">
              <div 
                className="flex justify-between items-center cursor-pointer"
                onClick={() => setShowErrorDetails(!showErrorDetails)}
              >
                <span className="font-medium text-sm">Error details</span>
                <svg 
                  className={`h-4 w-4 transition-transform ${showErrorDetails ? 'rotate-180' : ''}`} 
                  fill="none" 
                  viewBox="0 0 24 24" 
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                </svg>
              </div>
              
              {showErrorDetails && (
                <div className="mt-2">
                  <p className="text-sm font-mono text-muted-foreground break-words">{error.toString()}</p>
                  {error.stack && (
                    <pre className="mt-2 text-xs overflow-auto max-h-32 text-muted-foreground">
                      {error.stack}
                    </pre>
                  )}
                </div>
              )}
            </div>
          )}
          
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <button
              onClick={handleReload}
              className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 transition-colors"
            >
              Reload Page
            </button>
            {error && (
              <button
                onClick={handleCopyError}
                className="px-4 py-2 bg-secondary text-secondary-foreground rounded-md hover:bg-secondary/80 focus:outline-none focus:ring-2 focus:ring-secondary focus:ring-offset-2 transition-colors flex items-center gap-2"
              >
                {copySuccess ? (
                  <>
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Copied!
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                    </svg>
                    Copy Error Report
                  </>
                )}
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ErrorPopup;