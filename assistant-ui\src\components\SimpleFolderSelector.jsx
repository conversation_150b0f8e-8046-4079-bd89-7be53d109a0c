import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faFolder, faFile, faFolderOpen, faExclamationTriangle, faCheckCircle } from '@fortawesome/free-solid-svg-icons';

import { Button } from "./ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "./ui/card";
import { Label } from "./ui/label";
import { Input } from "./ui/input";

const SimpleFolderSelector = ({
  label,
  description,
  fetchCommand,
  saveCommand,
  onFolderSelected,
  defaultPath
}) => {
  const [currentPath, setCurrentPath] = useState(defaultPath || '');
  const [directoryInfo, setDirectoryInfo] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState({ text: '', type: '' });
  const [isExpanded, setIsExpanded] = useState(false);

  // Load current path on component mount
  useEffect(() => {
    if (defaultPath) {
      setCurrentPath(defaultPath);
      loadDirectoryInfo(defaultPath);
      if (onFolderSelected) {
        onFolderSelected(defaultPath);
      }
    } else {
      loadCurrentPath();
    }
  }, [fetchCommand, defaultPath]);

  const loadCurrentPath = async () => {
    if (!fetchCommand) return;
    
    try {
      const pathValue = await invoke(fetchCommand);
      setCurrentPath(pathValue || '');
      if (pathValue) {
        await loadDirectoryInfo(pathValue);
      }
    } catch (error) {
      console.error(`Error loading current path:`, error);
      setCurrentPath('');
    }
  };

  const loadDirectoryInfo = async (path) => {
    if (!path) return;
    
    try {
      const info = await invoke('get_directory_info', { path });
      setDirectoryInfo(info);
    } catch (error) {
      console.error('Error loading directory info:', error);
      setDirectoryInfo({
        path,
        exists: false,
        files: [],
        directories: [],
        error: 'Failed to load directory info'
      });
    }
  };

  const handleSelectFolder = async () => {
    setIsLoading(true);
    setMessage({ text: '', type: '' });

    try {
      // Open folder selection dialog
      const result = await invoke('dialog_open', { dialog_type: 'openDirectory' });
      
      if (result && !result.canceled && result.file_paths && result.file_paths.length > 0) {
        const selectedPath = result.file_paths[0];
        
        // Save to preferences automatically
        if (saveCommand) {
          await invoke(saveCommand, { path: selectedPath });
        }
        
        // Update UI
        setCurrentPath(selectedPath);
        await loadDirectoryInfo(selectedPath);
        
        // Notify parent component
        if (onFolderSelected) {
          onFolderSelected(selectedPath);
        }
        
        setMessage({ text: 'Folder selected and saved successfully!', type: 'success' });
      }
    } catch (error) {
      console.error('Error selecting folder:', error);
      setMessage({ text: 'Failed to select folder. Please try again.', type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const renderDirectoryContents = () => {
    if (!directoryInfo) return null;

    if (!directoryInfo.exists) {
      return (
        <div className="text-red-600 text-xs">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1 h-2 w-2" />
          Folder does not exist
        </div>
      );
    }

    if (directoryInfo.error) {
      return (
        <div className="text-red-600 text-xs">
          <FontAwesomeIcon icon={faExclamationTriangle} className="mr-1 h-2 w-2" />
          {directoryInfo.error}
        </div>
      );
    }

    const totalItems = directoryInfo.directories.length + directoryInfo.files.length;
    
    if (totalItems === 0) {
      return (
        <div className="text-muted-foreground text-xs">
          Folder is empty
        </div>
      );
    }

    if (!isExpanded) {
      return (
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <span>{directoryInfo.directories.length} folders, {directoryInfo.files.length} files</span>
          <Button variant="link" size="sm" onClick={() => setIsExpanded(true)} className="text-xs h-5 p-0 ml-1">
            Show
          </Button>
        </div>
      );
    }

    return (
      <div>
        <div className="flex items-center justify-between text-xs text-muted-foreground mb-1">
          <span>{directoryInfo.directories.length} folders, {directoryInfo.files.length} files</span>
          <Button variant="link" size="sm" onClick={() => setIsExpanded(false)} className="text-xs h-5 p-0 ml-1">
            Hide
          </Button>
        </div>
        
        <div className="max-h-24 overflow-y-auto space-y-0.5 text-xs">
          {directoryInfo.directories.slice(0, 5).map((dir, index) => (
            <div key={`dir-${index}`} className="flex items-center text-blue-600">
              <FontAwesomeIcon icon={faFolder} className="mr-1 h-2 w-2" />
              {dir}
            </div>
          ))}
          {directoryInfo.files.slice(0, 5).map((file, index) => (
            <div key={`file-${index}`} className="flex items-center text-muted-foreground">
              <FontAwesomeIcon icon={faFile} className="mr-1 h-2 w-2" />
              {file}
            </div>
          ))}
          {totalItems > 10 && (
            <div className="text-xs text-muted-foreground italic text-[10px]">
              ... and {totalItems - 10} more items
            </div>
          )}
        </div>
      </div>
    );
  };

  return (
    // Reduced spacing from space-y-4 to space-y-1 for 25% height
    <div className="space-y-1">
      {/* Header - Removed description to save space */}
      <div>
        <Label htmlFor="folder-path" className="text-xs font-medium">
          {label || 'Select Folder'}
        </Label>
        {/* Removed description to make component thinner */}
      </div>

      {/* Current Selection with side-by-side button */}
      <div className="flex space-x-2 items-center">
        <div className="flex-1 relative">
          <Input
            id="folder-path"
            value={currentPath}
            readOnly
            placeholder="Select a folder..."
            // Reduced text size from text-sm to text-xs and adjusted padding
            className="pr-20 font-mono text-xs bg-slate-50 dark:bg-slate-800 h-7"
          />
          <Button 
            onClick={handleSelectFolder} 
            disabled={isLoading}
            // Made button smaller with reduced padding and text size
            size="sm"
            className="absolute right-1 top-0.5 bottom-0.5 h-6 px-2 text-xs"
          >
            <FontAwesomeIcon icon={faFolderOpen} className="mr-1 h-2 w-2" />
            {isLoading ? '...' : 'Select'}
          </Button>
        </div>
      </div>

      {/* Directory Contents - Only shown if we have contents */}
      {directoryInfo && (
        // Reduced margin from mt-1 to mt-0.5
        <div className="mt-0.5 text-xs">
          {renderDirectoryContents()}
        </div>
      )}

      {/* Status Message - Made smaller and thinner */}
      {message.text && (
        // Reduced padding from p-3 to p-1 and made text smaller
        <div className={`text-xs p-1 rounded ${
          message.type === 'success' 
            ? 'bg-green-100 text-green-700 dark:bg-green-900 dark:text-green-300' 
            : 'bg-red-100 text-red-700 dark:bg-red-900 dark:text-red-300'
        }`}>
          <FontAwesomeIcon 
            icon={message.type === 'success' ? faCheckCircle : faExclamationTriangle} 
            className="mr-1 h-2 w-2" 
          />
          {message.text}
        </div>
      )}
    </div>
  );
};

export default SimpleFolderSelector;