import React, { useEffect, useState } from 'react';
import { useNotifications } from '../contexts/NotificationContext';
import { useSettings } from '../contexts/SettingsContext';
import { X, CheckCircle, AlertCircle, AlertTriangle, Info } from 'lucide-react';

const Toast = ({ notification }) => {
  const { clearNotification } = useNotifications();
  const [timeLeft, setTimeLeft] = useState(notification.duration / 1000);

  useEffect(() => {
    if (notification.duration) {
      const timer = setInterval(() => {
        setTimeLeft((prevTime) => {
          if (prevTime <= 1) {
            clearInterval(timer);
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);

      return () => clearInterval(timer);
    }
  }, [notification.duration]);

  const getIcon = (type) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="w-5 h-5 text-yellow-500" />;
      case 'info':
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getBackgroundColor = (type) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 dark:bg-red-900/20 dark:border-red-800';
      case 'warning':
        return 'bg-yellow-50 border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-800';
    }
  };

  return (
    <div
      className={`
        flex items-center gap-3 p-4 rounded-lg border shadow-lg backdrop-blur-sm
        transform transition-all duration-300 ease-in-out
        hover:scale-105 cursor-pointer
        ${getBackgroundColor(notification.type)}
      `}
      onClick={() => clearNotification(notification.id)}
    >
      {getIcon(notification.type)}
      
      <div className="flex-1 min-w-0">
        {notification.title && (
          <div className="font-semibold text-sm text-foreground mb-1">
            {notification.title}
          </div>
        )}
        <div className="text-sm text-muted-foreground">
          <p className="text-sm opacity-90">{notification.message}</p>
          {notification.source && (
            <span className="text-xs opacity-70">Source: {notification.source}</span>
          )}
          {notification.category && (
            <span className="text-xs opacity-70 ml-2">Category: {notification.category}</span>
          )}
          {notification.priority && (
            <span className="text-xs opacity-70 ml-2">Priority: {notification.priority}</span>
          )}
        </div>
        {notification.duration && (
          <div className="w-full bg-gray-200 rounded-full h-1 dark:bg-gray-700 mt-2">
            <div
              className="bg-blue-600 h-1 rounded-full transition-all duration-1000 ease-linear"
              style={{ width: `${(timeLeft / (notification.duration / 1000)) * 100}%` }}
            ></div>
          </div>
        )}
      </div>

      <button
        onClick={(e) => {
          e.stopPropagation();
          clearNotification(notification.id);
        }}
        className="flex-shrink-0 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
      >
        <X className="w-4 h-4 text-muted-foreground" />
      </button>
    </div>
  );
};

const ToastContainer = () => {
  const { notifications } = useNotifications();
  const { userSettings } = useSettings();

  if (notifications.length === 0) return null;

  // Provide a default for userSettings.notifications if it's undefined
  const effectiveUserSettings = userSettings || {};
  const effectiveNotificationsSettings = effectiveUserSettings.notifications || {};

  const positionClasses = {
    'top-right': 'top-4 right-4',
    'top-center': 'top-4 left-1/2 -translate-x-1/2',
    'top-left': 'top-4 left-4',
    'bottom-right': 'bottom-4 right-4',
    'bottom-center': 'bottom-4 left-1/2 -translate-x-1/2',
    'bottom-left': 'bottom-4 left-4',
  };

  const containerPositionClass = positionClasses[effectiveNotificationsSettings.position] || 'bottom-4 right-4';

  // Group notifications by source and category
  const groupedNotifications = notifications.reduce((acc, notification) => {
    const key = `${notification.source || 'system'}-${notification.category || 'general'}`;
    if (!acc[key]) {
      acc[key] = { source: notification.source || 'system', category: notification.category || 'general', notifications: [] };
    }
    acc[key].notifications.push(notification);
    return acc;
  }, {});

  return (
    <div className={`fixed z-50 space-y-4 max-w-sm w-full ${containerPositionClass}`}>
      {Object.values(groupedNotifications).map((group, groupIndex) => (
        <div key={groupIndex} className="bg-card p-3 rounded-lg shadow-lg border">
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-semibold text-sm text-foreground capitalize">
              {group.source} - {group.category}
            </h4>
            <span className="text-xs text-muted-foreground">{group.notifications.length} new</span>
          </div>
          <div className="space-y-2">
            {group.notifications.map((notification) => (
              <Toast key={notification.id} notification={notification} />
            ))}
          </div>
        </div>
      ))}
    </div>
  );
};

export default ToastContainer;
