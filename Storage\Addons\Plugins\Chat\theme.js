// Enhanced Chat Theme Configuration
export const chatTheme = {
  colors: {
    primary: {
      50: '#eff6ff',
      100: '#dbeafe', 
      200: '#bfdbfe',
      300: '#93c5fd',
      400: '#60a5fa',
      500: '#3b82f6',
      600: '#2563eb',
      700: '#1d4ed8',
      800: '#1e40af',
      900: '#1e3a8a',
    },
    gray: {
      50: '#f9fafb',
      100: '#f3f4f6',
      200: '#e5e7eb',
      300: '#d1d5db',
      400: '#9ca3af',
      500: '#6b7280',
      600: '#4b5563',
      700: '#374151',
      800: '#1f2937',
      900: '#111827',
    },
    success: {
      50: '#f0fdf4',
      100: '#dcfce7',
      500: '#22c55e',
      600: '#16a34a',
    },
    warning: {
      50: '#fffbeb',
      100: '#fef3c7',
      500: '#f59e0b',
      600: '#d97706',
    },
    error: {
      50: '#fef2f2',
      100: '#fee2e2',
      500: '#ef4444',
      600: '#dc2626',
    }
  },
  
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
    '2xl': '3rem',
  },
  
  borderRadius: {
    sm: '0.25rem',
    md: '0.375rem',
    lg: '0.5rem',
    xl: '0.75rem',
    '2xl': '1rem',
    full: '9999px',
  },
  
  fontSize: {
    xs: '0.75rem',
    sm: '0.875rem',
    base: '1rem',
    lg: '1.125rem',
    xl: '1.25rem',
    '2xl': '1.5rem',
    '3xl': '1.875rem',
  },
  
  fontWeight: {
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700',
  },
  
  shadows: {
    sm: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
    md: '0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1)',
    lg: '0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1)',
    xl: '0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1)',
  },
  
  animation: {
    duration: {
      fast: '150ms',
      normal: '300ms',
      slow: '500ms',
    },
    easing: {
      ease: 'ease',
      easeIn: 'ease-in',
      easeOut: 'ease-out',
      easeInOut: 'ease-in-out',
    }
  },
  
  components: {
    button: {
      variants: {
        primary: {
          background: 'var(--chat-primary)',
          color: 'var(--chat-primary-foreground)',
          hover: {
            background: 'var(--chat-primary) / 0.9',
          }
        },
        secondary: {
          background: 'var(--chat-secondary)',
          color: 'var(--chat-secondary-foreground)',
          hover: {
            background: 'var(--chat-secondary) / 0.8',
          }
        },
        ghost: {
          background: 'transparent',
          color: 'var(--chat-foreground)',
          hover: {
            background: 'var(--chat-accent)',
            color: 'var(--chat-accent-foreground)',
          }
        },
        outline: {
          background: 'transparent',
          color: 'var(--chat-foreground)',
          border: '1px solid var(--chat-border)',
          hover: {
            background: 'var(--chat-accent)',
            color: 'var(--chat-accent-foreground)',
          }
        }
      }
    },
    
    input: {
      base: {
        background: 'var(--chat-background)',
        color: 'var(--chat-foreground)',
        border: '1px solid var(--chat-border)',
        borderRadius: 'var(--radius)',
        padding: '0.5rem 0.75rem',
        fontSize: '0.875rem',
        focus: {
          borderColor: 'var(--chat-ring)',
          boxShadow: '0 0 0 2px var(--chat-ring) / 0.2',
        }
      }
    },
    
    card: {
      base: {
        background: 'var(--chat-card)',
        color: 'var(--chat-card-foreground)',
        border: '1px solid var(--chat-border)',
        borderRadius: 'var(--radius)',
        padding: '1rem',
        boxShadow: '0 1px 3px 0 rgb(0 0 0 / 0.1)',
      }
    },
    
    message: {
      user: {
        background: 'linear-gradient(135deg, #3b82f6, #2563eb)',
        color: '#ffffff',
        borderRadius: '1rem 1rem 0.25rem 1rem',
        padding: '0.75rem 1rem',
        maxWidth: '85%',
        marginLeft: 'auto',
      },
      assistant: {
        background: 'var(--chat-card)',
        color: 'var(--chat-card-foreground)',
        border: '1px solid var(--chat-border)',
        borderRadius: '1rem 1rem 1rem 0.25rem',
        padding: '0.75rem 1rem',
        maxWidth: '85%',
      },
      system: {
        background: 'var(--chat-warning) / 0.1',
        color: 'var(--chat-warning)',
        border: '1px solid var(--chat-warning) / 0.2',
        borderRadius: '0.5rem',
        padding: '0.5rem 0.75rem',
        fontSize: '0.875rem',
        textAlign: 'center',
      }
    }
  }
};

export default chatTheme;