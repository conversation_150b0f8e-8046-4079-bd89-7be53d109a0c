import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import saveManager from '../utils/SaveManager';
import { useNotifications } from './NotificationContext';

const UIStateContext = createContext();

export const useUIState = () => {
  const context = useContext(UIStateContext);
  if (!context) {
    throw new Error('useUIState must be used within a UIStateProvider');
  }
  return context;
};

const DEFAULT_UI_STATE = {
  active_tab: 'chat',
  sidebar_collapsed: false,
  window_size: { width: 1200, height: 800 },
  last_visited_settings_section: 'appearance',
  theme_panel_open: false,
  notification_settings: {
    position: 'bottom-right',
    duration: 3000,
    enabled: true
  }
};

export function UIStateProvider({ children }) {
  const { addNotification } = useNotifications();
  const [uiState, setUIState] = useState(DEFAULT_UI_STATE);

  // Load UI state on mount
  useEffect(() => {
    const loadUIState = async () => {
      try {
        const loadedState = await saveManager.load('ui_state', DEFAULT_UI_STATE);
        setUIState(loadedState);
        console.log('✅ UIState: Loaded UI state', loadedState);
      } catch (error) {
        console.error('❌ UIState: Failed to load UI state:', error);
        addNotification({
          type: 'warning',
          message: 'Failed to load UI preferences'
        });
      }
    };

    loadUIState();
  }, [addNotification]);

  // Save UI state immediately when it changes
  const updateUIState = useCallback(async (updates) => {
    try {
      const newState = { ...uiState, ...updates };
      setUIState(newState);

      // Save immediately
      const success = await saveManager.save('ui_state', newState, {
        immediate: true,
        showNotification: false // Don't spam notifications for UI state
      });

      if (success) {
        console.log('✅ UIState: Saved UI state', updates);
      }

      return success;
    } catch (error) {
      console.error('❌ UIState: Failed to save UI state:', error);
      return false;
    }
  }, [uiState]);

  // Specific update functions for common UI operations
  const setActiveTab = useCallback((tab) => {
    updateUIState({ active_tab: tab });
  }, [updateUIState]);

  const toggleSidebar = useCallback(() => {
    updateUIState({ sidebar_collapsed: !uiState.sidebar_collapsed });
  }, [updateUIState, uiState.sidebar_collapsed]);

  const setLastVisitedSettingsSection = useCallback((section) => {
    updateUIState({ last_visited_settings_section: section });
  }, [updateUIState]);

  const setWindowSize = useCallback((size) => {
    updateUIState({ window_size: size });
  }, [updateUIState]);

  const value = {
    uiState,
    updateUIState,
    setActiveTab,
    toggleSidebar,
    setLastVisitedSettingsSection,
    setWindowSize
  };

  return (
    <UIStateContext.Provider value={value}>
      {children}
    </UIStateContext.Provider>
  );
}
