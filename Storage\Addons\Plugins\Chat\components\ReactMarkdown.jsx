import React from 'react';

// Simple markdown renderer for the chat plugin
const ReactMarkdown = ({ children, components = {} }) => {
  if (!children) return null;

  const text = typeof children === 'string' ? children : String(children);
  
  // Simple markdown parsing
  const parseMarkdown = (text) => {
    let html = text;
    
    // Code blocks
    html = html.replace(/```(\w+)?\n?([\s\S]*?)```/g, (match, lang, code) => {
      const language = lang || '';
      if (components.code) {
        return `<pre data-lang="${language}"><code class="language-${language}">${code.trim()}</code></pre>`;
      }
      return `<pre><code class="language-${language}">${code.trim()}</code></pre>`;
    });
    
    // Inline code
    html = html.replace(/`([^`]+)`/g, '<code class="inline-code">$1</code>');
    
    // Bold
    html = html.replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>');
    html = html.replace(/__([^_]+)__/g, '<strong>$1</strong>');
    
    // Italic
    html = html.replace(/\*([^*]+)\*/g, '<em>$1</em>');
    html = html.replace(/_([^_]+)_/g, '<em>$1</em>');
    
    // Headers
    html = html.replace(/^### (.+)$/gm, '<h3>$1</h3>');
    html = html.replace(/^## (.+)$/gm, '<h2>$1</h2>');
    html = html.replace(/^# (.+)$/gm, '<h1>$1</h1>');
    
    // Links
    html = html.replace(/\[([^\]]+)\]\(([^)]+)\)/g, '<a href="$2" target="_blank" rel="noopener noreferrer">$1</a>');
    
    // Line breaks
    html = html.replace(/\n/g, '<br>');
    
    return html;
  };

  // Custom component rendering
  const renderWithComponents = (html) => {
    if (!components.code) {
      return <div dangerouslySetInnerHTML={{ __html: html }} />;
    }

    // Handle code blocks with custom component
    const parts = html.split(/(<pre[^>]*><code[^>]*>[\s\S]*?<\/code><\/pre>)/);
    
    return (
      <div>
        {parts.map((part, index) => {
          const codeMatch = part.match(/<pre[^>]*><code class="language-(\w+)">([\s\S]*?)<\/code><\/pre>/);
          if (codeMatch) {
            const [, language, code] = codeMatch;
            return React.createElement(components.code, {
              key: index,
              inline: false,
              className: `language-${language}`,
              children: code
            });
          }
          
          const inlineCodeMatch = part.match(/<code class="inline-code">([^<]+)<\/code>/);
          if (inlineCodeMatch) {
            const [, code] = inlineCodeMatch;
            return React.createElement(components.code, {
              key: index,
              inline: true,
              children: code
            });
          }
          
          return <span key={index} dangerouslySetInnerHTML={{ __html: part }} />;
        })}
      </div>
    );
  };

  const parsedHtml = parseMarkdown(text);
  return renderWithComponents(parsedHtml);
};

export default ReactMarkdown;