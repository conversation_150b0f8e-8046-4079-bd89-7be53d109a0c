import React, { useState } from 'react';
import { useChat } from '../hooks/useChat';

const MessageInput = () => {
  const [inputValue, setInputValue] = useState('');
  const { sendMessage } = useChat();

  const handleSubmit = (e) => {
    e.preventDefault();
    if (inputValue.trim()) {
      sendMessage(inputValue);
      setInputValue('');
    }
  };

  return (
    <form onSubmit={handleSubmit} className="p-4 border-t">
      <input
        type="text"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        placeholder="Type a message..."
        className="w-full p-2 border rounded-lg"
      />
    </form>
  );
};

export default MessageInput;