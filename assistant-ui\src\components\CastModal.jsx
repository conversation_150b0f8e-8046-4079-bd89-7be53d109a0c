import React, { useEffect, useCallback } from 'react';
import PropTypes from 'prop-types';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTimes, 
  faSpinner, 
  faSyncAlt, 
  faCheck, 
  faTv,
  faDesktop,
  faMobileScreen,
  faVolumeHigh,
  faExclamationTriangle
} from '@fortawesome/free-solid-svg-icons';
import { useCastController } from '../hooks/useCastController';

function CastModalComponent({ isOpen, onClose, onSelectDevice, selectedDevice: initialSelectedDevice }) {
  const {
    devices,
    selectedDevice,
    setSelectedDevice,
    activeSession,
    isLoading,
    error: castError,
    isCasting,
    startCasting,
    stopCasting,
    refreshDevices: discoverDevices,
    closeCastModal
  } = useCastController();
  
  // Update selected device when initialSelectedDevice changes
  useEffect(() => {
    if (initialSelectedDevice) {
      setSelectedDevice(initialSelectedDevice);
    }
  }, [initialSelectedDevice, setSelectedDevice]);
  
  // Notify parent when device is selected
  useEffect(() => {
    if (selectedDevice && onSelectDevice) {
      onSelectDevice(selectedDevice);
    }
  }, [selectedDevice, onSelectDevice]);

  // Refresh devices when modal opens
  useEffect(() => {
    if (isOpen) {
      discoverDevices();
    }
  }, [isOpen, discoverDevices]);

  // Ensure controller state resets when modal is closed externally
  useEffect(() => {
    if (!isOpen) {
      // Clear errors, selected device, and intervals managed by the controller
      closeCastModal();
    }
  }, [isOpen, closeCastModal]);

  const handleClose = useCallback(() => {
    // Reset controller state then notify parent
    closeCastModal();
    if (onClose) onClose();
  }, [closeCastModal, onClose]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      // Any cleanup needed when component unmounts
    };
  }, []);
  
  // Prepare content for display
  const contentToCast = {
    type: 'tab',
    url: window.location.href,
    title: document.title
  };
  
  // Handle device selection and start casting
  const handleSelectDevice = useCallback(async (device) => {
    if (!device) return;
    
    try {
      await startCasting(device);
    } catch (err) {
      console.error('Error in handleSelectDevice:', err);
    }
  }, [startCasting]);
  
  // Handle stop casting
  const handleStopCasting = useCallback(async () => {
    try {
      await stopCasting();
    } catch (err) {
      console.error('Error in handleStopCasting:', err);
    }
  }, [stopCasting]);

  // Auto-refresh devices while open
  useEffect(() => {
    if (!isOpen) return;

    const interval = setInterval(discoverDevices, 10000);
    return () => clearInterval(interval);
  }, [isOpen, discoverDevices]);

  // Component render
  return isOpen ? (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col">
        <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Cast to Device</h2>
          <button
            onClick={handleClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300"
            aria-label="Close"
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
        
        <div className="p-4 overflow-y-auto flex-1">
          {castError && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md flex items-start">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mt-0.5 mr-2 flex-shrink-0" />
              <span>{castError}</span>
            </div>
          )}
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {activeSession ? 'Currently Casting' : 'Available Devices'}
              </h3>
              <button
                onClick={discoverDevices}
                disabled={isLoading || isCasting}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50"
              >
                <FontAwesomeIcon icon={isLoading ? faSpinner : faSyncAlt} spin={isLoading} className="mr-1" />
                Refresh
              </button>
            </div>
            
            {activeSession && (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/30 rounded-lg border border-blue-100 dark:border-blue-800/50">
                <div className="flex items-center">
                  <FontAwesomeIcon 
                    icon={activeSession.device.icon || faTv} 
                    className="text-blue-500 mr-3 text-lg" 
                  />
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 dark:text-white truncate">
                      {activeSession.device.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400">
                      Casting {activeSession?.content?.title || 'Current tab'}
                    </p>
                    {activeSession && (
                      <button
                        onClick={handleStopCasting}
                        disabled={isCasting}
                        className="ml-2 px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed"
                      >
                        {isCasting ? 'Stopping...' : 'Stop'}
                      </button>
                    )}
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {!activeSession && (
            <>
              {isLoading && devices.length === 0 ? (
                <div className="flex justify-center items-center py-8">
                  <FontAwesomeIcon icon={faSpinner} spin className="text-blue-500 text-2xl" />
                  <span className="ml-2 text-gray-600 dark:text-gray-400">Searching for devices...</span>
                </div>
              ) : devices.length === 0 ? (
                <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                  <FontAwesomeIcon icon={faTv} className="text-3xl mb-2 opacity-50" />
                  <p className="font-medium">No devices found</p>
                  <p className="text-sm mt-1">Make sure your devices are on the same network</p>
                  <button
                    onClick={discoverDevices}
                    className="mt-3 px-4 py-2 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md inline-flex items-center"
                  >
                    <FontAwesomeIcon icon={faSyncAlt} className="mr-2" />
                    Try Again
                  </button>
                </div>
              ) : (
                <ul className="space-y-2 max-h-60 overflow-y-auto pr-1 -mr-1">
                  {devices.map((device) => {
                    const isSelected = selectedDevice?.id === device.id;
                    const isPending = isSelected && isCasting && !activeSession;
                    const Icon = device.icon || faTv;
                    
                    return (
                      <li key={device.id}>
                        <button
                          onClick={() => handleSelectDevice(device)}
                          disabled={isPending}
                          className={`w-full text-left p-3 rounded-md transition-all ${
                            isSelected
                              ? 'bg-blue-100 dark:bg-blue-900/50 border border-blue-200 dark:border-blue-800'
                              : 'hover:bg-gray-100 dark:hover:bg-gray-700/50 border border-transparent'
                          } ${isCasting && !isSelected ? 'opacity-50' : ''}`}
                        >
                          <div className="flex items-center">
                            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3 flex-shrink-0">
                              <FontAwesomeIcon 
                                icon={Icon} 
                                className={`${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500'}`} 
                              />
                            </div>
                            <div className="flex-1 min-w-0">
                              <p className="font-medium text-gray-900 dark:text-white truncate">
                                {device.name}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                <span className="capitalize">{device.type}</span>
                                <span className="mx-1">•</span>
                                <span className="inline-flex items-center">
                                  <span className={`w-1.5 h-1.5 rounded-full mr-1 ${device.isLocal ? 'bg-green-500' : 'bg-amber-500'}`}></span>
                                  {device.isLocal ? 'This device' : 'Network device'}
                                </span>
                              </p>
                            </div>
                            {isSelected && (
                              <div className="ml-2 flex items-center space-x-2">
                                {isPending ? (
                                  <FontAwesomeIcon icon={faSpinner} spin className="text-blue-500" />
                                ) : (
                                  <FontAwesomeIcon icon={faCheck} className="text-blue-500" />
                                )}
                              </div>
                            )}
                          </div>
                        </button>
                      </li>
                    );
                  })}
                </ul>
              )}
            </>
          )}
        </div>
      </div>
    </div>
  ) : null;
}

// Add prop types for better type checking
CastModalComponent.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired,
  onSelectDevice: PropTypes.func,
  selectedDevice: PropTypes.shape({
    id: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    type: PropTypes.string,
    isLocal: PropTypes.bool,
    icon: PropTypes.any
  })
};

CastModalComponent.defaultProps = {
  onSelectDevice: null,
  selectedDevice: null
};

const CastModal = React.memo(CastModalComponent);
export default CastModal;
