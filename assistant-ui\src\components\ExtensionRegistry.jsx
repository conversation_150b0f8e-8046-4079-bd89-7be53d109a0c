import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faFile, faDatabase, faBoxOpen, faRobot, faPlus, faCog, faRefresh,
  faTrash, faEdit, faCheck, faTimes, faQuestionCircle
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "./ui/button";
import { Card, CardHeader, CardTitle, CardDescription, CardContent, CardFooter } from "./ui/card";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "./ui/dialog";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "./ui/tabs";
import { Label } from "./ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";
import { Textarea } from "./ui/textarea";
import { Badge } from "./ui/badge";
import { StandardInput, StandardSelect } from "./ui/form-fields";

const iconOptions = {
  'file': faFile,
  'database': faDatabase,
  'box-open': faBoxOpen,
  'robot': faRobot
};

const colorOptions = {
  'blue': 'bg-blue-100 text-blue-600',
  'green': 'bg-green-100 text-green-600',
  'purple': 'bg-purple-100 text-purple-600',
  'orange': 'bg-orange-100 text-orange-600',
  'red': 'bg-red-100 text-red-600',
  'yellow': 'bg-yellow-100 text-yellow-600',
  'gray': 'bg-gray-100 text-gray-600'
};

const statusOptions = {
  'active': { label: 'Active', color: 'text-green-600' },
  'development': { label: 'Development', color: 'text-yellow-600' },
  'disabled': { label: 'Disabled', color: 'text-gray-600' }
};

function ExtensionForm({ extension, handlers, onSave, onCancel }) {
  const [formData, setFormData] = useState({
    name: '',
    extension: '',
    description: '',
    file_type: 'Text',
    handler: '',
    status: 'active',
    icon: 'file',
    color: 'blue',
    metadata: {}
  });

  useEffect(() => {
    if (extension) {
      setFormData(extension);
    }
  }, [extension]);

  const handleChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    onSave(formData);
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="space-y-2">
        <StandardInput
          label="Extension Name"
          id="name"
          value={formData.name}
          onChange={(e) => handleChange('name', e.target.value)}
          placeholder="e.g., Prompt"
          required
        />
      </div>
      
      <div className="space-y-2">
        <StandardInput
          label="File Extension"
          id="extension"
          value={formData.extension}
          onChange={(e) => handleChange('extension', e.target.value)}
          placeholder="e.g., .prompt"
          required
          description="Must start with a period (e.g., .prompt)"
        />
      </div>
      
      <div className="space-y-2">
        <Label htmlFor="description">Description</Label>
        <Textarea
          id="description"
          value={formData.description}
          onChange={(e) => handleChange('description', e.target.value)}
          placeholder="Describe what this extension is used for"
          required
          className="min-h-24"
        />
      </div>
      
      <div className="grid grid-cols-2 gap-4">
        <StandardSelect
          label="File Type"
          id="file_type"
          value={formData.file_type}
          onChange={(value) => handleChange('file_type', value)}
          options={[
            { value: 'Text', label: 'Text' },
            { value: 'JSON', label: 'JSON' },
            { value: 'Binary', label: 'Binary' },
            { value: 'XML', label: 'XML' },
            { value: 'YAML', label: 'YAML' }
          ]}
        />
        
        <StandardSelect
          label="Handler"
          id="handler"
          value={formData.handler}
          onChange={(value) => handleChange('handler', value)}
          options={handlers.map(handler => ({ 
            value: handler.name, 
            label: handler.name 
          }))}
        />
      </div>
      
      <div className="grid grid-cols-3 gap-4">
        <StandardSelect
          label="Status"
          id="status"
          value={formData.status}
          onChange={(value) => handleChange('status', value)}
          options={[
            { value: 'active', label: 'Active' },
            { value: 'development', label: 'Development' },
            { value: 'disabled', label: 'Disabled' }
          ]}
        />
        
        <StandardSelect
          label="Icon"
          id="icon"
          value={formData.icon}
          onChange={(value) => handleChange('icon', value)}
          options={[
            { value: 'file', label: 'File' },
            { value: 'database', label: 'Database' },
            { value: 'box-open', label: 'Box' },
            { value: 'robot', label: 'Robot' }
          ]}
        />
        
        <StandardSelect
          label="Color"
          id="color"
          value={formData.color}
          onChange={(value) => handleChange('color', value)}
          options={[
            { value: 'blue', label: 'Blue' },
            { value: 'green', label: 'Green' },
            { value: 'purple', label: 'Purple' },
            { value: 'orange', label: 'Orange' },
            { value: 'red', label: 'Red' },
            { value: 'yellow', label: 'Yellow' },
            { value: 'gray', label: 'Gray' }
          ]}
        />
      </div>

      <DialogFooter>
        <Button type="button" variant="outline" onClick={onCancel}>
          Cancel
        </Button>
        <Button type="submit">Save Extension</Button>
      </DialogFooter>
    </form>
  );
}

function ExtensionCard({ extension, onEdit, onDelete }) {
  const iconKey = extension.icon || 'file';
  const colorKey = extension.color || 'blue';
  const statusKey = extension.status || 'active';
  
  const icon = iconOptions[iconKey] || faFile;
  const colorClass = colorOptions[colorKey] || colorOptions.blue;
  const status = statusOptions[statusKey] || statusOptions.active;
  
  return (
    <Card className="hover:shadow-lg transition-shadow cursor-pointer border-blue-200">
      <CardHeader className="text-center">
        <div className={`mx-auto w-16 h-16 rounded-full flex items-center justify-center mb-4 ${colorClass.split(' ')[0]}`}>
          <FontAwesomeIcon icon={icon} className={`h-8 w-8 ${colorClass.split(' ')[1]}`} />
        </div>
        <CardTitle className="text-lg">{extension.extension}</CardTitle>
        <CardDescription className="text-sm">
          {extension.description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2 text-xs text-muted-foreground">
          <div className="flex justify-between">
            <span>File Type:</span>
            <span className="font-mono bg-blue-50 px-2 py-1 rounded">{extension.file_type}</span>
          </div>
          <div className="flex justify-between">
            <span>Handler:</span>
            <span>{extension.handler}</span>
          </div>
          <div className="flex justify-between">
            <span>Status:</span>
            <span className={status.color}>
              {statusKey === 'active' && '✓ '}
              {statusKey === 'development' && '⚠ '}
              {status.label}
            </span>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end space-x-2 pt-0">
        <Button variant="outline" size="sm" onClick={() => onEdit(extension)}>
          <FontAwesomeIcon icon={faEdit} className="h-3 w-3 mr-1" />
          Edit
        </Button>
        <Button variant="outline" size="sm" className="text-destructive" onClick={() => onDelete(extension)}>
          <FontAwesomeIcon icon={faTrash} className="h-3 w-3 mr-1" />
          Delete
        </Button>
      </CardFooter>
    </Card>
  );
}

export default function ExtensionRegistry() {
  const [registry, setRegistry] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({});
  
  const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
  const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  
  const [currentExtension, setCurrentExtension] = useState(null);
  
  useEffect(() => {
    loadRegistry();
  }, []);
  
  const loadRegistry = async () => {
    setIsLoading(true);
    try {
      const result = await invoke('get_extensions_registry');
      setRegistry(result);
      
      // Get stats
      const statsResult = await invoke('get_extension_stats');
      setStats(statsResult);
      
      setError(null);
    } catch (err) {
      console.error('Error loading extensions registry:', err);
      setError(`Failed to load extensions registry: ${err}`);
    } finally {
      setIsLoading(false);
    }
  };
  
  const handleAddExtension = async (extension) => {
    try {
      await invoke('add_extension', { extension });
      await loadRegistry();
      setIsAddDialogOpen(false);
    } catch (err) {
      console.error('Error adding extension:', err);
      alert(`Failed to add extension: ${err}`);
    }
  };
  
  const handleUpdateExtension = async (extension) => {
    try {
      await invoke('update_extension', { extension });
      await loadRegistry();
      setIsEditDialogOpen(false);
      setCurrentExtension(null);
    } catch (err) {
      console.error('Error updating extension:', err);
      alert(`Failed to update extension: ${err}`);
    }
  };
  
  const handleDeleteExtension = async () => {
    if (!currentExtension) return;
    
    try {
      await invoke('remove_extension', { extension_name: currentExtension.extension });
      await loadRegistry();
      setIsDeleteDialogOpen(false);
      setCurrentExtension(null);
    } catch (err) {
      console.error('Error deleting extension:', err);
      alert(`Failed to delete extension: ${err}`);
    }
  };
  
  const openEditDialog = (extension) => {
    setCurrentExtension(extension);
    setIsEditDialogOpen(true);
  };
  
  const openDeleteDialog = (extension) => {
    setCurrentExtension(extension);
    setIsDeleteDialogOpen(true);
  };
  
  if (isLoading && !registry) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="text-center p-4 border border-destructive rounded bg-destructive/10 text-destructive">
        <p>{error}</p>
        <Button onClick={loadRegistry} className="mt-4">Try Again</Button>
      </div>
    );
  }
  
  return (
    <div className="space-y-6">
      {/* Extension Management Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Extension Management</CardTitle>
          <CardDescription>
            Configure file type associations and create new extensions
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-wrap gap-4">
            <Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
              <DialogTrigger asChild>
                <Button variant="outline" className="flex items-center space-x-2">
                  <FontAwesomeIcon icon={faPlus} className="h-4 w-4" />
                  <span>Add Extension</span>
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-md">
                <DialogHeader>
                  <DialogTitle>Add New Extension</DialogTitle>
                  <DialogDescription>
                    Create a new file extension type for the system.
                  </DialogDescription>
                </DialogHeader>
                <ExtensionForm 
                  handlers={registry?.handlers || []} 
                  onSave={handleAddExtension} 
                  onCancel={() => setIsAddDialogOpen(false)} 
                />
              </DialogContent>
            </Dialog>
            
            <Button variant="outline" className="flex items-center space-x-2">
              <FontAwesomeIcon icon={faCog} className="h-4 w-4" />
              <span>Configure Handlers</span>
            </Button>
            
            <Button variant="outline" className="flex items-center space-x-2" onClick={loadRegistry}>
              <FontAwesomeIcon icon={faRefresh} className="h-4 w-4" />
              <span>Refresh Registry</span>
            </Button>
          </div>
          
          {/* Extension Statistics */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-6">
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{stats.active || 0}</div>
              <div className="text-sm text-blue-600">Active Extensions</div>
            </div>
            <div className="text-center p-4 bg-yellow-50 rounded-lg">
              <div className="text-2xl font-bold text-yellow-600">{stats.development || 0}</div>
              <div className="text-sm text-yellow-600">In Development</div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{stats.disabled || 0}</div>
              <div className="text-sm text-red-600">Disabled</div>
            </div>
            <div className="text-center p-4 bg-purple-50 rounded-lg">
              <div className="text-2xl font-bold text-purple-600">{stats.handlers || 0}</div>
              <div className="text-sm text-purple-600">Handlers Available</div>
            </div>
          </div>
        </CardContent>
      </Card>
      
      {/* Extensions Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {registry?.extensions.map(extension => (
          <ExtensionCard
            key={extension.extension}
            extension={extension}
            onEdit={openEditDialog}
            onDelete={openDeleteDialog}
          />
        ))}
      </div>
      
      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Edit Extension</DialogTitle>
            <DialogDescription>
              Modify the extension's properties.
            </DialogDescription>
          </DialogHeader>
          {currentExtension && (
            <ExtensionForm
              extension={currentExtension}
              handlers={registry?.handlers || []}
              onSave={handleUpdateExtension}
              onCancel={() => setIsEditDialogOpen(false)}
            />
          )}
        </DialogContent>
      </Dialog>
      
      {/* Delete Confirmation Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle className="text-destructive">Delete Extension</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the "{currentExtension?.extension}" extension? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="destructive" onClick={handleDeleteExtension}>
              Delete Extension
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}