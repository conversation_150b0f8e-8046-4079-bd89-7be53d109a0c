{"name": "Cha<PERSON>", "description": "AI-powered chat interface with real-time streaming, model management, and superior user experience.", "version": "2.0.0", "app_type": "core-tool", "enabled": true, "main": "chat.jsx", "class_name": "EnhancedChat", "plugin_type": "ui_component", "author": "The Collective Team", "permissions": ["ai_client", "database", "local_storage", "streaming"], "data_directory": "./data/", "features": ["Real-time streaming responses", "Model selection and preloading", "Typing indicators", "Welcome message generation", "Error handling and recovery", "Smooth scrolling and animations", "Professional UI design", "Dedicated settings page"], "settings": {"auto_welcome": true, "enable_streaming": true, "show_model_info": true, "auto_scroll": true, "typing_indicators": true, "theme": "system", "font_size": "medium"}, "state_management": {"persistent": true, "auto_restore": true, "backup_enabled": true}}