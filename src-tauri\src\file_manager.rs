use std::path::Path;
use std::fs;
use std::io;
use rusqlite::Connection;
use crate::database;

const MAX_CONTENT_PREVIEW_LENGTH: usize = 1000; // Define a constant for max preview length

use log::{info, warn}; // Added for logging

pub fn index_file(conn: &Connection, path: &Path) -> io::Result<()> {
    let file_path_str = path.to_string_lossy().into_owned();
    let filename = path.file_name()
        .and_then(|s| s.to_str())
        .unwrap_or_else(|| {
            warn!("Could not get valid UTF-8 filename for path: {}", path.display());
            "unknown_filename"
        });
    let file_type = path.extension()
        .and_then(|s| s.to_str())
        .unwrap_or_else(|| {
            warn!("Could not get valid UTF-8 file extension for path: {}", path.display());
            "unknown_extension"
        });
    
    info!("Indexing file: {}", file_path_str);

    let content_preview = if path.is_file() {
        match fs::read(path) { // Read as bytes
            Ok(bytes) => {
                // Attempt lossy UTF-8 conversion for preview
                let content = String::from_utf8_lossy(&bytes);
                Some(content.chars().take(MAX_CONTENT_PREVIEW_LENGTH).collect::<String>())
            },
            Err(e) => {
                // Log error but don't crash, return empty preview
                eprintln!("Warning: Failed to read file {} as bytes for preview: {}", path.display(), e);
                Some(format!("[Binary or unreadable content: {}]", e))
            }
        }
    } else {
        None
    };

    match database::get_file_by_path(conn, &file_path_str) {
        Ok(Some(file_id)) => {
            // File exists, update it
            database::update_file_content_preview(conn, file_id, &content_preview.unwrap_or_default())
                .map_err(|e| io::Error::new(io::ErrorKind::Other, format!("Failed to update file in DB: {}", e)))?;
            // Re-index keywords if necessary (not implemented yet)
        },
        Ok(None) => {
            // File does not exist, insert it
            let metadata = fs::metadata(path)?;
            let last_modified = metadata.modified()?.duration_since(std::time::UNIX_EPOCH)
                .unwrap_or_default().as_secs() as i64;
            let size = metadata.len() as i64;

            database::insert_file(
                conn,
                &file_path_str,
                filename,
                Some(file_type),
                last_modified,
                size,
            )
            .map_err(|e| io::Error::new(io::ErrorKind::Other, format!("Failed to insert file into DB: {}", e)))?;
        },
        Err(e) => return Err(io::Error::new(io::ErrorKind::Other, format!("DB error checking file existence: {}", e))),
    }

    Ok(())
}

pub fn scan_directory(conn: &Connection, path: &Path) -> io::Result<()> {
    info!("Scanning directory: {}", path.display());
    if path.is_dir() {
        for entry_result in fs::read_dir(path)? {
            let entry = entry_result?;
            let entry_path = entry.path();
            
            if entry_path.is_dir() {
                info!("Found directory: {}", entry_path.display());
                scan_directory(conn, &entry_path)?;
            } else {
                info!("Found file: {}", entry_path.display());
                index_file(conn, &entry_path)?;
            }
        }
    } else {
        warn!("Path is not a directory, skipping scan: {}", path.display());
    }
    Ok(())
}

pub fn read_file_content_internal(path: &Path) -> io::Result<String> {
    fs::read_to_string(path)
}

#[tauri::command]
pub fn read_file_content(file_path: String) -> Result<String, String> {
    match fs::read_to_string(&file_path) {
        Ok(content) => Ok(content),
        Err(e) => Err(format!("Failed to read file {}: {}", file_path, e)),
    }
}

#[tauri::command]
pub fn get_file_content(file_path: String) -> Result<String, String> {
    match fs::read_to_string(&file_path) {
        Ok(content) => Ok(content),
        Err(e) => Err(format!("Failed to read file {}: {}", file_path, e)),
    }
}

#[tauri::command]
pub async fn dialog_open(app_handle: tauri::AppHandle, dialog_type: String) -> Result<DialogResult, String> {
    use tauri_plugin_dialog::DialogExt;
    use std::sync::{Arc, Mutex};
    use tokio::sync::oneshot;

    let (tx, rx) = oneshot::channel();
    let tx = Arc::new(Mutex::new(Some(tx)));

    match dialog_type.as_str() {
        "openDirectory" => {
            let tx_clone = tx.clone();
            app_handle.dialog().file().pick_folder(move |folder_path| {
                let result = match folder_path {
                    Some(path) => DialogResult {
                        canceled: false,
                        file_paths: vec![path.to_string()],
                    },
                    None => DialogResult {
                        canceled: true,
                        file_paths: vec![],
                    },
                };
                if let Ok(mut tx_guard) = tx_clone.lock() {
                    if let Some(tx) = tx_guard.take() {
                        let _ = tx.send(result);
                    }
                }
            });
        },
        "openFile" => {
            let tx_clone = tx.clone();
            app_handle.dialog().file().pick_file(move |file_path| {
                let result = match file_path {
                    Some(path) => DialogResult {
                        canceled: false,
                        file_paths: vec![path.to_string()],
                    },
                    None => DialogResult {
                        canceled: true,
                        file_paths: vec![],
                    },
                };
                if let Ok(mut tx_guard) = tx_clone.lock() {
                    if let Some(tx) = tx_guard.take() {
                        let _ = tx.send(result);
                    }
                }
            });
        },
        _ => return Err(format!("Unsupported dialog type: {}", dialog_type)),
    }

    match rx.await {
        Ok(result) => Ok(result),
        Err(_) => Err("Dialog operation was cancelled or failed".to_string()),
    }
}

// Removed unused DirEntry struct - the list_directory_contents function was never implemented
// The browse_directory function uses DirectoryEntry and FileEntry instead

#[derive(serde::Serialize)]
pub struct FileEntry {
    pub name: String,
    pub path: String,
    pub size: Option<u64>,
}

#[derive(serde::Serialize)]
pub struct DirectoryEntry {
    pub name: String,
    pub path: String,
}

#[derive(serde::Serialize)]
pub struct BrowseDirectoryResult {
    pub directories: Vec<DirectoryEntry>,
    pub files: Vec<FileEntry>,
}

#[tauri::command]
pub fn browse_directory(path: String) -> Result<BrowseDirectoryResult, String> {
    let dir_path = std::path::PathBuf::from(&path);
    if !dir_path.is_dir() {
        return Err(format!("Path is not a directory: {}", dir_path.display()));
    }

    let mut directories = Vec::new();
    let mut files = Vec::new();

    for entry in std::fs::read_dir(&dir_path).map_err(|e| format!("Failed to read directory {}: {}", dir_path.display(), e))? {
        let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
        let entry_path = entry.path();
        let name = entry_path.file_name().and_then(|s| s.to_str()).unwrap_or("unknown").to_string();
        let full_path = entry_path.to_string_lossy().to_string();
        
        if entry_path.is_dir() {
            directories.push(DirectoryEntry {
                name,
                path: full_path,
            });
        } else {
            let size = entry_path.metadata()
                .map(|meta| meta.len())
                .ok();
            
            files.push(FileEntry {
                name,
                path: full_path,
                size,
            });
        }
    }

    Ok(BrowseDirectoryResult {
        directories,
        files,
    })
}

#[tauri::command]
pub fn list_model_files(dir_path: String) -> Result<Vec<ModelFileEntry>, String> {
    let path = std::path::PathBuf::from(dir_path);
    if !path.exists() {
        return Err(format!("Path does not exist: {}", path.display()));
    }

    if !path.is_dir() {
        return Err(format!("Path is not a directory: {}", path.display()));
    }

    let mut entries = Vec::new();

    // Common LLM model file extensions
    let model_extensions = [
        "gguf", "ggml", "bin", "safetensors", "pt", "pth", "pkl", "h5",
        "tflite", "onnx", "pb", "json", "txt", "tokenizer", "vocab",
        "model", "weights", "checkpoint", "ckpt", "params", "config"
    ];

    fn scan_directory_recursive(dir: &std::path::Path, entries: &mut Vec<ModelFileEntry>, model_extensions: &[&str]) -> Result<(), String> {
        for entry in std::fs::read_dir(dir).map_err(|e| format!("Failed to read directory {}: {}", dir.display(), e))? {
            let entry = entry.map_err(|e| format!("Failed to read directory entry: {}", e))?;
            let entry_path = entry.path();
            let name = entry_path.file_name().and_then(|s| s.to_str()).unwrap_or("unknown").to_string();

            if entry_path.is_dir() {
                // Add directory
                entries.push(ModelFileEntry {
                    name: name.clone(),
                    path: entry_path.to_string_lossy().into_owned(),
                    is_dir: true,
                    file_type: "folder".to_string(),
                    size: 0,
                    is_model_file: false,
                });

                // Recursively scan subdirectories
                scan_directory_recursive(&entry_path, entries, model_extensions)?;
            } else {
                // Check if it's a model file
                let extension = entry_path.extension()
                    .and_then(|s| s.to_str())
                    .unwrap_or("")
                    .to_lowercase();

                let is_model_file = model_extensions.contains(&extension.as_str()) ||
                    name.to_lowercase().contains("model") ||
                    name.to_lowercase().contains("tokenizer") ||
                    name.to_lowercase().contains("vocab") ||
                    name.to_lowercase().contains("config");

                let size = entry_path.metadata()
                    .map(|m| m.len())
                    .unwrap_or(0);

                entries.push(ModelFileEntry {
                    name,
                    path: entry_path.to_string_lossy().into_owned(),
                    is_dir: false,
                    file_type: if extension.is_empty() { "unknown".to_string() } else { extension },
                    size,
                    is_model_file,
                });
            }
        }
        Ok(())
    }

    scan_directory_recursive(&path, &mut entries, &model_extensions)?;

    // Sort entries: directories first, then model files, then other files
    entries.sort_by(|a, b| {
        match (a.is_dir, b.is_dir) {
            (true, false) => std::cmp::Ordering::Less,
            (false, true) => std::cmp::Ordering::Greater,
            _ => match (a.is_model_file, b.is_model_file) {
                (true, false) => std::cmp::Ordering::Less,
                (false, true) => std::cmp::Ordering::Greater,
                _ => a.name.cmp(&b.name),
            }
        }
    });

    Ok(entries)
}

#[derive(serde::Serialize)]
pub struct ModelFileEntry {
    pub name: String,
    pub path: String,
    pub is_dir: bool,
    pub file_type: String,
    pub size: u64,
    pub is_model_file: bool,
}

#[derive(serde::Serialize)]
pub struct DialogResult {
    pub canceled: bool,
    pub file_paths: Vec<String>,
}

#[tauri::command]
pub async fn save_file_content(file_path: String, content: String) -> Result<String, String> {
    use std::fs;
    match fs::write(&file_path, content) {
        Ok(_) => Ok(format!("Successfully saved content to: {}", file_path)),
        Err(e) => Err(format!("Failed to save file {}: {}", file_path, e)),
    }
}

#[tauri::command]
pub async fn delete_file(file_path: String) -> Result<String, String> {
    use std::fs;
    let path = std::path::Path::new(&file_path);
    
    let result = if path.is_dir() {
        fs::remove_dir_all(&file_path)
    } else {
        fs::remove_file(&file_path)
    };
    
    match result {
        Ok(_) => Ok(format!("Successfully deleted: {}", file_path)),
        Err(e) => Err(format!("Failed to delete {}: {}", file_path, e)),
    }
}

#[tauri::command]
pub async fn copy_file(source_path: String, destination_path: String) -> Result<String, String> {
    use std::fs;
    let source = std::path::Path::new(&source_path);
    let destination = std::path::Path::new(&destination_path);
    
    // Create parent directory if it doesn't exist
    if let Some(parent) = destination.parent() {
        if !parent.exists() {
            if let Err(e) = fs::create_dir_all(parent) {
                return Err(format!("Failed to create destination directory: {}", e));
            }
        }
    }
    
    let result = if source.is_dir() {
        // Copy directory recursively
        copy_dir_recursive(source, destination)
    } else {
        // Copy single file
        fs::copy(source, destination).map(|_| ())
    };
    
    match result {
        Ok(_) => Ok(format!("Successfully copied {} to {}", source_path, destination_path)),
        Err(e) => Err(format!("Failed to copy {} to {}: {}", source_path, destination_path, e)),
    }
}

#[tauri::command]
pub async fn move_file(source_path: String, destination_path: String) -> Result<String, String> {
    use std::fs;
    let source = std::path::Path::new(&source_path);
    let destination = std::path::Path::new(&destination_path);
    
    // Create parent directory if it doesn't exist
    if let Some(parent) = destination.parent() {
        if !parent.exists() {
            if let Err(e) = fs::create_dir_all(parent) {
                return Err(format!("Failed to create destination directory: {}", e));
            }
        }
    }
    
    match fs::rename(source, destination) {
        Ok(_) => Ok(format!("Successfully moved {} to {}", source_path, destination_path)),
        Err(_rename_err) => {
            // If rename fails (e.g., cross-device), try copy + delete
            match copy_file(source_path.clone(), destination_path.clone()).await {
                Ok(_) => {
                    match delete_file(source_path.clone()).await {
                        Ok(_) => Ok(format!("Successfully moved {} to {}", source_path, destination_path)),
                        Err(e) => Err(format!("Copied but failed to delete source {}: {}", source_path, e)),
                    }
                },
                Err(e) => Err(format!("Failed to move {} to {}: {}", source_path, destination_path, e)),
            }
        }
    }
}

#[tauri::command]
pub async fn rename_file(file_path: String, new_name: String) -> Result<String, String> {
    use std::fs;
    let source = std::path::Path::new(&file_path);
    
    if let Some(parent) = source.parent() {
        let destination = parent.join(&new_name);
        match fs::rename(source, &destination) {
            Ok(_) => Ok(format!("Successfully renamed {} to {}", file_path, new_name)),
            Err(e) => Err(format!("Failed to rename {} to {}: {}", file_path, new_name, e)),
        }
    } else {
        Err("Cannot determine parent directory".to_string())
    }
}

// Helper function for recursive directory copying
fn copy_dir_recursive(src: &std::path::Path, dst: &std::path::Path) -> std::io::Result<()> {
    use std::fs;
    
    fs::create_dir_all(dst)?;
    
    for entry in fs::read_dir(src)? {
        let entry = entry?;
        let entry_path = entry.path();
        let file_name = entry.file_name();
        let dest_path = dst.join(&file_name);
        
        if entry_path.is_dir() {
            copy_dir_recursive(&entry_path, &dest_path)?;
        } else {
            fs::copy(&entry_path, &dest_path)?;
        }
    }
    
    Ok(())
}

#[tauri::command]
pub async fn open_file_in_explorer(file_path: String) -> Result<String, String> {
    // Placeholder: Simulate opening a file in explorer
    Ok(format!("Opened file in explorer: {}", file_path))
}
