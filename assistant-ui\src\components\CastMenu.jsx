import React, { useState, useEffect, useRef, useCallback } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faTimes, 
  faCheck, 
  faExclamationTriangle,
  faSpinner,
  faSyncAlt,
  faTv
} from '@fortawesome/free-solid-svg-icons';
import PropTypes from 'prop-types';
import { useCastController } from '../hooks/useCastController';

const CastMenu = ({ isOpen, onClose }) => {
  const {
    devices,
    selectedDevice,
    setSelectedDevice,
    activeSession,
    isLoading,
    error: castError,
    isCasting,
    startCasting,
    stopCasting,
    refreshDevices: discoverDevices
  } = useCastController();
  
  const modalRef = useRef(null);
  const isMounted = useRef(true);
  
  // Handle device selection and start casting
  const handleSelectDevice = useCallback(async (device, event) => {
    // Stop event propagation to prevent triggering parent handlers
    event?.stopPropagation();
    event?.preventDefault();
    
    // Prevent multiple clicks
    if (!device || isCasting || (selectedDevice?.id === device.id && activeSession)) {
      return;
    }
    
    try {
      await startCasting(device);
    } catch (err) {
      console.error('Error starting cast:', err);
      // Error is already set in the controller
    }
  }, [isCasting, startCasting, selectedDevice, activeSession]);

  // Handle stopping the current cast
  const handleStopCasting = useCallback(async () => {
    if (!activeSession) return;
    
    try {
      await stopCasting();
      // Don't close the menu after stopping
    } catch (err) {
      console.error('Error stopping cast:', err);
      // Error is already set in the controller
    }
  }, [activeSession, stopCasting]);
  
  // Refresh devices when menu opens
  useEffect(() => {
    if (isOpen) {
      discoverDevices();
    }
  }, [isOpen, discoverDevices]);
  
  // Define handleEscape first
  const handleEscape = useCallback((e) => {
    if (e.key === 'Escape') {
      e.stopPropagation(); // Prevent bubbling to parent components
      onClose();
    }
  }, [onClose]);

  // Clean up on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);
  
  // Close on Escape key
  useEffect(() => {
    if (!isOpen) return;
    
    // Use capture phase to ensure we catch the event first
    document.addEventListener('keydown', handleEscape, true);
    return () => {
      document.removeEventListener('keydown', handleEscape, true);
    };
  }, [isOpen, handleEscape]);
  
  // Close on click outside
  const handleClickOutside = useCallback((e) => {
    // Only close if the click is outside our modal and not on the cast button
    const castButton = document.querySelector('[data-cast-button]');
    const isCastButton = castButton && (castButton === e.target || castButton.contains(e.target));
    
    if (modalRef.current && !modalRef.current.contains(e.target) && !isCastButton) {
      onClose();
    }
  }, [onClose]);
  
  useEffect(() => {
    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
      return () => document.removeEventListener('mousedown', handleClickOutside);
    }
  }, [isOpen, handleClickOutside]);
  
  if (!isOpen) return null;
  
  return (
    <div 
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4 backdrop-blur-sm"
      data-cast-menu="true"
      role="dialog"
      aria-modal="true"
      aria-labelledby="cast-menu-title"
    >
      <div 
        ref={modalRef}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md max-h-[80vh] flex flex-col overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b dark:border-gray-700">
          <h2 id="cast-menu-title" className="text-xl font-semibold text-gray-900 dark:text-white">
            {activeSession ? 'Casting to Device' : 'Cast to Device'}
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 p-1"
            aria-label="Close"
          >
            <FontAwesomeIcon icon={faTimes} className="w-5 h-5" />
          </button>
        </div>
        
        {/* Content */}
        <div className="p-4 overflow-y-auto flex-1">
          {castError && (
            <div className="mb-4 p-3 bg-red-100 dark:bg-red-900/30 text-red-700 dark:text-red-300 rounded-md flex items-start">
              <FontAwesomeIcon icon={faExclamationTriangle} className="mt-0.5 mr-2 flex-shrink-0" />
              <span>{castError}</span>
            </div>
          )}
          
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300">
                {activeSession ? 'Currently Casting' : 'Available Devices'}
              </h3>
              <button
                onClick={discoverDevices}
                disabled={isLoading || isCasting}
                className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 disabled:opacity-50 flex items-center"
              >
                <FontAwesomeIcon 
                  icon={isLoading ? faSpinner : faSyncAlt} 
                  spin={isLoading} 
                  className="mr-1.5" 
                />
                Refresh
              </button>
            </div>
            
            {activeSession ? (
              <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-100 dark:border-blue-800/50">
                <div className="flex items-center">
                  <div className="w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3 flex-shrink-0">
                    <FontAwesomeIcon 
                      icon={activeSession.device.icon || faTv} 
                      className="text-blue-600 dark:text-blue-400" 
                    />
                  </div>
                  <div className="flex-1 min-w-0">
                    <p className="font-medium text-gray-900 dark:text-white truncate">
                      {activeSession.device.name}
                    </p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {activeSession.content?.title || 'Current content'}
                    </p>
                    <button
                      onClick={handleStopCasting}
                      disabled={isCasting}
                      className="mt-2 px-3 py-1 text-sm bg-red-500 hover:bg-red-600 text-white rounded-md disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
                    >
                      {isCasting ? (
                        <>
                          <FontAwesomeIcon icon={faSpinner} spin className="mr-1.5" />
                          Stopping...
                        </>
                      ) : 'Stop Casting'}
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="mt-2">
                {isLoading && devices.length === 0 ? (
                  <div className="flex justify-center items-center py-8">
                    <FontAwesomeIcon icon={faSpinner} spin className="text-blue-500 text-2xl mr-3" />
                    <span className="text-gray-600 dark:text-gray-400">Searching for devices...</span>
                  </div>
                ) : devices.length === 0 ? (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <FontAwesomeIcon icon={faTv} className="text-3xl mb-2 opacity-50" />
                    <p className="font-medium">No devices found</p>
                    <p className="text-sm mt-1">Make sure your devices are on the same network</p>
                    <button
                      onClick={discoverDevices}
                      className="mt-3 px-4 py-2 text-sm bg-blue-500 hover:bg-blue-600 text-white rounded-md inline-flex items-center"
                    >
                      <FontAwesomeIcon icon={faSyncAlt} className="mr-2" />
                      Try Again
                    </button>
                  </div>
                ) : (
                  <ul className="space-y-2 max-h-60 overflow-y-auto pr-1 -mr-1">
                    {devices.map((device) => {
                      const isSelected = selectedDevice?.id === device.id;
                      const isPending = isSelected && isCasting && !activeSession;
                      
                      return (
                        <li key={device.id}>
                          <button
                            onClick={(e) => handleSelectDevice(device, e)}
                            disabled={isPending}
                            className={`w-full text-left p-3 rounded-md transition-all ${
                              isSelected
                                ? 'bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800'
                                : 'hover:bg-gray-100 dark:hover:bg-gray-700/50 border border-transparent'
                            } ${isCasting && !isSelected ? 'opacity-50' : ''} flex items-center`}
                          >
                            <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3 flex-shrink-0">
                              <FontAwesomeIcon 
                                icon={device.icon || faTv} 
                                className={`${isSelected ? 'text-blue-600 dark:text-blue-400' : 'text-gray-500'}`} 
                              />
                            </div>
                            <div className="flex-1 min-w-0 text-left">
                              <p className="font-medium text-gray-900 dark:text-white truncate">
                                {device.name}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                                <span className="capitalize">{device.type || 'device'}</span>
                                <span className="mx-1">•</span>
                                <span className="inline-flex items-center">
                                  <span className={`w-1.5 h-1.5 rounded-full mr-1 ${device.isLocal ? 'bg-green-500' : 'bg-amber-500'}`}></span>
                                  {device.isLocal ? 'This device' : 'Network device'}
                                </span>
                              </p>
                            </div>
                            {isSelected && (
                              <div className="ml-2 flex items-center space-x-2">
                                {isPending ? (
                                  <FontAwesomeIcon icon={faSpinner} spin className="text-blue-500" />
                                ) : (
                                  <FontAwesomeIcon icon={faCheck} className="text-blue-500" />
                                )}
                              </div>
                            )}
                          </button>
                        </li>
                      );
                    })}
                  </ul>
                )}
              </div>
            )}
          </div>
        </div>
        
        {/* Footer */}
        <div className="p-3 bg-gray-50 dark:bg-gray-800/50 border-t dark:border-gray-700 text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            {devices.length > 0 
              ? `${devices.length} device${devices.length === 1 ? '' : 's'} found` 
              : 'No devices available'}
          </p>
        </div>
      </div>
    </div>
  );
};

CastMenu.propTypes = {
  isOpen: PropTypes.bool.isRequired,
  onClose: PropTypes.func.isRequired
};

export default CastMenu;
