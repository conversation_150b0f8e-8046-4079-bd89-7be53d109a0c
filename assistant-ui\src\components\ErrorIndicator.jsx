import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faExclamationTriangle, faTimes } from '@fortawesome/free-solid-svg-icons';
import ErrorPopup from './ErrorPopup';

/**
 * ErrorIndicator - A small indicator in the bottom right corner that shows when errors occur
 * This maintains an error log and displays a small icon that users can click to see recent errors
 */
const ErrorIndicator = ({ 
  className = '',
  maxErrors = 10,
  autoHideTimeout = 5000
}) => {
  const [errors, setErrors] = useState([]);
  const [isOpen, setIsOpen] = useState(false);
  const [hasNewErrors, setHasNewErrors] = useState(false);
  const [selectedError, setSelectedError] = useState(null);

  // Set up the error event listener
  useEffect(() => {
    const handleError = (event) => {
      event.preventDefault(); // Prevent default browser error handling
      
      // Create a structured error object
      const newError = {
        id: Date.now(),
        timestamp: new Date(),
        message: event.message || 'Unknown error',
        source: event.filename || 'Unknown source',
        line: event.lineno,
        column: event.colno,
        error: event.error,
        stack: event.error?.stack
      };
      
      // Add the error to our log
      setErrors(prevErrors => {
        const updatedErrors = [newError, ...prevErrors.slice(0, maxErrors - 1)];
        return updatedErrors;
      });
      
      // Set the new error flag
      setHasNewErrors(true);
      
      // Auto-hide the error after a delay if it's open
      if (isOpen) {
        setTimeout(() => {
          setIsOpen(false);
        }, autoHideTimeout);
      }
    };

    // Add our window error event listener
    window.addEventListener('error', handleError);
    
    // Add unhandled promise rejection handler
    window.addEventListener('unhandledrejection', (event) => {
      const newError = {
        id: Date.now(),
        timestamp: new Date(),
        message: `Unhandled Promise Rejection: ${event.reason?.message || 'Unknown reason'}`,
        source: 'Promise',
        error: event.reason,
        stack: event.reason?.stack
      };
      
      setErrors(prevErrors => {
        const updatedErrors = [newError, ...prevErrors.slice(0, maxErrors - 1)];
        return updatedErrors;
      });
      
      setHasNewErrors(true);
    });
    
    // Cleanup
    return () => {
      window.removeEventListener('error', handleError);
      window.removeEventListener('unhandledrejection', handleError);
    };
  }, [maxErrors, isOpen, autoHideTimeout]);

  // Handle clicking on an error to show the full popup
  const showErrorPopup = (error) => {
    setSelectedError(error);
    setIsOpen(false); // Close the panel when showing the popup
  };

  // Handle clearing the errors
  const clearErrors = () => {
    setErrors([]);
    setHasNewErrors(false);
  };

  // If no errors, don't render anything
  if (errors.length === 0) {
    return null;
  }
  
  return (
    <div className={`fixed bottom-4 right-4 z-50 ${className}`}>
      {/* Error indicator button */}
      <button
        onClick={() => {
          setIsOpen(!isOpen);
          setHasNewErrors(false);
        }}
        className={`w-10 h-10 rounded-full flex items-center justify-center shadow-lg ${
          hasNewErrors 
            ? 'bg-red-600 text-white animate-pulse' 
            : 'bg-red-500 text-white'
        }`}
        title="View application errors"
      >
        <FontAwesomeIcon icon={faExclamationTriangle} className="h-5 w-5" />
        {errors.length > 0 && (
          <span className="absolute -top-1 -right-1 bg-red-700 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
            {errors.length}
          </span>
        )}
      </button>
      
      {/* Error panel */}
      {isOpen && (
        <div className="absolute bottom-12 right-0 w-96 max-h-[70vh] bg-white dark:bg-slate-800 rounded-lg shadow-xl border border-slate-200 dark:border-slate-700 overflow-hidden">
          <div className="flex items-center justify-between bg-red-600 text-white p-3">
            <h3 className="font-bold text-sm">Application Errors</h3>
            <div className="flex items-center gap-2">
              <button
                onClick={clearErrors}
                className="text-xs bg-red-700 hover:bg-red-800 text-white px-2 py-1 rounded"
              >
                Clear All
              </button>
              <button
                onClick={() => setIsOpen(false)}
                className="text-white hover:bg-red-700 rounded p-1"
              >
                <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
              </button>
            </div>
          </div>
          
          <div className="overflow-y-auto max-h-[calc(70vh-40px)]">
            {errors.length === 0 ? (
              <div className="p-4 text-center text-slate-500 dark:text-slate-400">
                No errors to display
              </div>
            ) : (
              <ul className="divide-y divide-slate-200 dark:divide-slate-700">
                {errors.map((error) => (
                  <li 
                    key={error.id} 
                    className="p-3 hover:bg-slate-50 dark:hover:bg-slate-700/50 cursor-pointer"
                    onClick={() => showErrorPopup(error)}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <p className="font-medium text-sm text-red-600 dark:text-red-400">{error.message}</p>
                        <p className="text-xs text-slate-500 dark:text-slate-400 mt-1">
                          {error.source}
                          {error.line && `:${error.line}`}
                          {error.column && `:${error.column}`}
                        </p>
                        <p className="text-xs text-slate-400 dark:text-slate-500 mt-1">
                          {error.timestamp.toLocaleTimeString()}
                        </p>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            )}
          </div>
        </div>
      )}

      {/* Full Error Popup when an error is selected */}
      {selectedError && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[100]">
          <div className="relative w-full max-w-2xl">
            <button 
              className="absolute top-4 right-4 text-white bg-red-600 rounded-full p-1"
              onClick={() => setSelectedError(null)}
            >
              <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
            </button>
            <ErrorPopup
              title="Application Error"
              message={selectedError.message}
              error={selectedError.error}
              showDetails={true}
              onRetry={() => setSelectedError(null)}
              className=""
            />
          </div>
        </div>
      )}
    </div>
  );
};

export default ErrorIndicator;