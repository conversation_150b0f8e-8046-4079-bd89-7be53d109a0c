import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Button } from './ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { FontAwesomeIcon } from './icons/FontAwesome.jsx';
import {
  faPaperPlane,
  faSpinner,
  faRefresh,
  faPaperclip,
  faMicrophone,
  faStop,
  faImage,
  faFile,
  faTimes,
  faSmile,
  faCode,
  faMarkdown,
  faKeyboard,
  faMagicWandSparkles,
  faTemperatureHalf,
  faCog
} from './icons/FontAwesome.jsx';

const MessageComposer = ({
  input,
  setInput,
  onSubmit,
  onKeyPress,
  isLoading,
  isPreloading,
  selectedModel,
  setSelectedModel,
  availableModels,
  isLoadingModels,
  modelError,
  preloadingError,
  onRefreshModels,
  onPreloadModel,
  uploadedFiles,
  onFileUpload,
  onRemoveFile,
  isUploading,
  isRecording,
  onStartRecording,
  onStopRecording,
  settings,
  focusMode = false
}) => {
  const [showAdvanced, setShowAdvanced] = useState(false);
  const [showTemplates, setShowTemplates] = useState(false);
  const [dragOver, setDragOver] = useState(false);
  const [localSettings, setLocalSettings] = useState({
    temperature: settings.temperature || 0.7,
    maxTokens: settings.maxTokens || 2048,
    systemPrompt: settings.systemPrompt || ''
  });

  const textareaRef = useRef(null);
  const fileInputRef = useRef(null);
  const dropZoneRef = useRef(null);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = Math.min(textareaRef.current.scrollHeight, 200) + 'px';
    }
  }, [input]);

  // Message templates
  const messageTemplates = [
    {
      category: 'Writing',
      templates: [
        { title: 'Professional Email', content: 'Help me write a professional email about [topic]' },
        { title: 'Blog Post', content: 'Write a blog post about [topic] with an engaging introduction and conclusion' },
        { title: 'Summary', content: 'Summarize the following text in 3-5 bullet points:\n\n[paste text here]' }
      ]
    },
    {
      category: 'Coding',
      templates: [
        { title: 'Code Review', content: 'Review this code and suggest improvements:\n\n```\n[paste code here]\n```' },
        { title: 'Debug Help', content: 'Help me debug this error:\n\n```\n[paste error here]\n```' },
        { title: 'Explain Code', content: 'Explain what this code does:\n\n```\n[paste code here]\n```' }
      ]
    },
    {
      category: 'Analysis',
      templates: [
        { title: 'Pros and Cons', content: 'List the pros and cons of [topic]' },
        { title: 'Compare Options', content: 'Compare [option A] vs [option B] in terms of [criteria]' },
        { title: 'Research Help', content: 'Help me research [topic] and provide key insights' }
      ]
    }
  ];

  // Handle file drop
  const handleDrop = useCallback((e) => {
    e.preventDefault();
    setDragOver(false);
    
    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      onFileUpload(files);
    }
  }, [onFileUpload]);

  const handleDragOver = useCallback((e) => {
    e.preventDefault();
    setDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e) => {
    e.preventDefault();
    if (!dropZoneRef.current?.contains(e.relatedTarget)) {
      setDragOver(false);
    }
  }, []);

  // Handle file selection
  const handleFileSelect = useCallback((e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      onFileUpload(files);
    }
    // Reset input
    e.target.value = '';
  }, [onFileUpload]);

  // Handle template selection
  const handleTemplateSelect = (template) => {
    setInput(template.content);
    setShowTemplates(false);
    textareaRef.current?.focus();
  };

  // Handle keyboard shortcuts
  const handleKeyDown = useCallback((e) => {
    // Ctrl/Cmd + Enter to send
    if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
      e.preventDefault();
      onSubmit(e);
    }
    // Escape to clear
    else if (e.key === 'Escape') {
      setInput('');
    }
    // Tab for templates
    else if (e.key === 'Tab' && !e.shiftKey && input === '') {
      e.preventDefault();
      setShowTemplates(true);
    }
    // Call parent handler
    else if (onKeyPress) {
      onKeyPress(e);
    }
  }, [onSubmit, onKeyPress, input, setInput]);

  const renderModelSelector = () => (
    <div className="flex items-center gap-2">
      <Select 
        value={selectedModel} 
        onValueChange={(model) => {
          setSelectedModel(model);
          onPreloadModel(model);
        }}
        disabled={isLoadingModels || !!modelError || isLoading || isPreloading}
      >
        <SelectTrigger className="w-full min-w-[200px]">
          <div className="flex items-center gap-2">
            {isLoadingModels && !availableModels.length ? (
              <span className="flex items-center gap-2">
                <FontAwesomeIcon icon={faSpinner} className="h-4 w-4 animate-spin" />
                <span>Loading models...</span>
              </span>
            ) : modelError ? (
              <span className="text-red-500">Error loading models</span>
            ) : (
              <>
                <span>{selectedModel || 'Select a model'}</span>
                {isPreloading && (
                  <FontAwesomeIcon 
                    icon={faSpinner} 
                    className="h-4 w-4 animate-spin text-muted-foreground" 
                    title="Preloading model..." 
                  />
                )}
              </>
            )}
          </div>
        </SelectTrigger>
        <SelectContent>
          {availableModels.map((model) => (
            <SelectItem key={model} value={model}>
              <div className="flex items-center justify-between w-full">
                <span>{model}</span>
                {model === selectedModel && !isPreloading && (
                  <span className="text-green-500 text-xs">Ready</span>
                )}
              </div>
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      
      <Button 
        variant="outline" 
        size="icon" 
        onClick={onRefreshModels}
        disabled={isLoadingModels || isLoading || isPreloading}
        title="Refresh models"
      >
        <FontAwesomeIcon 
          icon={isLoadingModels ? faSpinner : faRefresh} 
          className={`h-4 w-4 ${isLoadingModels ? 'animate-spin' : ''}`} 
        />
      </Button>
    </div>
  );

  const renderUploadedFiles = () => {
    if (uploadedFiles.length === 0) return null;

    return (
      <div className="flex flex-wrap gap-2 p-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        {uploadedFiles.map((file) => (
          <div
            key={file.id}
            className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-gray-700 border border-gray-200 dark:border-gray-600 rounded-lg"
          >
            <FontAwesomeIcon
              icon={file.type?.startsWith('image/') ? faImage : faFile}
              className="h-4 w-4 text-blue-500"
            />
            <span className="text-sm text-gray-700 dark:text-gray-300 truncate max-w-[150px]">
              {file.name}
            </span>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => onRemoveFile(file.id)}
              className="h-5 w-5 p-0 text-gray-500 hover:text-red-500"
            >
              <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
            </Button>
          </div>
        ))}
      </div>
    );
  };

  const renderAdvancedSettings = () => {
    if (!showAdvanced) return null;

    return (
      <div className="p-4 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              <FontAwesomeIcon icon={faTemperatureHalf} className="h-4 w-4 mr-1" />
              Temperature: {localSettings.temperature}
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={localSettings.temperature}
              onChange={(e) => setLocalSettings(prev => ({ ...prev, temperature: parseFloat(e.target.value) }))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Focused</span>
              <span>Balanced</span>
              <span>Creative</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Tokens
            </label>
            <input
              type="number"
              min="1"
              max="8192"
              value={localSettings.maxTokens}
              onChange={(e) => setLocalSettings(prev => ({ ...prev, maxTokens: parseInt(e.target.value) }))}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
            System Prompt
          </label>
          <textarea
            value={localSettings.systemPrompt}
            onChange={(e) => setLocalSettings(prev => ({ ...prev, systemPrompt: e.target.value }))}
            placeholder="Enter a system prompt to customize the AI's behavior..."
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
            rows={3}
          />
        </div>
      </div>
    );
  };

  const renderTemplates = () => {
    if (!showTemplates) return null;

    return (
      <div className="absolute bottom-full left-0 right-0 mb-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-lg max-h-80 overflow-y-auto z-50">
        <div className="p-3 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <h3 className="text-sm font-medium text-gray-900 dark:text-gray-100">
              Message Templates
            </h3>
            <Button
              size="sm"
              variant="ghost"
              onClick={() => setShowTemplates(false)}
              className="h-6 w-6 p-0"
            >
              <FontAwesomeIcon icon={faTimes} className="h-3 w-3" />
            </Button>
          </div>
        </div>
        
        <div className="p-2">
          {messageTemplates.map((category) => (
            <div key={category.category} className="mb-4 last:mb-0">
              <h4 className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider mb-2 px-2">
                {category.category}
              </h4>
              {category.templates.map((template, index) => (
                <button
                  key={index}
                  onClick={() => handleTemplateSelect(template)}
                  className="w-full text-left p-2 hover:bg-gray-50 dark:hover:bg-gray-700 rounded-lg transition-colors"
                >
                  <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {template.title}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 mt-1 line-clamp-2">
                    {template.content}
                  </div>
                </button>
              ))}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700">
      {/* Model selector - only show if not in focus mode */}
      {!focusMode && (
        <div className="p-4 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            {renderModelSelector()}
            
            <div className="flex items-center gap-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowAdvanced(!showAdvanced)}
                className="h-8"
              >
                <FontAwesomeIcon icon={faCog} className="h-4 w-4 mr-2" />
                Advanced
              </Button>
            </div>
          </div>
          
          {(modelError || preloadingError) && (
            <div className="mt-2 text-sm text-red-600 dark:text-red-400">
              {modelError || preloadingError}
            </div>
          )}
        </div>
      )}

      {/* Advanced settings */}
      {renderAdvancedSettings()}

      {/* Uploaded files */}
      {renderUploadedFiles()}

      {/* Main input area */}
      <div className="relative">
        <div
          ref={dropZoneRef}
          className={`relative ${dragOver ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
        >
          {dragOver && (
            <div className="absolute inset-0 flex items-center justify-center bg-blue-50 dark:bg-blue-900/20 border-2 border-dashed border-blue-300 dark:border-blue-600 rounded-lg z-10">
              <div className="text-center">
                <FontAwesomeIcon icon={faFile} className="h-8 w-8 text-blue-500 mb-2" />
                <p className="text-blue-600 dark:text-blue-400 font-medium">Drop files here to upload</p>
              </div>
            </div>
          )}

          <form onSubmit={onSubmit} className="p-4">
            <div className="flex items-end gap-3">
              {/* File upload button */}
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => fileInputRef.current?.click()}
                disabled={isLoading || isPreloading || isUploading}
                className="h-12 w-12 rounded-xl flex-shrink-0"
                title="Attach files"
              >
                <FontAwesomeIcon icon={faPaperclip} className="h-4 w-4" />
              </Button>

              {/* Voice recording button */}
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={isRecording ? onStopRecording : onStartRecording}
                disabled={isLoading || isPreloading}
                className={`h-12 w-12 rounded-xl flex-shrink-0 ${
                  isRecording ? 'bg-red-500 text-white border-red-500' : ''
                }`}
                title={isRecording ? 'Stop recording' : 'Start voice recording'}
              >
                <FontAwesomeIcon icon={isRecording ? faStop : faMicrophone} className="h-4 w-4" />
              </Button>

              {/* Message input */}
              <div className="flex-1 relative">
                <textarea
                  ref={textareaRef}
                  value={input}
                  onChange={(e) => setInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  placeholder={
                    isRecording 
                      ? "Recording..." 
                      : "Type your message... (Tab for templates, Ctrl+Enter to send)"
                  }
                  disabled={isLoading || isPreloading || !selectedModel || isRecording}
                  className="w-full px-4 py-3 pr-12 border-2 border-gray-200 dark:border-gray-600 rounded-xl bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 placeholder-gray-500 dark:placeholder-gray-400 resize-none focus:outline-none focus:border-blue-500 dark:focus:border-blue-400 transition-colors"
                  style={{ minHeight: '48px', maxHeight: '200px' }}
                />
                
                {/* Template trigger button */}
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowTemplates(!showTemplates)}
                  className="absolute right-2 top-2 h-8 w-8 p-0"
                  title="Message templates"
                >
                  <FontAwesomeIcon icon={faMagicWandSparkles} className="h-4 w-4" />
                </Button>
              </div>

              {/* Send button */}
              <Button
                type="submit"
                disabled={!input.trim() || isLoading || isPreloading || !selectedModel}
                className="h-12 px-6 rounded-xl bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 transition-all duration-200 flex-shrink-0"
              >
                <FontAwesomeIcon
                  icon={isLoading ? faSpinner : faPaperPlane}
                  className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`}
                />
                {isLoading ? 'Sending...' : 'Send'}
              </Button>
            </div>

            {/* Keyboard shortcuts hint */}
            <div className="flex items-center justify-between mt-3 text-xs text-gray-500 dark:text-gray-400">
              <div className="flex items-center gap-4">
                <span>
                  <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">Tab</kbd> for templates
                </span>
                <span>
                  <kbd className="px-1 py-0.5 bg-gray-100 dark:bg-gray-700 rounded">Ctrl+Enter</kbd> to send
                </span>
              </div>
              
              {isUploading && (
                <div className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faSpinner} className="h-3 w-3 animate-spin" />
                  <span>Uploading files...</span>
                </div>
              )}
            </div>
          </form>
        </div>

        {/* Templates popup */}
        {renderTemplates()}
      </div>

      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple
        onChange={handleFileSelect}
        className="hidden"
        accept="*/*"
      />
    </div>
  );
};

export default MessageComposer;