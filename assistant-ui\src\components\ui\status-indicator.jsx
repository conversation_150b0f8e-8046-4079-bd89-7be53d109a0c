import React, { memo } from 'react';
import { cn } from '../../lib/utils';

/**
 * StatusIndicator component for displaying status with animated visual indicators
 * 
 * @param {Object} props
 * @param {string} props.status - Status type: 'online', 'offline', 'error', 'warning', 'loading', 'idle'
 * @param {string} props.size - Size variant: 'sm', 'md', 'lg'
 * @param {boolean} props.showLabel - Whether to show text label
 * @param {boolean} props.animated - Whether to show pulse animation
 * @param {string} props.className - Additional CSS classes
 * @param {string} props.label - Custom label text (optional)
 */
const StatusIndicator = memo(({
  status = 'idle',
  size = 'md',
  showLabel = false,
  animated = true,
  className,
  label,
  ...props
}) => {
  // Status configurations
  const statusConfig = {
    online: {
      color: 'bg-status-online',
      label: 'Online',
      animate: true
    },
    offline: {
      color: 'bg-status-offline',
      label: 'Offline',
      animate: false
    },
    error: {
      color: 'bg-status-error',
      label: 'Error',
      animate: true
    },
    warning: {
      color: 'bg-status-warning',
      label: 'Warning',
      animate: true
    },
    loading: {
      color: 'bg-status-loading',
      label: 'Loading',
      animate: true
    },
    idle: {
      color: 'bg-status-idle',
      label: 'Idle',
      animate: false
    }
  };

  // Size configurations
  const sizeConfig = {
    sm: {
      dot: 'w-2 h-2',
      text: 'text-xs',
      gap: 'gap-1'
    },
    md: {
      dot: 'w-3 h-3',
      text: 'text-sm',
      gap: 'gap-2'
    },
    lg: {
      dot: 'w-4 h-4',
      text: 'text-base',
      gap: 'gap-2'
    }
  };

  const config = statusConfig[status] || statusConfig.idle;
  const sizes = sizeConfig[size] || sizeConfig.md;
  
  const shouldAnimate = animated && config.animate;
  const displayLabel = label || config.label;

  return (
    <div
      className={cn(
        'inline-flex items-center',
        sizes.gap,
        className
      )}
      {...props}
    >
      {/* Status Dot */}
      <div
        className={cn(
          'rounded-full',
          sizes.dot,
          config.color,
          shouldAnimate && 'animate-pulse-soft'
        )}
        aria-hidden="true"
      />
      
      {/* Status Label */}
      {showLabel && (
        <span
          className={cn(
            'font-medium text-foreground',
            sizes.text
          )}
        >
          {displayLabel}
        </span>
      )}
      
      {/* Screen Reader Text */}
      <span className="sr-only">
        Status: {displayLabel}
      </span>
    </div>
  );
});

StatusIndicator.displayName = 'StatusIndicator';


export default StatusIndicator;
