/* Settings.css */
.settings-page-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: var(--primary-bg);
  color: var(--primary-text);
  overflow: hidden; /* Prevent scrolling on container */
}

/* Custom scrollbar style */
.thin-scrollbar::-webkit-scrollbar {
  width: 6px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(155, 155, 155, 0.5);
  border-radius: 4px;
}

.thin-scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: rgba(155, 155, 155, 0.8);
}

/* Settings Top Bar */
.settings-top-bar {
  display: flex;
  align-items: center;
  padding: 0 20px;
  background-color: var(--secondary-bg);
  border-bottom: 1px solid var(--border-color);
  height: 50px;
}

.settings-back-link {
  display: flex;
  align-items: center;
  color: var(--primary-text);
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
}

.settings-back-link svg {
  margin-right: 10px;
}

.settings-back-link:hover {
  color: var(--accent-blue);
}

.settings-main-tabs {
  display: flex;
  margin-left: 40px; /* Space from back link */
}

.main-tab-button {
  background: none;
  border: none;
  color: var(--secondary-text);
  padding: 15px 20px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
  transition: color 0.2s, border-color 0.2s;
}

.main-tab-button:hover {
  color: var(--primary-text);
}

.main-tab-button.active {
  color: var(--accent-blue);
  border-bottom-color: var(--accent-blue);
}

.settings-top-bar-spacer {
  flex-grow: 1;
}

/* Settings Main Area */
.settings-main-area {
  display: flex;
  flex: 1;
  overflow: hidden; /* Contain children */
}

/* Settings Sidebar */
.settings-sidebar {
  width: 260px;
  background-color: var(--secondary-bg);
  padding: 20px;
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Contain children */
}

.settings-search-bar {
  position: relative;
  margin-bottom: 20px;
}

.settings-search-bar input {
  width: 100%;
  padding: 10px 10px 10px 35px; /* Space for icon */
  background-color: var(--tertiary-bg);
  border: 1px solid var(--border-color);
  border-radius: 6px;
  color: var(--primary-text);
  font-size: 0.9rem;
}

.settings-search-bar input::placeholder {
  color: var(--secondary-text);
}

.search-icon {
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--secondary-text);
}

.sidebar-nav {
  display: flex;
  flex-direction: column;
  gap: 5px;
  overflow-y: auto; /* Allow scrolling for nav items */
  flex: 1; /* Take available space */
}

.sidebar-nav-item {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: var(--secondary-text);
  padding: 10px 12px;
  text-align: left;
  cursor: pointer;
  font-size: 0.95rem;
  border-radius: 6px;
  transition: background-color 0.2s, color 0.2s;
}

.sidebar-nav-item:hover {
  background-color: var(--tertiary-bg);
  color: var(--primary-text);
}

.sidebar-nav-item.active {
  background-color: var(--accent-blue);
  color: white;
}

.sidebar-item-icon {
  margin-right: 12px;
  width: 18px; /* Ensure icons align */
  text-align: center;
}

/* Settings Content Area */
.settings-content-area {
  flex: 1;
  padding: 30px;
  overflow-y: auto; /* Allow scrolling for content */
  height: 100%; /* Take full height */
}

.settings-content-area h3 {
  font-size: 1.4rem;
  color: var(--primary-text);
  margin-top: 0;
  margin-bottom: 25px;
  padding-bottom: 10px;
  border-bottom: 1px solid var(--border-color);
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start; /* Align items to top for multi-line text */
  padding: 15px 0;
  border-bottom: 1px solid var(--border-color-light);
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info h4 {
  margin: 0 0 5px 0;
  color: var(--primary-text);
  font-size: 1.05rem;
}

.setting-info p {
  margin: 0;
  color: var(--secondary-text);
  font-size: 0.9rem;
  max-width: 450px; /* Limit width of description */
}

.action-button {
  padding: 8px 16px;
  background-color: var(--accent-blue);
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  font-size: 0.9rem;
  transition: background-color 0.2s;
  white-space: nowrap; /* Prevent button text from wrapping */
}

.action-button:hover {
  background-color: #2563eb; /* Darker blue */
}

.action-button.danger {
  background-color: var(--accent-red);
}

.action-button.danger:hover {
  background-color: #c0392b; /* Darker red */
}

/* Plugin Specific Styles */
.plugins-list-container h3 {
    /* Uses general h3 style */
}

.loading-message, .error-message, .empty-message {
  padding: 20px;
  text-align: center;
  color: var(--secondary-text);
  background-color: var(--tertiary-bg);
  border-radius: 6px;
  margin-top: 15px;
}

.error-message {
  color: var(--accent-red);
  background-color: rgba(220, 53, 69, 0.1);
}

.plugins-list {
  display: grid;
  grid-template-columns: 1fr; /* Single column for now, can be 1fr 1fr for two columns */
  gap: 15px;
}

.plugin-item {
  background-color: var(--secondary-bg);
  padding: 15px;
  border-radius: 6px;
  border: 1px solid var(--border-color);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.plugin-info h4 {
  margin: 0 0 5px 0;
  font-size: 1.1rem;
  color: var(--primary-text);
}

.plugin-version {
  font-size: 0.8rem;
  color: var(--secondary-text);
  background-color: var(--tertiary-bg);
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
}

.plugin-info p {
  margin: 0;
  color: var(--secondary-text);
  font-size: 0.9rem;
}

/* Toggle Switch (re-styled for dark theme) */
.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
  margin-left: 15px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--tertiary-bg); /* Darker background for off state */
  transition: .3s;
  border-radius: 24px;
  border: 1px solid var(--border-color-light);
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 2px;
  bottom: 2px;
  background-color: var(--secondary-text); /* Knob color */
  transition: .3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--accent-green);
  border-color: var(--accent-green);
}

input:checked + .toggle-slider:before {
  background-color: white;
  transform: translateX(20px);
}

/* Placeholder Content */
.placeholder-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  height: 100%;
  color: var(--secondary-text);
}

.placeholder-content svg {
  color: var(--accent-blue);
  margin-bottom: 20px;
}

.placeholder-content h2 {
  font-size: 1.5rem;
  color: var(--primary-text);
  margin-bottom: 10px;
}

.placeholder-content p {
  font-size: 1rem;
  max-width: 450px;
}