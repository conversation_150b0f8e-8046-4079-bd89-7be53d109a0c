use serde::{Deserialize, Serialize};
use std::fs;
use std::path::{Path, PathBuf};
use std::collections::HashMap;
use std::sync::{Arc, RwLock};
use lazy_static::lazy_static;
use log::info;

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct Colors {
    pub primary: String,
    pub background: String,
    pub surface: String,
    pub accent: String,
    pub text: String,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct ColorScheme {
    pub id: String,
    pub name: String,
    pub is_dark: bool,
    pub colors: Colors,
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct Background {
    pub id: String,
    pub name: String,
    pub r#type: String,
    pub value: String,
    pub opacity: f64,
}

fn default_appearance_settings() -> AppearanceSettings {
    AppearanceSettings {
        color_schemes: default_color_schemes(),
        backgrounds: default_backgrounds(),
        theme: default_theme(),
        dark_mode: default_dark_mode(),
        layout: default_layout(),
    }
}

fn default_color_schemes() -> Vec<ColorScheme> {
    vec![
        ColorScheme {
            id: "voidCircuit".to_string(),
            name: "Void Circuit".to_string(),
            is_dark: true,
            colors: Colors {
                primary: "#00BCD4".to_string(),
                background: "#121212".to_string(),
                surface: "#1E1E1E".to_string(),
                accent: "#FF4081".to_string(),
                text: "#E0E0E0".to_string(),
            },
        },
    ]
}

fn default_backgrounds() -> Vec<Background> {
    vec![
        Background {
            id: "default".to_string(),
            name: "Default".to_string(),
            r#type: "color".to_string(),
            value: "#121212".to_string(),
            opacity: 1.0,
        },
    ]
}

fn default_theme() -> String {
    "voidCircuit".to_string()
}

fn default_dark_mode() -> bool {
    true
}

fn default_layout() -> String {
    "default".to_string()
}

#[derive(Debug, Serialize, Deserialize, Clone, PartialEq)]
pub struct AppearanceSettings {
    #[serde(default = "default_color_schemes")]
    pub color_schemes: Vec<ColorScheme>,
    #[serde(default = "default_backgrounds")]
    pub backgrounds: Vec<Background>,
    #[serde(default = "default_theme")]
    pub theme: String,
    #[serde(default = "default_dark_mode")]
    pub dark_mode: bool,
    #[serde(default = "default_layout")]
    pub layout: String,
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerExecutable {
    pub path: String,
    pub file_name: String,
    pub executable_type: String, // "exe", "py", "jar", "sh", etc.
    pub priority: i32,           // Lower number = higher priority
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ServerProfile {
    pub name: String,
    pub enabled: bool,
    #[serde(default)]
    pub folder_path: String,
    #[serde(default)]
    pub detected_executables: Vec<ServerExecutable>,
    #[serde(default)]
    pub primary_executable: Option<ServerExecutable>,
    #[serde(default)]
    pub server_type: Option<String>,        // "language_server", "llamacpp", "custom", etc.
    #[serde(default)]
    pub last_verified: Option<i64>,         // Unix timestamp
    #[serde(default = "default_verification_status")]
    pub verification_status: String,        // "verified", "error", "not_checked"
    #[serde(default)]
    pub error_message: Option<String>,
    #[serde(default)]
    pub metadata: HashMap<String, String>,  // For future extensibility
}

fn default_verification_status() -> String {
    "not_checked".to_string()
}

fn default_config_version() -> u32 {
    2 // Current version with enhanced ServerProfile
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct ModelProfile {
    pub name: String,
    pub enabled: bool,
}

// Get the application base directory (one level up from the executable)
pub fn get_app_base_dir() -> Result<PathBuf, String> {
    let manifest_dir = PathBuf::from(env!("CARGO_MANIFEST_DIR"));
    if let Some(parent) = manifest_dir.parent() {
        Ok(parent.to_path_buf())
    } else {
        std::env::current_dir().map_err(|e| format!("Failed to get current directory: {}", e))
    }
}

// Helper function to convert Path/PathBuf to string for display
pub fn path_to_string(path: &Path) -> String {
    path.to_string_lossy().to_string()
}

// Default paths - relative to the application base directory
lazy_static! {
    static ref BASE_DIR: PathBuf = {
        let path = get_app_base_dir().unwrap_or_else(|_| PathBuf::from("."));
        info!("Base directory (from lazy_static): {}", path.display());
        path
    };
    
    // Main storage paths
    pub static ref STORAGE_DIR: PathBuf = {
        let path = BASE_DIR.join("Storage");
        info!("Storage directory (from lazy_static): {}", path.display());
        path
    };
    pub static ref SYSTEM_DIR: PathBuf = STORAGE_DIR.join("System");
    pub static ref ADDONS_DIR: PathBuf = STORAGE_DIR.join("Addons");
    pub static ref USER_DIR: PathBuf = SYSTEM_DIR.join("User");
    pub static ref USER_PREFS_PATH: PathBuf = USER_DIR.join("userpref.config");
    
    // Default paths - can be overridden by user preferences
    pub static ref DEFAULT_INDEXED_DIRECTORY: PathBuf = STORAGE_DIR.to_path_buf();
    pub static ref DEFAULT_SYSTEM_LOG_PATH: PathBuf = SYSTEM_DIR.join("logs");
    pub static ref DEFAULT_PLUGINS_PATH: PathBuf = ADDONS_DIR.join("Plugins");
    pub static ref DEFAULT_MCPS_PATH: PathBuf = ADDONS_DIR.join("MCP");
    pub static ref DEFAULT_APIS_PATH: PathBuf = ADDONS_DIR.join("API");
    pub static ref DEFAULT_MODELS_PATH: PathBuf = SYSTEM_DIR.join("Models");
    pub static ref DEFAULT_SERVERS_PATH: PathBuf = SYSTEM_DIR.join("Servers");
    pub static ref DEFAULT_SYSTEM_PROMPTS_PATH: PathBuf = SYSTEM_DIR.join("Logic");
    pub static ref DEFAULT_LOGIC_HUB_PATH: PathBuf = DEFAULT_SYSTEM_PROMPTS_PATH.join("Hub");
    pub static ref DEFAULT_LOGIC_SYSTEM_PROMPTS_PATH: PathBuf = DEFAULT_SYSTEM_PROMPTS_PATH.join("SystemPrompts");
    pub static ref DEFAULT_LOGIC_MODALS_PATH: PathBuf = DEFAULT_SYSTEM_PROMPTS_PATH.join("Modals");
    pub static ref DEFAULT_LOGIC_AGENTS_PATH: PathBuf = DEFAULT_SYSTEM_PROMPTS_PATH.join("Agents");
    pub static ref DEFAULT_EXTENSIONS_PATH: PathBuf = SYSTEM_DIR.join("Extensions");
    // Cached user preferences
    static ref CACHED_PREFERENCES: Arc<RwLock<Option<UserPreferences>>> = Arc::new(RwLock::new(None));
}

// Ensure all required directories exist
pub fn ensure_directories() -> Result<(), String> {
    info!("Ensuring storage directories exist...");
    info!("Storage root: {}", STORAGE_DIR.display());
    
    let dirs = vec![
        ("User Directory", USER_DIR.as_path()),
        ("System Logs", DEFAULT_SYSTEM_LOG_PATH.as_path()),
        ("Plugins", DEFAULT_PLUGINS_PATH.as_path()),
        ("MCPs", DEFAULT_MCPS_PATH.as_path()),
        ("APIs", DEFAULT_APIS_PATH.as_path()),
        ("Models", DEFAULT_MODELS_PATH.as_path()),
        ("Servers", DEFAULT_SERVERS_PATH.as_path()),
        ("Logic", DEFAULT_SYSTEM_PROMPTS_PATH.as_path()),
        ("Logic Hub", DEFAULT_LOGIC_HUB_PATH.as_path()),
        ("Logic System Prompts", DEFAULT_LOGIC_SYSTEM_PROMPTS_PATH.as_path()),
        ("Logic Modals", DEFAULT_LOGIC_MODALS_PATH.as_path()),
        ("Logic Agents", DEFAULT_LOGIC_AGENTS_PATH.as_path()),
        ("Extensions", DEFAULT_EXTENSIONS_PATH.as_path()),
    ];

    for (name, dir) in dirs {
        info!("Checking {}: {}", name, dir.display());
        fs::create_dir_all(dir)
            .map_err(|e| format!("Failed to create directory {}: {}", dir.display(), e))?;
    }
    
    Ok(())
}

// Helper function to resolve paths relative to the application base directory
pub fn resolve_path(path: &str) -> Result<PathBuf, String> {
    // Handle absolute paths
    if Path::new(path).is_absolute() {
        return Ok(PathBuf::from(path));
    }
    
    // Handle paths starting with ./
    let clean_path = if path.starts_with("./") {
        &path[2..]
    } else {
        path
    };
    
    // Join with base directory
    Ok(BASE_DIR.join(clean_path))
}

#[derive(Debug, Serialize, Deserialize, Clone)]
pub struct UserPreferences {
    // Version for migration handling
    #[serde(default = "default_config_version")]
    pub config_version: u32,

    // Appearance settings
    #[serde(default = "default_appearance_settings")]
    pub appearance: AppearanceSettings,

    pub plugin_states: HashMap<String, bool>,

    // Path settings (extending existing config)
    pub system_log_path: String,
    #[serde(default = "default_indexed_directory")]
    pub indexed_directory: String,
    #[serde(default = "default_plugins_path")]
    pub plugins_path: String,
    #[serde(default = "default_mcps_path")]
    pub mcps_path: String,
    #[serde(default = "default_apis_path")]
    pub apis_path: String,
    #[serde(default = "default_models_path")]
    pub models_path: String,
    #[serde(default = "default_servers_path")]
    pub servers_path: String,
    #[serde(default = "default_system_prompts_path")]
    pub system_prompts_path: String,
    #[serde(default = "default_logic_hub_path")]
    pub logic_hub_path: String,
    #[serde(default = "default_logic_system_prompts_path")]
    pub logic_system_prompts_path: String,
    #[serde(default = "default_logic_modals_path")]
    pub logic_modals_path: String,
    #[serde(default = "default_logic_agents_path")]
    pub logic_agents_path: String,
    #[serde(default = "default_extensions_path")]
    pub extensions_path: String,

    // Date/time settings
    #[serde(default = "default_date_format")]
    pub date_format: String,
    #[serde(default = "default_time_format")]
    pub time_format: String,
    #[serde(default = "default_timezone")]
    pub timezone: String,

    // Application behavior settings
    #[serde(default = "default_auto_save")]
    pub auto_save: bool,
    #[serde(default = "default_startup_tab")]
    pub startup_tab: String,
    #[serde(default = "default_window_state")]
    pub window_maximized: bool,
    #[serde(default = "default_notifications")]
    pub notifications_enabled: bool,

    // Browser settings
    #[serde(default = "default_browser_homepage")]
    pub browser_homepage: String,
    #[serde(default = "default_browser_zoom")]
    pub browser_zoom_level: i32,
    #[serde(default = "default_browser_js")]
    pub browser_enable_javascript: bool,
    #[serde(default = "default_browser_images")]
    pub browser_enable_images: bool,
    #[serde(default = "default_browser_cookies")]
    pub browser_enable_cookies: bool,
    #[serde(default = "default_browser_popups")]
    pub browser_block_popups: bool,

    // AI settings
    #[serde(default = "default_server_url")]
    pub server_url: String,
    #[serde(default = "default_model")]
    pub default_model: String,
    #[serde(default = "default_auto_start_server")]
    pub auto_start_server: bool,

    // Server and Model profiles (like plugins)
    #[serde(default)]
    pub server_profiles: HashMap<String, ServerProfile>,
    #[serde(default)]
    pub model_profiles: HashMap<String, ModelProfile>,
    #[serde(default)]
    pub active_server_profile: String,
    #[serde(default)]
    pub active_model_profile: String,

    // Projection settings
    #[serde(default = "default_ios_projection_enabled")] 
    pub ios_projection_enabled: bool,
    #[serde(default = "default_ios_device_name")] 
    pub ios_device_name: String,
    #[serde(default = "default_ios_projection_quality")] 
    pub ios_projection_quality: String,
    #[serde(default = "default_android_projection_enabled")] 
    pub android_projection_enabled: bool,
    #[serde(default = "default_android_device_name")] 
    pub android_device_name: String,
    #[serde(default = "default_android_projection_quality")] 
    pub android_projection_quality: String,
    #[serde(default = "default_miracast_enabled")] 
    pub miracast_enabled: bool,
    #[serde(default = "default_dlna_enabled")] 
    pub dlna_enabled: bool,
    #[serde(default = "default_projection_local_only")] 
    pub projection_local_only: bool,
    #[serde(default = "default_projection_port")] 
    pub projection_port: u16,
    #[serde(default = "default_projection_protocol")] 
    pub projection_protocol: String,
    
    // Network settings
    #[serde(default = "default_server_host")]
    pub server_host: String,
    #[serde(default = "default_server_port")]
    pub server_port: u16,
    #[serde(default = "default_network_protocol")]
    pub network_protocol: String,
    #[serde(default = "default_proxy_mode")]
    pub proxy_mode: String,
    #[serde(default = "default_proxy_host")]
    pub proxy_host: String,
    #[serde(default = "default_proxy_port")]
    pub proxy_port: u16,
    #[serde(default = "default_connection_timeout")]
    pub connection_timeout: u32,
    #[serde(default = "default_max_retries")]
    pub max_retries: u32,
    #[serde(default = "default_enable_ssl")]
    pub enable_ssl: bool,
    #[serde(default = "default_verify_certificates")]
    pub verify_certificates: bool,
}

// Default value functions for serde
fn default_indexed_directory() -> String { path_to_string(&DEFAULT_INDEXED_DIRECTORY) }
fn default_plugins_path() -> String { path_to_string(&DEFAULT_PLUGINS_PATH) }
fn default_mcps_path() -> String { path_to_string(&DEFAULT_MCPS_PATH) }
fn default_apis_path() -> String { path_to_string(&DEFAULT_APIS_PATH) }
fn default_models_path() -> String { path_to_string(&DEFAULT_MODELS_PATH) }
fn default_servers_path() -> String { path_to_string(&DEFAULT_SERVERS_PATH) }
fn default_system_prompts_path() -> String { path_to_string(&DEFAULT_SYSTEM_PROMPTS_PATH) }
fn default_logic_hub_path() -> String { path_to_string(&DEFAULT_LOGIC_HUB_PATH) }
fn default_logic_system_prompts_path() -> String { path_to_string(&DEFAULT_LOGIC_SYSTEM_PROMPTS_PATH) }
fn default_logic_modals_path() -> String { path_to_string(&DEFAULT_LOGIC_MODALS_PATH) }
fn default_logic_agents_path() -> String { path_to_string(&DEFAULT_LOGIC_AGENTS_PATH) }
fn default_extensions_path() -> String { path_to_string(&DEFAULT_EXTENSIONS_PATH) }
fn default_date_format() -> String { "YYYY-MM-DD".to_string() }
fn default_time_format() -> String { "24h".to_string() }
fn default_timezone() -> String { "UTC".to_string() }

// Application behavior defaults
fn default_auto_save() -> bool { true }
fn default_startup_tab() -> String { "chat".to_string() }
fn default_window_state() -> bool { false }
fn default_notifications() -> bool { true }

// Browser defaults
fn default_browser_homepage() -> String { "https://www.google.com".to_string() }
fn default_browser_zoom() -> i32 { 100 }
fn default_browser_js() -> bool { true }
fn default_browser_images() -> bool { true }
fn default_browser_cookies() -> bool { true }
fn default_browser_popups() -> bool { true }

// AI defaults
fn default_server_url() -> String { "http://127.0.0.1:11435".to_string() }
fn default_model() -> String { "".to_string() }
fn default_auto_start_server() -> bool { true }

// Projection defaults
fn default_ios_projection_enabled() -> bool { false }
fn default_ios_device_name() -> String { "The Collective".to_string() }
fn default_ios_projection_quality() -> String { "auto".to_string() }
fn default_android_projection_enabled() -> bool { false }
fn default_android_device_name() -> String { "The Collective".to_string() }
fn default_android_projection_quality() -> String { "auto".to_string() }
fn default_miracast_enabled() -> bool { false }
fn default_dlna_enabled() -> bool { false }
fn default_projection_local_only() -> bool { true }
fn default_projection_port() -> u16 { 8080 }
fn default_projection_protocol() -> String { "auto".to_string() }

// Network defaults
fn default_server_host() -> String { "127.0.0.1".to_string() }
fn default_server_port() -> u16 { 11435 }
fn default_network_protocol() -> String { "http".to_string() }
fn default_proxy_mode() -> String { "none".to_string() }
fn default_proxy_host() -> String { "".to_string() }
fn default_proxy_port() -> u16 { 0 }
fn default_connection_timeout() -> u32 { 30 }
fn default_max_retries() -> u32 { 3 }
fn default_enable_ssl() -> bool { false }
fn default_verify_certificates() -> bool { true }

impl Default for UserPreferences {
    fn default() -> Self {
        // Create default paths as strings
        let default_indexed = path_to_string(&DEFAULT_INDEXED_DIRECTORY);
        let default_plugins = path_to_string(&DEFAULT_PLUGINS_PATH);
        let default_mcps = path_to_string(&DEFAULT_MCPS_PATH);
        let default_apis = path_to_string(&DEFAULT_APIS_PATH);
        let default_models = path_to_string(&DEFAULT_MODELS_PATH);
        let default_servers = path_to_string(&DEFAULT_SERVERS_PATH);
        let default_system_prompts = path_to_string(&DEFAULT_SYSTEM_PROMPTS_PATH);
        let default_logs = path_to_string(&DEFAULT_SYSTEM_LOG_PATH);
        
        // Debug output to verify paths
        println!("DEBUG: Default paths being generated:");
        println!("  indexed_directory: {}", default_indexed);
        println!("  plugins_path: {}", default_plugins);
        println!("  servers_path: {}", default_servers);
        
        Self {
            // Version for migration handling
            config_version: 2,

            // Appearance settings
            appearance: default_appearance_settings(),
            plugin_states: HashMap::new(),

            // Path settings
            system_log_path: default_logs,
            indexed_directory: default_indexed,
            plugins_path: default_plugins,
            mcps_path: default_mcps,
            apis_path: default_apis,
            models_path: default_models,
            servers_path: default_servers,
            system_prompts_path: default_system_prompts,
            logic_hub_path: path_to_string(&DEFAULT_LOGIC_HUB_PATH),
            logic_system_prompts_path: path_to_string(&DEFAULT_LOGIC_SYSTEM_PROMPTS_PATH),
            logic_modals_path: path_to_string(&DEFAULT_LOGIC_MODALS_PATH),
            logic_agents_path: path_to_string(&DEFAULT_LOGIC_AGENTS_PATH),
            extensions_path: path_to_string(&DEFAULT_EXTENSIONS_PATH),

            // Date/time settings
            date_format: "YYYY-MM-DD".to_string(),
            time_format: "24h".to_string(),
            timezone: "UTC".to_string(),

            // Application behavior settings
            auto_save: default_auto_save(),
            startup_tab: default_startup_tab(),
            window_maximized: default_window_state(),
            notifications_enabled: default_notifications(),

            // Browser settings
            browser_homepage: default_browser_homepage(),
            browser_zoom_level: default_browser_zoom(),
            browser_enable_javascript: default_browser_js(),
            browser_enable_images: default_browser_images(),
            browser_enable_cookies: default_browser_cookies(),
            browser_block_popups: default_browser_popups(),

            // AI settings
            server_url: default_server_url(),
            default_model: default_model(),
            auto_start_server: default_auto_start_server(),

            // Server and Model profiles
            server_profiles: HashMap::new(),
            model_profiles: HashMap::new(),
            active_server_profile: String::new(),
            active_model_profile: String::new(),

            // Projection settings
            ios_projection_enabled: default_ios_projection_enabled(),
            ios_device_name: default_ios_device_name(),
            ios_projection_quality: default_ios_projection_quality(),
            android_projection_enabled: default_android_projection_enabled(),
            android_device_name: default_android_device_name(),
            android_projection_quality: default_android_projection_quality(),
            miracast_enabled: default_miracast_enabled(),
            dlna_enabled: default_dlna_enabled(),
            projection_local_only: default_projection_local_only(),
            projection_port: default_projection_port(),
            projection_protocol: default_projection_protocol(),
            
            // Network settings
            server_host: default_server_host(),
            server_port: default_server_port(),
            network_protocol: default_network_protocol(),
            proxy_mode: default_proxy_mode(),
            proxy_host: default_proxy_host(),
            proxy_port: default_proxy_port(),
            connection_timeout: default_connection_timeout(),
            max_retries: default_max_retries(),
            enable_ssl: default_enable_ssl(),
            verify_certificates: default_verify_certificates(),
        }
    }
}

impl UserPreferences {
    // Load preferences with caching
    pub fn load() -> Self {
        // Try to get from cache first
        if let Ok(cache) = CACHED_PREFERENCES.read() {
            if let Some(ref prefs) = *cache {
                return prefs.clone();
            }
        }

        // Load from disk and cache
        let prefs = Self::load_from_disk();
        if let Ok(mut cache) = CACHED_PREFERENCES.write() {
            *cache = Some(prefs.clone());
        }
        prefs
    }

    // Force reload from disk (bypasses cache)
    pub fn reload() -> Self {
        let prefs = Self::load_from_disk();
        if let Ok(mut cache) = CACHED_PREFERENCES.write() {
            *cache = Some(prefs.clone());
        }
        prefs
    }

    // Clear cache
    pub fn clear_cache() {
        if let Ok(mut cache) = CACHED_PREFERENCES.write() {
            *cache = None;
        }
    }

    // Internal function to load from disk
    fn load_from_disk() -> Self {
        // Ensure storage directories exist
        if let Err(e) = ensure_directories() {
            eprintln!("Warning: Failed to create storage directories: {}", e);
        }

        // Get the prefs path
        let prefs_path = USER_PREFS_PATH.clone();

        if !prefs_path.exists() {
            // Create default preferences file
            let default_prefs = UserPreferences::default();
            if let Err(e) = default_prefs.save() {
                eprintln!("Failed to create default user preferences: {}", e);
            }
            return default_prefs;
        }

        println!("Loading user preferences from: {}", prefs_path.display());
        match fs::read_to_string(&prefs_path) {
            Ok(contents) => {
                println!("Read {} bytes from preferences file", contents.len());
                if contents.trim().is_empty() {
                    println!("Preferences file is empty, creating defaults");
                    let default_prefs = UserPreferences::default();
                    if let Err(e) = default_prefs.save() {
                        eprintln!("Failed to save default preferences: {}", e);
                    }
                    return default_prefs;
                }
                match serde_json::from_str::<UserPreferences>(&contents) {
                    Ok(mut prefs) => {
                        // Function to check and convert relative paths to absolute
                        let make_absolute = |path: &str| -> String {
                            let path_buf = PathBuf::from(path);
                            if path_buf.is_absolute() {
                                return path.to_string();
                            }
                            
                            // If path is relative, resolve it against the base directory
                            match resolve_path(path) {
                                Ok(abs_path) => {
                                    abs_path.to_string_lossy().to_string()
                                },
                                Err(_) => {
                                    eprintln!("Failed to resolve path: {}", path);
                                    path.to_string()
                                }
                            }
                        };

                        // Convert all paths to absolute
                        prefs.indexed_directory = make_absolute(&prefs.indexed_directory);
                        prefs.plugins_path = make_absolute(&prefs.plugins_path);
                        prefs.mcps_path = make_absolute(&prefs.mcps_path);
                        prefs.apis_path = make_absolute(&prefs.apis_path);
                        prefs.models_path = make_absolute(&prefs.models_path);
                        prefs.servers_path = make_absolute(&prefs.servers_path);
                        prefs.system_prompts_path = make_absolute(&prefs.system_prompts_path);
                        prefs.system_log_path = make_absolute(&prefs.system_log_path);

                        // Check if migration is needed
                        if prefs.config_version < 2 {
                            println!("Migrating user preferences from version {} to version 2", prefs.config_version);
                            let migrated_prefs = Self::migrate_to_v2(prefs);

                            // Save migrated preferences
                            if let Err(e) = migrated_prefs.save() {
                                eprintln!("Failed to save migrated preferences: {}", e);
                            } else {
                                println!("Successfully migrated and saved user preferences");
                            }
                            return migrated_prefs;
                        }
                        prefs
                    },
                    Err(e) => {
                        println!("Failed to parse user preferences from {}: {}", prefs_path.display(), e);
                        println!("File contents (first 200 chars): {}",
                            if contents.len() > 200 {
                                format!("{}...", &contents[..200])
                            } else {
                                contents.clone()
                            });
                        let default_prefs = UserPreferences::default();
                        if let Err(save_e) = default_prefs.save() {
                            eprintln!("Failed to save default preferences: {}", save_e);
                        }
                        default_prefs
                    }
                }
            }
            Err(e) => {
                eprintln!("Failed to read user preferences: {}, using defaults", e);
                let default_prefs = UserPreferences::default();
                if let Err(save_e) = default_prefs.save() {
                    eprintln!("Failed to save default preferences: {}", save_e);
                }
                default_prefs
            }
        }
    }

    pub fn save(&self) -> Result<(), String> {
        // Ensure the user directory exists
        let prefs_path = USER_PREFS_PATH.clone();
        if let Some(parent) = prefs_path.parent() {
            std::fs::create_dir_all(parent).map_err(|e| 
                format!("Failed to create user directory: {}", e)
            )?;
        }
        
        // Convert to string and write to file
        let json = serde_json::to_string_pretty(self)
            .map_err(|e| format!("Failed to serialize user preferences: {}", e))?;

        fs::write(&prefs_path, json)
            .map_err(|e| format!("Failed to write user preferences to {}: {}", path_to_string(&prefs_path), e))?;

        // Update cache with new preferences
        if let Ok(mut cache) = CACHED_PREFERENCES.write() {
            *cache = Some(self.clone());
        }

        Ok(())
    }

    // Migration from version 1 to version 2 (enhanced ServerProfile)
    fn migrate_to_v2(mut prefs: UserPreferences) -> UserPreferences {
        println!("Migrating server profiles to enhanced format...");

        // Convert old server profiles to new format
        let mut new_server_profiles = HashMap::new();

        for (name, old_profile) in prefs.server_profiles.iter() {
            let enhanced_profile = ServerProfile {
                name: old_profile.name.clone(),
                enabled: old_profile.enabled,
                folder_path: String::new(), // Will be populated on next scan
                detected_executables: Vec::new(),
                primary_executable: None,
                server_type: None,
                last_verified: None,
                verification_status: "needs_scan".to_string(),
                error_message: None,
                metadata: HashMap::new(),
            };
            new_server_profiles.insert(name.clone(), enhanced_profile);
        }

        prefs.server_profiles = new_server_profiles;
        prefs.config_version = 2;

        println!("Migrated {} server profiles", prefs.server_profiles.len());
        prefs
    }

    // Try to migrate from completely old format (before versioning)
    #[allow(dead_code)]
    fn try_migrate_from_old_format(contents: &str) -> Result<UserPreferences, String> {
        // Try to parse as a generic JSON value first
        let json_value: serde_json::Value = serde_json::from_str(contents)
            .map_err(|e| format!("Invalid JSON: {}", e))?;

        let mut prefs = UserPreferences::default();
        prefs.config_version = 2; // Set to current version

        // Extract basic fields that existed in old format
        if let Some(obj) = json_value.as_object() {
            // Theme settings
            if let Some(dark_mode) = obj.get("dark_mode").and_then(|v| v.as_bool()) {
                prefs.appearance.dark_mode = dark_mode;
            }
            if let Some(layout) = obj.get("layout").and_then(|v| v.as_str()) {
                prefs.appearance.layout = layout.to_string();
            }
            if let Some(theme) = obj.get("theme").and_then(|v| v.as_str()) {
                prefs.appearance.theme = theme.to_string();
            }

            // Path settings
            if let Some(path) = obj.get("system_log_path").and_then(|v| v.as_str()) {
                prefs.system_log_path = path.to_string();
            }
            if let Some(path) = obj.get("indexed_directory").and_then(|v| v.as_str()) {
                prefs.indexed_directory = path.to_string();
            }
            if let Some(path) = obj.get("plugins_path").and_then(|v| v.as_str()) {
                prefs.plugins_path = path.to_string();
            }
            if let Some(path) = obj.get("servers_path").and_then(|v| v.as_str()) {
                prefs.servers_path = path.to_string();
            }
            if let Some(path) = obj.get("models_path").and_then(|v| v.as_str()) {
                prefs.models_path = path.to_string();
            }

            // Migrate old server profiles
            if let Some(server_profiles) = obj.get("server_profiles").and_then(|v| v.as_object()) {
                for (name, profile_value) in server_profiles {
                    if let Some(profile_obj) = profile_value.as_object() {
                        let enabled = profile_obj.get("enabled").and_then(|v| v.as_bool()).unwrap_or(false);

                        let enhanced_profile = ServerProfile {
                            name: name.clone(),
                            enabled,
                            folder_path: String::new(), // Will be populated on next scan
                            detected_executables: Vec::new(),
                            primary_executable: None,
                            server_type: None,
                            last_verified: None,
                            verification_status: "needs_scan".to_string(),
                            error_message: None,
                            metadata: HashMap::new(),
                        };

                        prefs.server_profiles.insert(name.clone(), enhanced_profile);
                    }
                }
            }

            // Copy other fields that might exist
            if let Some(plugin_states) = obj.get("plugin_states").and_then(|v| v.as_object()) {
                for (key, value) in plugin_states {
                    if let Some(enabled) = value.as_bool() {
                        prefs.plugin_states.insert(key.clone(), enabled);
                    }
                }
            }
        }

        println!("Successfully migrated from old format with {} server profiles", prefs.server_profiles.len());
        Ok(prefs)
    }
}


// Path functions using user preferences
#[tauri::command]
pub async fn get_indexed_directory() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.indexed_directory)
}

#[tauri::command]
pub async fn get_system_log_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.system_log_path)
}

#[tauri::command]
pub async fn get_plugins_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.plugins_path)
}

#[tauri::command]
pub async fn get_mcps_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.mcps_path)
}

#[tauri::command]
pub async fn get_apis_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.apis_path)
}

#[tauri::command]
pub async fn get_models_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    // Ensure we return an absolute path
    let path = if prefs.models_path.starts_with("./") || !std::path::Path::new(&prefs.models_path).is_absolute() {
        resolve_path(&prefs.models_path)?.to_string_lossy().to_string()
    } else {
        prefs.models_path
    };
    Ok(path)
}

#[tauri::command]
pub async fn get_servers_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.servers_path)
}

#[tauri::command]
pub async fn get_system_prompts_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.system_prompts_path)
}

#[tauri::command]
pub async fn get_logic_hub_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.logic_hub_path)
}

#[tauri::command]
pub async fn get_logic_system_prompts_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.logic_system_prompts_path)
}

#[tauri::command]
pub async fn get_logic_modals_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.logic_modals_path)
}

#[tauri::command]
pub async fn get_logic_agents_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.logic_agents_path)
}

#[tauri::command]
pub async fn get_extensions_path() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.extensions_path)
}

// Set commands for path configuration - now saves to user preferences
#[tauri::command]
pub async fn set_indexed_directory(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.indexed_directory = path.clone();
    prefs.save()?;
    Ok(format!("Indexed directory path set to: {}", path))
}

#[tauri::command]
pub async fn set_system_log_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.system_log_path = path.clone();
    prefs.save()?;
    Ok(format!("System log path set to: {}", path))
}

#[tauri::command]
pub async fn set_plugins_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.plugins_path = path.clone();
    prefs.save()?;
    Ok(format!("Plugins path set to: {}", path))
}

#[tauri::command]
pub async fn set_mcps_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.mcps_path = path.clone();
    prefs.save()?;
    Ok(format!("MCPs path set to: {}", path))
}

#[tauri::command]
pub async fn set_apis_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.apis_path = path.clone();
    prefs.save()?;
    Ok(format!("APIs path set to: {}", path))
}

#[tauri::command]
pub async fn set_models_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.models_path = path.clone();
    prefs.save()?;
    Ok(format!("Models path set to: {}", path))
}

#[tauri::command]
pub async fn set_servers_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.servers_path = path.clone();
    prefs.save()?;
    Ok(format!("Servers path set to: {}", path))
}

#[tauri::command]
pub async fn set_system_prompts_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.system_prompts_path = path.clone();
    prefs.save()?;
    Ok(format!("System prompts path set to: {}", path))
}

#[tauri::command]
pub async fn set_logic_hub_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.logic_hub_path = path.clone();
    prefs.save()?;
    Ok(format!("Logic hub path set to: {}", path))
}

#[tauri::command]
pub async fn set_logic_system_prompts_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.logic_system_prompts_path = path.clone();
    prefs.save()?;
    Ok(format!("Logic system prompts path set to: {}", path))
}

#[tauri::command]
pub async fn set_logic_modals_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.logic_modals_path = path.clone();
    prefs.save()?;
    Ok(format!("Logic modals path set to: {}", path))
}

#[tauri::command]
pub async fn set_logic_agents_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.logic_agents_path = path.clone();
    prefs.save()?;
    Ok(format!("Logic agents path set to: {}", path))
}

#[tauri::command]
pub async fn set_extensions_path(path: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.extensions_path = path.clone();
    prefs.save()?;
    Ok(format!("Extensions path set to: {}", path))
}

// User preferences commands (for userpref.config)
#[tauri::command]
pub async fn load_user_settings() -> Result<UserPreferences, String> {
    Ok(UserPreferences::load())
}

#[tauri::command]
pub async fn save_user_settings(settings: UserPreferences) -> Result<String, String> {
    settings.save().map(|_| "User settings saved successfully.".to_string())
}

// Enhanced server detection and verification
pub fn detect_server_executables(folder_path: &std::path::Path) -> Vec<ServerExecutable> {
    let mut executables = Vec::new();

    // Comprehensive list of executable types with priorities
    let executable_patterns = vec![
        ("llama-server.exe", "llamacpp", 0), // Highest priority for llama-server
        (".exe", "exe", 1),           // Windows executables (highest priority)
        (".cpp", "cpp", 1),           // C++ executables
        (".bat", "batch", 2),         // Windows batch files
        (".cmd", "batch", 2),         // Windows command files
        (".ps1", "powershell", 3),    // PowerShell scripts
        (".py", "python", 4),         // Python scripts
        (".pyw", "python", 4),        // Python Windows scripts
        (".jar", "java", 5),          // Java applications
        (".sh", "shell", 6),          // Shell scripts
        (".bash", "shell", 6),        // Bash scripts
        (".zsh", "shell", 6),         // Zsh scripts
        (".fish", "shell", 6),        // Fish scripts
        (".rb", "ruby", 7),           // Ruby scripts
        (".pl", "perl", 7),           // Perl scripts
        (".php", "php", 7),           // PHP scripts
        (".js", "nodejs", 8),         // Node.js scripts
        (".ts", "typescript", 8),     // TypeScript scripts
        (".go", "go", 9),             // Go binaries
        (".dll", "library", 10),      // Dynamic libraries
        (".so", "library", 10),       // Shared objects (Unix)
        (".dylib", "library", 10),    // Dynamic libraries (macOS)
        (".app", "macos", 2),         // macOS applications
    ];

    if folder_path.exists() && folder_path.is_dir() {
        if let Ok(entries) = std::fs::read_dir(folder_path) {
            for entry in entries {
                if let Ok(entry) = entry {
                    let path = entry.path();
                    if path.is_file() {
                        let file_name = path.file_name()
                            .and_then(|n| n.to_str())
                            .unwrap_or("")
                            .to_string();

                        // Check against known executable patterns
                        for (ext, exec_type, priority) in &executable_patterns {
                            if file_name.to_lowercase().ends_with(&ext.to_lowercase()) {
                                executables.push(ServerExecutable {
                                    path: path.to_string_lossy().to_string(),
                                    file_name: file_name.clone(),
                                    executable_type: exec_type.to_string(),
                                    priority: *priority,
                                });
                                break;
                            }
                        }

                        // Check for extensionless files on Unix systems
                        if !file_name.contains('.') {
                            #[cfg(unix)]
                            {
                                if let Ok(metadata) = std::fs::metadata(&path) {
                                    use std::os::unix::fs::PermissionsExt;
                                    if metadata.permissions().mode() & 0o111 != 0 {
                                        executables.push(ServerExecutable {
                                            path: path.to_string_lossy().to_string(),
                                            file_name: file_name.clone(),
                                            executable_type: "unix_executable".to_string(),
                                            priority: 1, // High priority for Unix executables
                                        });
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }

    // Sort by priority (lower number = higher priority)
    executables.sort_by_key(|e| e.priority);
    executables
}

pub fn identify_server_type(_folder_name: &str, executables: &[ServerExecutable]) -> Option<String> {
    // Check for specific executables to identify server type
    for exec in executables {
        if exec.file_name == "llama-server.exe" {
            return Some("llamacpp".to_string());
        }
        if exec.file_name == "ollama.exe" {
            return Some("ollama".to_string());
        }
    }

    // Fallback for less specific cases
    if executables.len() == 1 {
        return Some("ollama".to_string());
    }
    if executables.len() > 1 {
        return Some("llamacpp".to_string());
    }
    None
}

// Validate ServerProfile data integrity
pub fn validate_server_profile(profile: &ServerProfile) -> Result<(), String> {
    // Check if name is not empty
    if profile.name.trim().is_empty() {
        return Err("Server profile name cannot be empty".to_string());
    }

    // Check if folder_path exists (if specified)
    if !profile.folder_path.is_empty() {
        let folder_path = std::path::Path::new(&profile.folder_path);
        if !folder_path.exists() {
            return Err(format!("Server folder does not exist: {}", profile.folder_path));
        }
        if !folder_path.is_dir() {
            return Err(format!("Server path is not a directory: {}", profile.folder_path));
        }
    }

    // Validate primary executable if specified
    if let Some(primary_exec) = &profile.primary_executable {
        let exec_path = std::path::Path::new(&primary_exec.path);
        if !exec_path.exists() {
            return Err(format!("Primary executable does not exist: {}", primary_exec.path));
        }
        if !exec_path.is_file() {
            return Err(format!("Primary executable is not a file: {}", primary_exec.path));
        }
    }

    // Validate detected executables
    for executable in &profile.detected_executables {
        let exec_path = std::path::Path::new(&executable.path);
        if !exec_path.exists() {
            return Err(format!("Detected executable does not exist: {}", executable.path));
        }
        if !exec_path.is_file() {
            return Err(format!("Detected executable is not a file: {}", executable.path));
        }

        // Validate executable type is not empty
        if executable.executable_type.trim().is_empty() {
            return Err(format!("Executable type cannot be empty for: {}", executable.path));
        }

        // Validate priority is reasonable
        if executable.priority < 0 || executable.priority > 100 {
            return Err(format!("Executable priority must be between 0-100, got: {}", executable.priority));
        }
    }

    // Validate verification status
    let valid_statuses = ["verified", "error", "not_checked", "needs_scan"];
    if !valid_statuses.contains(&profile.verification_status.as_str()) {
        return Err(format!("Invalid verification status: {}", profile.verification_status));
    }

    Ok(())
}

// Refresh and validate a server profile by re-scanning its folder
pub fn refresh_server_profile(profile: &mut ServerProfile, servers_base_path: &str) -> Result<(), String> {
    if profile.folder_path.is_empty() {
        // Construct folder path from servers base path and profile name
        profile.folder_path = std::path::Path::new(servers_base_path)
            .join(&profile.name)
            .to_string_lossy()
            .to_string();
    }

    let folder_path = std::path::Path::new(&profile.folder_path);

    // Re-scan for executables
    profile.detected_executables = detect_server_executables(folder_path);
    profile.primary_executable = profile.detected_executables.first().cloned();
    profile.server_type = identify_server_type(&profile.name, &profile.detected_executables);

    // Update verification status
    if profile.detected_executables.is_empty() {
        profile.verification_status = "error".to_string();
        profile.error_message = Some("No executable files found in server folder".to_string());
    } else {
        profile.verification_status = "verified".to_string();
        profile.error_message = None;
    }

    // Update last verified timestamp
    profile.last_verified = Some(
        std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap_or_default()
            .as_secs() as i64
    );

    // Update metadata
    profile.metadata.insert("executable_count".to_string(), profile.detected_executables.len().to_string());
    if let Some(primary) = &profile.primary_executable {
        profile.metadata.insert("primary_type".to_string(), primary.executable_type.clone());
    }

    // Validate the refreshed profile
    validate_server_profile(profile)?;

    Ok(())
}

// Enhanced server profile management with detection and verification
#[tauri::command]
pub async fn get_server_profiles() -> Result<Vec<ServerProfile>, String> {
    info!("get_server_profiles called - starting profile load from servers path");
    let prefs = UserPreferences::load();
    let servers_path = resolve_path(&prefs.servers_path).map_err(|e| e.to_string())?;

    // Reduced logging - only log once per session
    static LOGGED_ONCE: std::sync::Once = std::sync::Once::new();
    LOGGED_ONCE.call_once(|| {
        println!("Scanning server profiles at: {}", servers_path.display());
    });

    let mut profiles = Vec::new();

    if servers_path.exists() && servers_path.is_dir() {
        for entry in std::fs::read_dir(&servers_path).map_err(|e| e.to_string())? {
            let entry = entry.map_err(|e| e.to_string())?;
            let path = entry.path();

            if path.is_dir() {
                let folder_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                // Reduced logging

                // Detect executables in this folder
                let detected_executables = detect_server_executables(&path);
                let primary_executable = detected_executables.first().cloned();
                let server_type = identify_server_type(&folder_name, &detected_executables);

                // Determine verification status
                let (verification_status, error_message) = if detected_executables.is_empty() {
                    ("error".to_string(), Some("No executable files found in server folder".to_string()))
                } else {
                    ("verified".to_string(), None)
                };

                // Check if this server is enabled in user preferences
                let enabled = prefs.server_profiles.get(&folder_name)
                    .map(|p| p.enabled)
                    .unwrap_or_else(|| {
                        // Auto-enable if only one server found, otherwise default to false
                        let auto_enable = detected_executables.len() == 1;
                        if auto_enable {
                            println!("Auto-enabling server '{}' (only server found)", folder_name);
                        }
                        auto_enable
                    });

                let current_time = std::time::SystemTime::now()
                    .duration_since(std::time::UNIX_EPOCH)
                    .unwrap_or_default()
                    .as_secs() as i64;

                let mut metadata = HashMap::new();
                metadata.insert("executable_count".to_string(), detected_executables.len().to_string());
                if let Some(primary) = &primary_executable {
                    metadata.insert("primary_type".to_string(), primary.executable_type.clone());
                }

                let mut profile = ServerProfile {
                    name: folder_name.clone(),
                    enabled,
                    folder_path: path.to_string_lossy().to_string(),
                    detected_executables,
                    primary_executable,
                    server_type,
                    last_verified: Some(current_time),
                    verification_status,
                    error_message,
                    metadata,
                };

                // Validate the profile before adding it
                info!("get_server_profiles: validating profile {:?}", profile.name);
                match validate_server_profile(&profile) {
                    Ok(()) => {
                        println!("  - ✓ {} executables, type: {:?}, status: {}",
                            profile.detected_executables.len(),
                            profile.server_type,
                            profile.verification_status
                        );
                        profiles.push(profile);
                    },
                    Err(validation_error) => {
                        println!("  - ✗ Validation failed for {}: {}", folder_name, validation_error);

                        // Update profile with validation error
                        profile.verification_status = "error".to_string();
                        profile.error_message = Some(format!("Validation failed: {}", validation_error));

                        // Still add the profile but mark it as invalid
                        profiles.push(profile);
                    }
                }
            }
        }
    } else {
        println!("Server path does not exist or is not a directory: {}", servers_path.display());
    }

    println!("Found {} server profiles", profiles.len());


    Ok(profiles)
}


// Model profile management - get from Ollama API first, fallback to folder scanning
#[tauri::command]
pub async fn get_model_profiles() -> Result<Vec<ModelProfile>, String> {
    let prefs = UserPreferences::load();
    let mut profiles = Vec::new();

    // Fallback to folder scanning
    let models_path = resolve_path(&prefs.models_path).map_err(|e| e.to_string())?;
    println!("Scanning model profiles at: {}", models_path.display());

    if models_path.exists() && models_path.is_dir() {
        for entry in std::fs::read_dir(&models_path).map_err(|e| e.to_string())? {
            let entry = entry.map_err(|e| e.to_string())?;
            let path = entry.path();

            if path.is_dir() {
                let folder_name = path.file_name()
                    .and_then(|n| n.to_str())
                    .unwrap_or("unknown")
                    .to_string();

                println!("Found model folder: {}", folder_name);

                // Check if this model is enabled in user preferences
                let enabled = prefs.model_profiles.get(&folder_name)
                    .map(|p| p.enabled)
                    .unwrap_or_else(|| {
                        // Auto-enable if this is the first model found
                        let auto_enable = profiles.is_empty();
                        if auto_enable {
                            println!("Auto-enabling model '{}' (first model found)", folder_name);
                        }
                        auto_enable
                    });

                profiles.push(ModelProfile {
                    name: folder_name,
                    enabled,
                });
            }
        }
    } else {
        println!("Model path does not exist or is not a directory: {}", models_path.display());
    }

    println!("Found {} model profiles", profiles.len());

    // Save the scanned model profiles back to UserPreferences (like we do for servers)
    if !profiles.is_empty() {
        let mut updated_prefs = prefs;

        // Update the model profiles in preferences with the scanned data
        for profile in &profiles {
            updated_prefs.model_profiles.insert(profile.name.clone(), profile.clone());
        }

        // Save the updated preferences
        if let Err(e) = updated_prefs.save() {
            eprintln!("Warning: Failed to save updated model profiles: {}", e);
        }
    }

    Ok(profiles)
}

// Toggle server profile enabled state
#[tauri::command]
pub async fn toggle_server_profile(server_name: String, enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();

    println!("=== DEBUG: Toggling server profile ===");
    println!("Server name: {}", server_name);
    println!("Enabled: {}", enabled);
    println!("Before: {:?}", prefs.server_profiles);

    // Get the current server profile if it exists, or create a minimal one for toggling
    let existing_profile = prefs.server_profiles.get(&server_name).cloned();

    let updated_profile = if let Some(mut profile) = existing_profile {
        // Update existing profile
        profile.enabled = enabled;
        profile
    } else {
        // Create new minimal profile - will be fully populated on next scan
        ServerProfile {
            name: server_name.clone(),
            enabled,
            folder_path: String::new(), // Will be populated on next scan
            detected_executables: Vec::new(),
            primary_executable: None,
            server_type: None,
            last_verified: None,
            verification_status: "not_checked".to_string(),
            error_message: None,
            metadata: HashMap::new(),
        }
    };

    prefs.server_profiles.insert(server_name.clone(), updated_profile);

    println!("After: {:?}", prefs.server_profiles);

    prefs.save()?;
    println!("Saved preferences successfully");

    Ok(format!("Server {} {}", server_name, if enabled { "enabled" } else { "disabled" }))
}

// Toggle model profile enabled state
#[tauri::command]
pub async fn toggle_model_profile(model_name: String, enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();

    prefs.model_profiles.insert(model_name.clone(), ModelProfile {
        name: model_name.clone(),
        enabled,
    });

    prefs.save()?;
    Ok(format!("Model {} {}", model_name, if enabled { "enabled" } else { "disabled" }))
}

// Save a server profile
#[tauri::command]
pub async fn save_server_profile(profile: ServerProfile) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.server_profiles.insert(profile.name.clone(), profile);
    prefs.save()?;
    Ok("Server profile saved successfully.".to_string())
}

// Delete a server profile
#[tauri::command]
pub async fn delete_server_profile(server_name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    if prefs.server_profiles.remove(&server_name).is_some() {
        prefs.save()?;
        Ok(format!("Server profile {} deleted successfully.", server_name))
    } else {
        Err(format!("Server profile {} not found.", server_name))
    }
}

// Save a model profile
#[tauri::command]
pub async fn save_model_profile(profile: ModelProfile) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.model_profiles.insert(profile.name.clone(), profile);
    prefs.save()?;
    Ok("Model profile saved successfully.".to_string())
}

// Delete a model profile
#[tauri::command]
pub async fn delete_model_profile(model_name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    if prefs.model_profiles.remove(&model_name).is_some() {
        prefs.save()?;
        Ok(format!("Model profile {} deleted successfully.", model_name))
    } else {
        Err(format!("Model profile {} not found.", model_name))
    }
}

// Active Server Profile Management
#[tauri::command]
pub async fn set_active_server_profile(server_name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    
    // Validate that the server profile exists and is enabled
    if let Some(profile) = prefs.server_profiles.get(&server_name) {
        if !profile.enabled {
            return Err(format!("Cannot activate disabled server profile: {}", server_name));
        }
        if profile.verification_status == "error" {
            return Err(format!("Cannot activate server profile with errors: {}", server_name));
        }
    } else {
        return Err(format!("Server profile not found: {}", server_name));
    }
    
    prefs.active_server_profile = server_name.clone();
    prefs.save()?;
    Ok(format!("Active server profile set to: {}", server_name))
}

#[tauri::command]
pub async fn get_active_server_profile() -> Result<Option<ServerProfile>, String> {
    let prefs = UserPreferences::load();
    
    if prefs.active_server_profile.is_empty() {
        // Auto-select first enabled server if none is active
        for (name, profile) in &prefs.server_profiles {
            if profile.enabled && profile.verification_status == "verified" {
                // Auto-set as active
                let _ = set_active_server_profile(name.clone()).await;
                return Ok(Some(profile.clone()));
            }
        }
        return Ok(None);
    }
    
    if let Some(profile) = prefs.server_profiles.get(&prefs.active_server_profile) {
        if profile.enabled {
            Ok(Some(profile.clone()))
        } else {
            Ok(None)
        }
    } else {
        Ok(None)
    }
}

#[tauri::command]
pub async fn clear_active_server_profile() -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.active_server_profile = String::new();
    prefs.save()?;
    Ok("Active server profile cleared".to_string())
}

// Active Model Profile Management
#[tauri::command]
pub async fn set_active_model_profile(model_name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    
    // Validate that the model profile exists and is enabled
    if let Some(profile) = prefs.model_profiles.get(&model_name) {
        if !profile.enabled {
            return Err(format!("Cannot activate disabled model profile: {}", model_name));
        }
    } else {
        return Err(format!("Model profile not found: {}", model_name));
    }
    
    prefs.active_model_profile = model_name.clone();
    prefs.save()?;
    Ok(format!("Active model profile set to: {}", model_name))
}

#[tauri::command]
pub async fn get_active_model_profile() -> Result<Option<ModelProfile>, String> {
    let prefs = UserPreferences::load();
    
    if prefs.active_model_profile.is_empty() {
        // Auto-select first enabled model if none is active
        for (name, profile) in &prefs.model_profiles {
            if profile.enabled {
                // Auto-set as active
                let _ = set_active_model_profile(name.clone()).await;
                return Ok(Some(profile.clone()));
            }
        }
        return Ok(None);
    }
    
    if let Some(profile) = prefs.model_profiles.get(&prefs.active_model_profile) {
        if profile.enabled {
            Ok(Some(profile.clone()))
        } else {
            Ok(None)
        }
    } else {
        Ok(None)
    }
}

#[tauri::command]
pub async fn clear_active_model_profile() -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.active_model_profile = String::new();
    prefs.save()?;
    Ok("Active model profile cleared".to_string())
}

// Get server configuration for AI client
#[tauri::command]
pub async fn get_active_server_config() -> Result<Option<serde_json::Value>, String> {
    let active_profile = get_active_server_profile().await?;
    
    if let Some(profile) = active_profile {
        let config = serde_json::json!({
            "name": profile.name,
            "server_type": profile.server_type,
            "folder_path": profile.folder_path,
            "primary_executable": profile.primary_executable,
            "base_url": "http://127.0.0.1:11435", // Default, can be enhanced later
            "verification_status": profile.verification_status
        });
        Ok(Some(config))
    } else {
        Ok(None)
    }
}

// Refresh and validate a specific server profile
#[tauri::command]
pub async fn refresh_server_profile_command(server_name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    let servers_path = resolve_path(&prefs.servers_path).map_err(|e| e.to_string())?;

    if let Some(profile) = prefs.server_profiles.get_mut(&server_name) {
        match refresh_server_profile(profile, &servers_path.to_string_lossy()) {
            Ok(()) => {
                prefs.save()?;
                Ok(format!("Successfully refreshed server profile: {}", server_name))
            },
            Err(e) => Err(format!("Failed to refresh server profile {}: {}", server_name, e))
        }
    } else {
        Err(format!("Server profile not found: {}", server_name))
    }
}

// Validate all server profiles
#[tauri::command]
pub async fn validate_all_server_profiles() -> Result<String, String> {
    let prefs = UserPreferences::load();
    let mut validation_results = Vec::new();
    let mut valid_count = 0;
    let mut invalid_count = 0;

    for (name, profile) in &prefs.server_profiles {
        match validate_server_profile(profile) {
            Ok(()) => {
                validation_results.push(format!("✓ {}: Valid", name));
                valid_count += 1;
            },
            Err(e) => {
                validation_results.push(format!("✗ {}: {}", name, e));
                invalid_count += 1;
            }
        }
    }

    let summary = format!(
        "Validation Summary:\n- Valid profiles: {}\n- Invalid profiles: {}\n\nDetails:\n{}",
        valid_count,
        invalid_count,
        validation_results.join("\n")
    );

    Ok(summary)
}

// Get current config version
#[tauri::command]
pub async fn get_config_version() -> Result<u32, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.config_version)
}

// Force migration to latest version (for debugging/recovery)
#[tauri::command]
pub async fn force_config_migration() -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    let original_version = prefs.config_version;

    // Force migration regardless of current version
    prefs = UserPreferences::migrate_to_v2(prefs);

    match prefs.save() {
        Ok(()) => Ok(format!(
            "Successfully migrated configuration from version {} to version {}",
            original_version, prefs.config_version
        )),
        Err(e) => Err(format!("Migration successful but failed to save: {}", e))
    }
}

// Projection Settings Commands
#[tauri::command]
pub async fn get_ios_projection_enabled() -> Result<bool, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.ios_projection_enabled)
}

#[tauri::command]
pub async fn set_ios_projection_enabled(enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.ios_projection_enabled = enabled;
    prefs.save()?;
    Ok(format!("iOS projection {}", if enabled { "enabled" } else { "disabled" }))
}

#[tauri::command]
pub async fn get_ios_device_name() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.ios_device_name)
}

#[tauri::command]
pub async fn set_ios_device_name(name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.ios_device_name = name;
    prefs.save()?;
    Ok("iOS device name updated".to_string())
}

#[tauri::command]
pub async fn get_ios_projection_quality() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.ios_projection_quality)
}

#[tauri::command]
pub async fn set_ios_projection_quality(quality: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.ios_projection_quality = quality;
    prefs.save()?;
    Ok("iOS projection quality updated".to_string())
}

#[tauri::command]
pub async fn get_android_projection_enabled() -> Result<bool, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.android_projection_enabled)
}

#[tauri::command]
pub async fn set_android_projection_enabled(enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.android_projection_enabled = enabled;
    prefs.save()?;
    Ok(format!("Android projection {}", if enabled { "enabled" } else { "disabled" }))
}

#[tauri::command]
pub async fn get_android_device_name() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.android_device_name)
}

#[tauri::command]
pub async fn set_android_device_name(name: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.android_device_name = name;
    prefs.save()?;
    Ok("Android device name updated".to_string())
}

#[tauri::command]
pub async fn get_android_projection_quality() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.android_projection_quality)
}

#[tauri::command]
pub async fn set_android_projection_quality(quality: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.android_projection_quality = quality;
    prefs.save()?;
    Ok("Android projection quality updated".to_string())
}

#[tauri::command]
pub async fn get_miracast_enabled() -> Result<bool, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.miracast_enabled)
}

#[tauri::command]
pub async fn set_miracast_enabled(enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.miracast_enabled = enabled;
    prefs.save()?;
    Ok(format!("Miracast {}", if enabled { "enabled" } else { "disabled" }))
}

#[tauri::command]
pub async fn get_dlna_enabled() -> Result<bool, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.dlna_enabled)
}

#[tauri::command]
pub async fn set_dlna_enabled(enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.dlna_enabled = enabled;
    prefs.save()?;
    Ok(format!("DLNA {}", if enabled { "enabled" } else { "disabled" }))
}

#[tauri::command]
pub async fn get_projection_local_only() -> Result<bool, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.projection_local_only)
}

#[tauri::command]
pub async fn set_projection_local_only(enabled: bool) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.projection_local_only = enabled;
    prefs.save()?;
    Ok(format!("Local network only {}", if enabled { "enabled" } else { "disabled" }))
}

#[tauri::command]
pub async fn get_projection_port() -> Result<u16, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.projection_port)
}

#[tauri::command]
pub async fn set_projection_port(port: u16) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.projection_port = port;
    prefs.save()?;
    Ok(format!("Projection port set to {}", port))
}

#[tauri::command]
pub async fn get_projection_protocol() -> Result<String, String> {
    let prefs = UserPreferences::load();
    Ok(prefs.projection_protocol)
}

#[tauri::command]
pub async fn set_projection_protocol(protocol: String) -> Result<String, String> {
    let mut prefs = UserPreferences::load();
    prefs.projection_protocol = protocol;
    prefs.save()?;
    Ok("Projection protocol updated".to_string())
}



#[derive(Debug, Serialize, Deserialize)]
pub struct DirectoryInfo {
    pub path: String,
    pub exists: bool,
    pub files: Vec<String>,
    pub directories: Vec<String>,
    pub error: Option<String>,
}

#[tauri::command]
pub async fn get_directory_info(path: String) -> Result<DirectoryInfo, String> {


    // Resolve relative paths to absolute paths using the same logic as other functions
    let dir_path = resolve_path(&path)?;

    if !dir_path.exists() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: false,
            files: vec![],
            directories: vec![],
            error: Some(format!("Directory does not exist: {}", dir_path.display())),
        });
    }

    if !dir_path.is_dir() {
        return Ok(DirectoryInfo {
            path: path.clone(),
            exists: true,
            files: vec![],
            directories: vec![],
            error: Some("Path is not a directory".to_string()),
        });
    }

    let mut files = Vec::new();
    let mut directories = Vec::new();

    match fs::read_dir(&dir_path) {
        Ok(entries) => {
            for entry in entries {
                match entry {
                    Ok(entry) => {
                        let file_name = entry.file_name().to_string_lossy().to_string();
                        if entry.path().is_dir() {
                            directories.push(file_name);
                        } else {
                            files.push(file_name);
                        }
                    }
                    Err(e) => {
                        return Ok(DirectoryInfo {
                            path: path.clone(),
                            exists: true,
                            files,
                            directories,
                            error: Some(format!("Error reading entry: {}", e)),
                        });
                    }
                }
            }
        }
        Err(e) => {
            return Ok(DirectoryInfo {
                path: path.clone(),
                exists: true,
                files: vec![],
                directories: vec![],
                error: Some(format!("Error reading directory: {}", e)),
            });
        }
    }

    // Sort for consistent output
    files.sort();
    directories.sort();

    Ok(DirectoryInfo {
        path: path.clone(),
        exists: true,
        files,
        directories,
        error: None,
    })
}
