@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Void Circuit Color Palette */
    --void-black: 225 14% 8%;      /* #0F1115 */
    --purple-pulse: 289 64% 50%;   /* #8E24AA */
    --dark-steel: 217 18% 22%;     /* #1C1F26 */
    --warm-orange: 16 100% 60%;    /* #FF7043 */
    --soft-silver: 200 13% 86%;    /* #CFD8DC */
    
    /* Base colors */
    --background: var(--void-black);
    --foreground: var(--soft-silver);
    --card: var(--dark-steel);
    --card-foreground: var(--soft-silver);
    --popover: var(--dark-steel);
    --popover-foreground: var(--soft-silver);
    
    /* Primary colors */
    --primary: var(--purple-pulse);
    --primary-foreground: 0 0% 100%;
    
    /* Secondary colors */
    --secondary: var(--dark-steel);
    --secondary-foreground: var(--soft-silver);
    
    /* Muted colors */
    --muted: var(--dark-steel);
    --muted-foreground: 200 10% 70%;
    
    /* Accent colors */
    --accent: var(--warm-orange);
    --accent-foreground: 0 0% 100%;
    
    /* Destructive colors */
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    
    /* Border colors */
    --border: 220 13% 25%;
    --input: 220 13% 25%;
    --ring: var(--purple-pulse);
    
    /* Border radius */
    --radius: 0.5rem;
    
    /* NEW: Status Colors */
    --status-online: #10B981;
    --status-offline: #6B7280;
    --status-error: #EF4444;
    --status-warning: #F59E0B;
    --status-loading: #3B82F6;
    --status-idle: #8B5CF6;
    
    /* Enhanced Shadows */
    --shadow-glow: 0 0 25px rgba(142, 36, 170, 0.3);
    --shadow-card: 0 4px 15px -3px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 8px 30px -10px rgba(0, 0, 0, 0.4);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.25);
    
    /* Gradients */
    --gradient-primary: linear-gradient(135deg, var(--purple-pulse) 0%, #BA68C8 100%);
    --gradient-accent: linear-gradient(135deg, var(--warm-orange) 0%, #FF8A65 100%);
    
    /* Animation */
    --transition-fast: 150ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-normal: 250ms cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: 350ms cubic-bezier(0.4, 0, 0.2, 1);
    
    /* Border radius */
    --radius-sm: 0.25rem;
    --radius-md: 0.5rem;
    --radius-lg: 1rem;
    --radius-xl: 1.5rem;
    --radius-full: 9999px;
  }

  /* Dark theme (default for Void Circuit) */
  .dark {
    --background: var(--void-black);
    --foreground: var(--soft-silver);
    --card: var(--dark-steel);
    --card-foreground: var(--soft-silver);
    --popover: var(--dark-steel);
    --popover-foreground: var(--soft-silver);
    --primary: var(--purple-pulse);
    --primary-foreground: 0 0% 100%;
    --secondary: var(--dark-steel);
    --secondary-foreground: var(--soft-silver);
    --muted: var(--dark-steel);
    --muted-foreground: 200 10% 70%;
    --accent: var(--warm-orange);
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 100%;
    --border: 220 13% 25%;
    --input: 220 13% 25%;
    --ring: var(--purple-pulse);
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    /* NEW: Enhanced Shadows for dark theme */
    --shadow-glow: 0 0 20px rgba(139, 92, 246, 0.25);
    --shadow-card: 0 4px 6px -1px rgba(0, 0, 0, 0.3);
    --shadow-elevated: 0 8px 25px -5px rgba(0, 0, 0, 0.4);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-clamp: 2;
  }
  
  /* NEW: Enhanced shadow utilities */
  .glow-shadow {
    box-shadow: var(--shadow-glow);
  }
  .card-shadow {
    box-shadow: var(--shadow-card);
  }
  .elevated-shadow {
    box-shadow: var(--shadow-elevated);
  }
  
  /* Frosted Glass Effect */
  .frosted-glass {
    -webkit-backdrop-filter: blur(12px);
    backdrop-filter: blur(12px);
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }
  
  .dark .frosted-glass {
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
    /* Background Patterns */
  .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(0, 0, 0, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(0, 0, 0, 0.1) 1px, transparent 1px);
    background-size: 24px 24px;
  }
  
  .dark .bg-grid-pattern {
    background-image: 
      linear-gradient(rgba(255, 255, 255, 0.05) 1px, transparent 1px),
      linear-gradient(90deg, rgba(255, 255, 255, 0.05) 1px, transparent 1px);
  }
  
  .bg-noise-pattern {
    background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)' opacity='0.1'/%3E%3C/svg%3E");
  }
  
  .dark .bg-noise-pattern {
    opacity: 0.05;
  }
  
  .bg-circuit-pattern {
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(142, 36, 170, 0.1) 0%, transparent 50%),
      linear-gradient(135deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%) -10px 0,
      linear-gradient(225deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%) -10px 0,
      linear-gradient(315deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%),
      linear-gradient(45deg, rgba(0, 0, 0, 0.1) 25%, transparent 25%);
    background-size: 20px 20px;
  }
  
  .dark .bg-circuit-pattern {
    background-image: 
      radial-gradient(circle at 25% 25%, rgba(142, 36, 170, 0.2) 0%, transparent 50%),
      linear-gradient(135deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%) -10px 0,
      linear-gradient(225deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%) -10px 0,
      linear-gradient(315deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%),
      linear-gradient(45deg, rgba(255, 255, 255, 0.05) 25%, transparent 25%);
  }
}

/* Enhanced Animations */
@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

.animate-fadeIn {
  animation: fadeIn 0.3s ease-out forwards;
}

.animate-shake {
  animation: shake 0.5s ease-in-out;
}

/* Smooth transitions for all interactive elements */
* {
  transition-property: background-color, border-color, color, fill, stroke, opacity, box-shadow, transform, filter;
  transition-duration: 150ms;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  
  /* Better text rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 200ms;
}

html, body {
  height: 100%;
}

body {
  margin: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen',
    'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

code {
  font-family: source-code-pro, Menlo, Monaco, Consolas, 'Courier New',
    monospace;
}

* {
  box-sizing: border-box;
}