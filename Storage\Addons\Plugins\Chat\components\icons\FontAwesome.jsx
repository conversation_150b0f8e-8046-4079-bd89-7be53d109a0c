import React from 'react';

// Simple FontAwesome icon component replacement for Chat plugin
const FontAwesome = ({ icon, className, ...props }) => {
  const iconMap = {
    // Chat specific icons
    faPaperPlane: '✈️',
    faRobot: '🤖',
    faUser: '👤',
    faSpinner: '⟳',
    faRefresh: '🔄',
    faCog: '⚙️',
    faPaperclip: '📎',
    faMicrophone: '🎤',
    faLightbulb: '💡',
    faInfoCircle: 'ℹ️',
    faSearch: '🔍',
    faPlus: '+',
    faBars: '☰',
    faEllipsisV: '⋮',
    faDownload: '⬇️',
    faTrash: '🗑️',
    faEdit: '✏️',
    faBookmark: '🔖',
    faCopy: '📋',
    faRedo: '↻',
    faExpand: '⛶',
    faCompress: '⛶',
    faTimes: '✕',
    faFile: '📄',
    faImage: '🖼️',
    faPlay: '▶️',
    faPause: '⏸️',
    faStop: '⏹️',
    faHeart: '❤️',
    faThumbsUp: '👍',
    faThumbsDown: '👎',
    faShare: '📤',
    faCode: '</>', 
    faMarkdown: 'M',
    faArrowLeft: '←',
    faPalette: '🎨',
    faVolumeUp: '🔊',
    faKeyboard: '⌨️',
    faDatabase: '🗄️',
    faShield: '🛡️',
    faUpload: '⬆️',
    faSave: '💾',
    faCalendar: '📅',
    faCheck: '✓',
    faFilter: '🔽',
    faArrowUp: '↑',
    faArrowDown: '↓',
    faHighlighter: '🖍️',
    faSmile: '😊',
    faMagicWandSparkles: '✨',
    faTemperatureHalf: '🌡️'
  };

  const iconSymbol = iconMap[icon?.iconName] || iconMap[icon] || '?';
  
  return (
    <span className={className} {...props}>
      {iconSymbol}
    </span>
  );
};

// Export individual icons for easier imports
export const faPaperPlane = { iconName: 'faPaperPlane' };
export const faRobot = { iconName: 'faRobot' };
export const faUser = { iconName: 'faUser' };
export const faSpinner = { iconName: 'faSpinner' };
export const faRefresh = { iconName: 'faRefresh' };
export const faCog = { iconName: 'faCog' };
export const faPaperclip = { iconName: 'faPaperclip' };
export const faMicrophone = { iconName: 'faMicrophone' };
export const faLightbulb = { iconName: 'faLightbulb' };
export const faInfoCircle = { iconName: 'faInfoCircle' };
export const faSearch = { iconName: 'faSearch' };
export const faPlus = { iconName: 'faPlus' };
export const faBars = { iconName: 'faBars' };
export const faEllipsisV = { iconName: 'faEllipsisV' };
export const faDownload = { iconName: 'faDownload' };
export const faTrash = { iconName: 'faTrash' };
export const faEdit = { iconName: 'faEdit' };
export const faBookmark = { iconName: 'faBookmark' };
export const faCopy = { iconName: 'faCopy' };
export const faRedo = { iconName: 'faRedo' };
export const faExpand = { iconName: 'faExpand' };
export const faCompress = { iconName: 'faCompress' };
export const faTimes = { iconName: 'faTimes' };
export const faFile = { iconName: 'faFile' };
export const faImage = { iconName: 'faImage' };
export const faPlay = { iconName: 'faPlay' };
export const faPause = { iconName: 'faPause' };
export const faStop = { iconName: 'faStop' };
export const faHeart = { iconName: 'faHeart' };
export const faThumbsUp = { iconName: 'faThumbsUp' };
export const faThumbsDown = { iconName: 'faThumbsDown' };
export const faShare = { iconName: 'faShare' };
export const faCode = { iconName: 'faCode' };
export const faMarkdown = { iconName: 'faMarkdown' };
export const faArrowLeft = { iconName: 'faArrowLeft' };
export const faPalette = { iconName: 'faPalette' };
export const faVolumeUp = { iconName: 'faVolumeUp' };
export const faKeyboard = { iconName: 'faKeyboard' };
export const faDatabase = { iconName: 'faDatabase' };
export const faShield = { iconName: 'faShield' };
export const faUpload = { iconName: 'faUpload' };
export const faSave = { iconName: 'faSave' };
export const faCalendar = { iconName: 'faCalendar' };
export const faCheck = { iconName: 'faCheck' };
export const faFilter = { iconName: 'faFilter' };
export const faArrowUp = { iconName: 'faArrowUp' };
export const faArrowDown = { iconName: 'faArrowDown' };
export const faHighlighter = { iconName: 'faHighlighter' };
export const faSmile = { iconName: 'faSmile' };
export const faMagicWandSparkles = { iconName: 'faMagicWandSparkles' };
export const faTemperatureHalf = { iconName: 'faTemperatureHalf' };

export { FontAwesome as FontAwesomeIcon };
export default FontAwesome;