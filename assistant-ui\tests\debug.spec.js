import { test, expect } from '@playwright/test';

test('check basic page load', async ({ page }) => {
  // Set a longer timeout for navigation
  page.setDefaultTimeout(60000);
  
  try {
    await page.goto('http://localhost:3000', { timeout: 60000 });
    
    // Check that the page title is correct
    await expect(page).toHaveTitle(/The Collective/);
    
    // Check that core navigation tabs are present
    await expect(page.getByRole('link', { name: 'Dashboard' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Chat' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Tools' })).toBeVisible();
    await expect(page.getByRole('link', { name: 'Settings' })).toBeVisible();
  } catch (error) {
    console.log('Navigation error:', error.message);
    throw error;
  }
});

test('test basic navigation', async ({ page }) => {
  // Set a longer timeout for navigation
  page.setDefaultTimeout(60000);
  
  try {
    await page.goto('http://localhost:3000', { timeout: 60000 });
    
    // Click on the Chat tab
    await page.getByRole('link', { name: 'Chat' }).click();
    
    // Click on the Tools tab
    await page.getByRole('link', { name: 'Tools' }).click();
    
    // Click on the Settings tab
    await page.getByRole('link', { name: 'Settings' }).click();
    
    // Click on the Dashboard tab
    await page.getByRole('link', { name: 'Dashboard' }).click();
  } catch (error) {
    console.log('Navigation error:', error.message);
    throw error;
  }
});

test('check for critical console errors', async ({ page }) => {
  // Set a longer timeout for navigation
  page.setDefaultTimeout(60000);
  
  // Listen for console errors
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      // Filter out Tauri-related errors since they're expected in browser testing
      const text = msg.text();
      if (!text.includes('Tauri') && !text.includes('invoke') && !text.includes('transformCallback')) {
        errors.push(text);
        console.log('Non-Tauri Console error:', text);
      }
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    // Filter out Tauri-related errors
    if (!error.message.includes('Tauri') && !error.message.includes('invoke') && !error.message.includes('transformCallback')) {
      errors.push(error.message);
      console.log('Non-Tauri Page error:', error.message);
    }
  });

  try {
    await page.goto('http://localhost:3000', { timeout: 60000, waitUntil: 'networkidle' });
    
    // Wait a bit for any initial errors to appear
    await page.waitForTimeout(5000);
    
    // Check that no critical errors were captured
    expect(errors).toEqual([]);
  } catch (error) {
    console.log('Navigation error:', error.message);
    throw error;
  }
});