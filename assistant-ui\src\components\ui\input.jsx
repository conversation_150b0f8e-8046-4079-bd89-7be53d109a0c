import * as React from "react"

import { cn } from "../../lib/utils"

function Input({
  className,
  type,
  ...props
}) {
  return (
    <input
      type={type}
      data-slot="input"
      className={cn(
        "file:text-slate-700 dark:file:text-slate-300 placeholder:text-slate-400 selection:bg-blue-100 dark:selection:bg-blue-900/50 selection:text-blue-800 dark:selection:text-blue-200 bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 flex h-10 w-full min-w-0 rounded-md border px-3 py-2 text-base shadow-sm transition-all duration-200 outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",
        "hover:border-blue-300 dark:hover:border-blue-600",
        "focus-visible:border-blue-400 focus-visible:ring-blue-500/30 focus-visible:ring-[3px]",
        "aria-invalid:ring-red-500/20 dark:aria-invalid:ring-red-500/40 aria-invalid:border-red-500",
        className
      )}
      {...props} />
  );
}

export { Input }
