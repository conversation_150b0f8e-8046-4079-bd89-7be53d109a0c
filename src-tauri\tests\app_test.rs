use tauri::Manager;

#[cfg(test)]
mod tests {
    use super::*;
    use tauri::test::{mock_builder, mock_context};

    #[test]
    fn app_initializes() {
        let context = mock_context();
        let app = mock_builder(tauri::test::mock_assets())
            .invoke_handler(tauri::generate_handler![])
            .build(context)
            .expect("failed to build app");

        assert!(app.run_iteration(|| true));
    }

    #[test]
    fn app_has_main_window() {
        let context = mock_context();
        let app = mock_builder()
            .invoke_handler(tauri::generate_handler![])
            .build(context)
            .expect("failed to build app");

        let window = app.get_webview_window("main").expect("failed to get main window");
        assert_eq!(window.label(), "main");
    }
}