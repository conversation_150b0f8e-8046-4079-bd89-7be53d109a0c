import React, { forwardRef, useState } from 'react';
import { cva } from 'class-variance-authority';
import { ChevronDown } from 'lucide-react';
import { cn } from '../../lib/utils';

const Accordion = ({ children, type = 'single', collapsible = true, value, onValueChange, className, ...props }) => {
  const [openItem, setOpenItem] = useState(value);
  
  const handleItemClick = (itemValue) => {
    if (type === 'single') {
      setOpenItem(openItem === itemValue && collapsible ? null : itemValue);
      if (onValueChange) {
        onValueChange(openItem === itemValue && collapsible ? null : itemValue);
      }
    }
  };
  
  return (
    <div className={cn('w-full', className)} {...props}>
      {React.Children.map(children, child => {
        if (!React.isValidElement(child)) return child;
        
        return React.cloneElement(child, {
          open: child.props.value === (value !== undefined ? value : openItem),
          onOpenChange: (open) => handleItemClick(child.props.value)
        });
      })}
    </div>
  );
};

const AccordionItem = forwardRef(({ className, value, open, onOpenChange, children, ...props }, ref) => {
  const [isOpen, setIsOpen] = useState(open || false);
  
  React.useEffect(() => {
    setIsOpen(open || false);
  }, [open]);
  
  const handleClick = () => {
    const newOpen = !isOpen;
    setIsOpen(newOpen);
    if (onOpenChange) {
      onOpenChange(newOpen);
    }
  };
  
  return (
    <div
      ref={ref}
      data-state={isOpen ? 'open' : 'closed'}
      className={cn(
        'border-b border-border',
        className
      )}
      {...props}
    >
      {React.Children.map(children, child => {
        if (!React.isValidElement(child)) return child;
        
        if (child.type.displayName === 'AccordionTrigger') {
          return React.cloneElement(child, { onClick: handleClick, open: isOpen });
        }
        
        if (child.type.displayName === 'AccordionContent') {
          return React.cloneElement(child, { open: isOpen });
        }
        
        return child;
      })}
    </div>
  );
});
AccordionItem.displayName = 'AccordionItem';

const AccordionTrigger = forwardRef(({ className, children, onClick, open, ...props }, ref) => (
  <button
    ref={ref}
    type="button"
    onClick={onClick}
    data-state={open ? 'open' : 'closed'}
    className={cn(
      'flex flex-1 items-center justify-between py-4 font-medium transition-all hover:underline',
      className
    )}
    {...props}
  >
    {children}
    <ChevronDown className={cn("h-4 w-4 shrink-0 transition-transform duration-200", open && "rotate-180")} />
  </button>
));
AccordionTrigger.displayName = 'AccordionTrigger';

const AccordionContent = forwardRef(({ className, children, open, ...props }, ref) => (
  <div
    ref={ref}
    data-state={open ? 'open' : 'closed'}
    className={cn(
      'overflow-hidden text-sm transition-all data-[state=closed]:animate-accordion-up data-[state=open]:animate-accordion-down',
      !open && 'h-0',
      className
    )}
    {...props}
  >
    <div className="pb-4 pt-0">{children}</div>
  </div>
));
AccordionContent.displayName = 'AccordionContent';

export { Accordion, AccordionItem, AccordionTrigger, AccordionContent };