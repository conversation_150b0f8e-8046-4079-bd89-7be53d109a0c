import React, { createContext, useContext, useEffect, useRef, useState } from 'react';
import { createPortal } from 'react-dom';

/**
 * Portal Context for managing stable portal containers
 * This prevents Radix UI portal cleanup errors during HMR
 */
const PortalContext = createContext({
  portalContainer: null,
  isReady: false
});

export const usePortal = () => {
  const context = useContext(PortalContext);
  if (!context) {
    throw new Error('usePortal must be used within a PortalProvider');
  }
  return context;
};

export const PortalProvider = ({ children }) => {
  const [portalContainer, setPortalContainer] = useState(null);
  const [isReady, setIsReady] = useState(false);
  const cleanupRef = useRef([]);

  useEffect(() => {
    // Get or create the stable portal container
    let container = document.getElementById('portal-root');
    
    if (!container) {
      // Fallback: create the container if it doesn't exist
      container = document.createElement('div');
      container.id = 'portal-root';
      container.style.position = 'relative';
      container.style.zIndex = '9999';
      document.body.appendChild(container);
      
      // Add to cleanup list
      cleanupRef.current.push(() => {
        if (container && container.parentNode) {
          try {
            container.parentNode.removeChild(container);
          } catch (error) {
            console.warn('Portal cleanup error (expected during HMR):', error);
          }
        }
      });
    }

    setPortalContainer(container);
    setIsReady(true);

    // Cleanup function for development HMR
    return () => {
      // Don't actually remove the container during HMR
      // Just mark it as not ready
      setIsReady(false);
      
      // Run any registered cleanup functions
      cleanupRef.current.forEach(cleanup => {
        try {
          cleanup();
        } catch (error) {
          console.warn('Portal cleanup error (expected during HMR):', error);
        }
      });
      cleanupRef.current = [];
    };
  }, []);

  // Add HMR cleanup for development
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      const handleBeforeUnload = () => {
        // Clear any pending portal operations
        setIsReady(false);
      };

      window.addEventListener('beforeunload', handleBeforeUnload);
      
      return () => {
        window.removeEventListener('beforeunload', handleBeforeUnload);
      };
    }
  }, []);

  const value = {
    portalContainer,
    isReady,
    addCleanup: (cleanupFn) => {
      if (typeof cleanupFn === 'function') {
        cleanupRef.current.push(cleanupFn);
      }
    }
  };

  return (
    <PortalContext.Provider value={value}>
      {children}
    </PortalContext.Provider>
  );
};

/**
 * Safe Portal component that uses the stable container
 */
export const SafePortal = ({ children, container = null }) => {
  const { portalContainer, isReady } = usePortal();
  const [fallbackMode, setFallbackMode] = useState(false);

  // Use provided container or fall back to context container
  const targetContainer = container || portalContainer;

  useEffect(() => {
    // Reset fallback mode when container becomes available
    if (targetContainer && isReady) {
      setFallbackMode(false);
    }
  }, [targetContainer, isReady]);

  // If not ready or no container, render inline
  if (!isReady || !targetContainer || fallbackMode) {
    return <div data-portal-fallback="true">{children}</div>;
  }

  try {
    // Verify container is still in DOM
    if (!document.contains(targetContainer)) {
      console.warn('Portal container not in DOM, falling back to inline rendering');
      setFallbackMode(true);
      return <div data-portal-fallback="true">{children}</div>;
    }

    // Use React's createPortal with our stable container
    return createPortal(children, targetContainer);
  } catch (error) {
    console.error('Portal creation failed, falling back to inline rendering:', error);
    setFallbackMode(true);
    return <div data-portal-fallback="true">{children}</div>;
  }
};

export default PortalProvider;
