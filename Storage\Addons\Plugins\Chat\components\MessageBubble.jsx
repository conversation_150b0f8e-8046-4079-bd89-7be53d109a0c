import React, { useState } from 'react';
import { colors, typography, spacing } from "./design-system";
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import { FontAwesomeIcon, faUser, faRobot, faCopy, faRedo, faBookmark } from './icons/FontAwesome.jsx';
import { Button } from './ui/button';


const MessageBubble = ({ message }) => {
  const [showActions, setShowActions] = useState(false);

  const isUser = message.sender === 'user';
  const isError = message.type === 'error';
  const isSystem = message.type === 'system';

  const formatTimestamp = (timestamp) => {
    const date = new Date(timestamp);
    return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
  };

  const renderContent = () => {
    return (
      <div className={`${typography.small} leading-relaxed whitespace-pre-wrap break-words`}>
        <ReactMarkdown
          remarkPlugins={[remarkGfm]}
          components={{
            code({ node, inline, className, children, ...props }) {
              const match = /language-(\w+)/.exec(className || '');
              return !inline && match ? (
                <pre className="rounded-md p-2 overflow-x-auto bg-gray-800 text-white">
                  <code className={className} {...props}>
                    {children}
                  </code>
                </pre>
              ) : (
                <code className="bg-gray-200 dark:bg-gray-700 rounded px-1 py-0.5" {...props}>
                  {children}
                </code>
              );
            },
            a: ({ node, ...props }) => <a className="text-blue-500 hover:underline" {...props} />,
            p: ({ node, ...props }) => <p className={`${spacing.xs}`} {...props} />,
            li: ({ node, ...props }) => <li className={`${spacing.xs}`} {...props} />,
          }}
        >
          {message.content}
        </ReactMarkdown>
      </div>
    );
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} ${spacing.md} animate-fade-in`}>
      <div
        className={`group max-w-[85%] rounded-2xl p-4 shadow-lg relative transition-all duration-200 ease-in-out transform hover:scale-[1.01] ${
          isUser
            ? `bg-gradient-to-br from-${colors.primary} to-${colors.primaryDark} text-white ml-4`
            : isError
            ? `bg-${colors.error} dark:bg-red-900/20 text-white dark:text-${colors.textDark} border border-${colors.error} mr-4`
            : isSystem
            ? `bg-${colors.warning} dark:bg-yellow-900/20 text-white dark:text-${colors.textDark} border border-${colors.warning} mr-4`
            : `bg-${colors.surface} dark:bg-${colors.surfaceDark} text-${colors.text} dark:text-${colors.textDark} mr-4 border border-${colors.border} dark:border-${colors.borderDark}`
        }`}
        onMouseEnter={() => setShowActions(true)}
        onMouseLeave={() => setShowActions(false)}
      >
        {/* Message bubble tail */}
        <div className={`absolute top-4 w-3 h-3 transform rotate-45 ${
          isUser
            ? `bg-${colors.primary} -right-1`
            : isError
            ? `bg-${colors.error} -left-1 border-l border-b border-${colors.error}`
            : isSystem
            ? `bg-${colors.warning} -left-1 border-l border-b border-${colors.warning}`
            : `bg-${colors.surface} dark:bg-${colors.surfaceDark} -left-1 border-l border-b border-${colors.border}`
        }`} />

        <div className="flex items-start gap-3">
          {/* Avatar */}
          <div className={`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${
            isUser
              ? 'bg-white/20'
              : isError
                ? `bg-${colors.error}`
                : isSystem
                ? `bg-${colors.warning}`
                : `bg-gradient-to-r from-purple-500 to-blue-500`
          }`}>
            <span className="text-sm">
              <FontAwesomeIcon icon={isUser ? faUser : faRobot} className="text-sm" />
            </span>
          </div>

          {/* Content */}
          <div className="flex-1 min-w-0">
            {renderContent()}

            {/* Message metadata and actions */}
            <div className="flex items-center justify-between mt-3">
              <div className={`${typography.tiny} ${
                isUser ? 'text-white/70' :
                isError ? 'text-red-500 dark:text-red-400' :
                isSystem ? 'text-yellow-500 dark:text-yellow-400' :
                'text-gray-500 dark:text-gray-400'
              }`}>
                {message.timestamp && formatTimestamp(message.timestamp)}
                {message.model_used && ` • ${message.model_used}`}
              </div>

              {/* Quick Actions */}
              {showActions && (
                <div className={`flex items-center gap-1 opacity-100 transition-opacity`}>
                  <Button
                    size="sm"
                    variant="ghost"
                    onClick={() => navigator.clipboard.writeText(message.content)}
                    className="h-6 w-6 p-0"
                    title="Copy message"
                  >
                    <FontAwesomeIcon icon={faCopy} className="text-sm" />
                  </Button>

                  {!isUser && !isSystem && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="h-6 w-6 p-0"
                      title="Regenerate response"
                    >
                      <FontAwesomeIcon icon={faRedo} className="text-sm" />
                    </Button>
                  )}

                  <Button
                    size="sm"
                    variant="ghost"
                    className="h-6 w-6 p-0"
                    title="Bookmark message"
                  >
                    <FontAwesomeIcon icon={faBookmark} className="text-sm" />
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MessageBubble;