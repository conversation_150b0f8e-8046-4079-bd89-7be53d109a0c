import { test, expect } from '@playwright/test';

test('check plugin loading', async ({ page }) => {
  // Detect if running under <PERSON>ri (desktop) vs browser
  const isTauri = await page.evaluate(() => !!window.__TAURI__);

  // Listen for console errors
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    errors.push(error.message);
  });

  await page.goto('http://localhost:3000/tools');

  // Avoid waiting for 'networkidle' because Vite dev server keeps WS connections alive
  await page.waitForLoadState('domcontentloaded');

  // Explicit readiness: wait for Tools page heading or a known selector to appear
  await page.waitForSelector('text=Tools', { timeout: 15000 });

  // Example: Check if plugin containers are present
  const pluginContainers = await page.$$('.plugin-container');
  console.log(`Found ${pluginContainers.length} plugin containers`);
  
  // Check that no errors occurred during plugin loading
  const pluginErrors = errors.filter(error => 
    error.includes('plugin') || 
    error.includes('import') ||
    error.includes('module')
  );
  
  if (isTauri) {
    expect(pluginErrors).toEqual([]);
  } else {
    console.log('Non-Tauri environment detected; not asserting on pluginErrors:', pluginErrors);
  }
});

test('check chat plugin specifically', async ({ page }) => {
  const isTauri = await page.evaluate(() => !!window.__TAURI__);

  // Listen for console errors
  const errors = [];
  page.on('console', msg => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });

  // Listen for page errors
  page.on('pageerror', error => {
    errors.push(error.message);
  });

  await page.goto('http://localhost:3000/chat');

  // Avoid waiting for 'networkidle' because dev HMR keeps the network busy
  await page.waitForLoadState('domcontentloaded');

  // Explicit readiness: wait for a chat-specific selector to be visible
  await page.waitForSelector('.chat-container, .message-input, .send-button', { state: 'attached', timeout: 15000 });
  
  // Optional: short stabilization delay for dynamic UI
  await page.waitForTimeout(500);

  // Check for common chat plugin elements
  const chatElements = await page.$$('.chat-container, .message-input, .send-button');
  console.log(`Found ${chatElements.length} chat elements`);
  
  // Check that no errors occurred during chat plugin loading
  const chatErrors = errors.filter(error => 
    error.includes('chat') || 
    error.includes('message') ||
    error.includes('send')
  );
  
  if (isTauri) {
    expect(chatErrors).toEqual([]);
  } else {
    console.log('Non-Tauri environment detected; not asserting on chatErrors:', chatErrors);
  }
});