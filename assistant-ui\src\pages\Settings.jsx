import React, { useState, useEffect } from 'react';
import { invoke } from '@tauri-apps/api/core';
import { listen } from '@tauri-apps/api/event';
import { Link, useLocation } from 'react-router-dom';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import {
  faCog, faPlug, faDatabase, faBoxOpen, faSearch, faUserCog,
  faBell, faPalette, faShieldAlt, faBook, faChevronLeft, faRobot, faPlus, faUser,
  faFolder, faFile, faRefresh, faExclamationTriangle, faDownload, faTrash, faServer,
  faHardDrive, faHome, faChevronRight, faChevronDown
} from '@fortawesome/free-solid-svg-icons';

import { Button } from "../components/ui/button.jsx";
import { Input } from "../components/ui/input.jsx";
import { Card, CardHeader, CardTitle, CardContent, CardDescription, CardFooter } from "../components/ui/card.jsx";
import { Tabs, TabsList, TabsTrigger, TabsContent } from "../components/ui/tabs.jsx";
import { Switch } from "../components/ui/switch.jsx";
import { Label } from "../components/ui/label.jsx";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "../components/ui/accordion.jsx";
import { StandardInput, StandardSelect, StandardCheckbox } from "../components/ui/form-fields.jsx";
import StorageSettings from '../components/StorageSettings.jsx';
import SimpleFolderSelector from '../components/SimpleFolderSelector.jsx';
import ServerProfileCard from '../components/ServerProfileCard.jsx';
import PluginLoader from '../components/PluginLoader.jsx';

import ModelProfileCard from '../components/ModelProfileCard.jsx';
import ModelDownloadStatus from '../components/ModelDownloadStatus.jsx';
import { useSettings } from '../contexts/SettingsContext.jsx';
import StatusBar from '../components/StatusBar.jsx';
import FileManager from '../components/FileManager.jsx';
import ExtensionRegistry from '../components/ExtensionRegistry.jsx';
import ErrorBoundary from '../components/ErrorBoundary.jsx';
import { ChevronDown } from 'lucide-react';
import ThemeSettings from '../components/settings/ThemeSettings';
import NotificationSettings from '../components/settings/NotificationSettings';

const Settings = React.memo(() => {
  // ALL HOOKS MUST BE AT THE TOP - NO CONDITIONAL HOOKS
  const { userSettings, saveUserSetting, updateUserSettings, isLoading: settingsLoading, error: settingsError } = useSettings();
  const location = useLocation();

  const [activeMainTab, setActiveMainTab] = useState('System');
  const [loadingPlugins, setLoadingPlugins] = useState(false);
  const [loadingMigration, setLoadingMigration] = useState(false);
  const [activeSidebarItem, setActiveSidebarItem] = useState('General');
  const [activeAppearanceTab, setActiveAppearanceTab] = useState('theme'); // Default to theme subpage
  const [expandedSidebarItems, setExpandedSidebarItems] = useState([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [plugins, setPlugins] = useState([]);
  const [pluginError, setPluginError] = useState(null);
  const [pluginsDirectoryContents, setPluginsDirectoryContents] = useState([]);

  // ALL REMAINING STATE HOOKS MOVED TO TOP
  const [serverStatus, setServerStatus] = useState('stopped');
  const [serverModels, setServerModels] = useState([]);
  const [loadingServerModels, setLoadingServerModels] = useState(false);
  const [modelToPull, setModelToPull] = useState('');
  const [pullingModel, setPullingModel] = useState(false);
  const [pullModelMessage, setPullModelMessage] = useState('');
  const [pullModelError, setPullModelError] = useState('');
  const [downloadStatus, setDownloadStatus] = useState(null);
  const [serverDirectoryContents, setServerDirectoryContents] = useState([]);
  const [modelDirectoryContents, setModelDirectoryContents] = useState([]);
  const [serverProfiles, setServerProfiles] = useState([]);
  const [modelProfiles, setModelProfiles] = useState([]);
  const [activeServerProfile, setActiveServerProfile] = useState('');
  const [activeModelProfile, setActiveModelProfile] = useState('');
  const [loadingProfiles, setLoadingProfiles] = useState(false);

  // Log when userSettings changes
  useEffect(() => {
    console.log('Settings page: userSettings updated:', userSettings);
  }, [userSettings]);

  // Handle URL parameters to set active section
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    const section = urlParams.get('section');

    if (section) {
      // Set the active sidebar item based on the section parameter
      setActiveSidebarItem(section);

      // If it's a sub-item, also expand the parent item
      const parentItem = section.split('-')[0];
      if (parentItem && parentItem !== section) {
        setExpandedSidebarItems(prev => {
          if (!prev.includes(parentItem)) {
            return [...prev, parentItem];
          }
          return prev;
        });
      }

      // Set the main tab based on the section
      // Check if the section belongs to Addons tab
      const isAddonsSection = ['Plugins', 'MCP', 'APIs'].includes(section);
      if (isAddonsSection) {
        setActiveMainTab('Addons');
      } else {
        // Most sections are under 'System' tab
        setActiveMainTab('System');
      }
    }
  }, [location.search]);

  // RENDER LOADING/ERROR STATES AFTER ALL HOOKS
  if (settingsLoading || !userSettings) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-foreground mb-2">Settings</h2>
          <p className="text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }
  if (settingsError) {
    return (
      <div className="flex items-center justify-center w-full h-full bg-background text-foreground">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-destructive mb-2">Settings Error</h2>
          <p className="text-muted-foreground mb-4">Failed to load settings: {settingsError}</p>
          <p className="text-sm text-muted-foreground">Using default settings...</p>
        </div>
      </div>
    );
  }

  // Remove configBaseUrl - we'll use Tauri commands instead
  // ALL STATE HOOKS MOVED TO TOP OF COMPONENT

  // Logic files are now handled by FileManager component

  useEffect(() => {
    const unlistenStart = listen('model_pull_start', (event) => {
      setDownloadStatus({
        modelName: event.payload,
        status: 'starting',
        total: 0,
        completed: 0,
      });
      setPullingModel(true);
      setPullModelMessage('');
      setPullModelError('');
    });

    const unlistenProgress = listen('model_pull_progress', (event) => {
      console.log('Progress event received:', event.payload);
      const payload = event.payload;

      // Handle Ollama's progress format
      if (payload.status && payload.total && payload.completed !== undefined) {
        setDownloadStatus({
          modelName: modelToPull || 'Unknown Model',
          status: payload.status,
          total: payload.total,
          completed: payload.completed
        });
      }
    });

    const unlistenSuccess = listen('model_pull_success', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelMessage(`Successfully pulled ${event.payload}`);
      // Refresh both Ollama models and model profiles
      fetchOllamaModels();
      loadModelProfiles();
      setModelToPull(''); // Clear the input
    });

    const unlistenError = listen('model_pull_error', (event) => {
      setDownloadStatus(null);
      setPullingModel(false);
      setPullModelError(`Failed to pull model: ${event.payload}`);
    });

    return () => {
      unlistenStart.then(f => f());
      unlistenProgress.then(f => f());
      unlistenSuccess.then(f => f());
      unlistenError.then(f => f());
    };
  }, []);

  // Fetch Ollama status on mount (server should already be started by App.jsx)
  useEffect(() => {
    console.log('Settings page: Fetching server status...');
    fetchServerStatus();
  }, []);

  const fetchServerStatus = async () => {
    console.log('=== FRONTEND: Fetching server status ===');
    try {
      const status = await invoke('get_server_status');
      console.log('Raw status response:', status);
      setServerStatus(status.status);
      console.log('Server status set to:', status.status);
    } catch (error) {
      console.error('Error fetching server status:', error);
      setServerStatus('error');
    }
  };
  

  const handleStartServer = async () => {
    console.log('=== FRONTEND: Start button clicked ===');
    try {
      setServerStatus('starting');
      console.log('Starting server...');
      console.log('About to invoke start_server...');
      const result = await invoke('start_server_command');
      console.log('Server start command completed, result:', result);
      // Wait a moment then check status
      setTimeout(() => {
        fetchServerStatus();
      }, 3000); // Increased wait time
    } catch (error) {
      console.error('Error starting server:', error);
      setServerStatus('error');
      // Show the error to the user
      alert(`Failed to start server:\n\n${error}`);
    }
  };



  const handleStopServer = async () => {
    try {
      console.log('Stopping server...');
      await invoke('stop_server_command');
      console.log('Server stop command completed');
      setServerStatus('stopped');
      setTimeout(() => {
        fetchServerStatus();
      }, 1000);
    } catch (error) {
      console.error('Error stopping server:', error);
      alert(`Failed to stop server:\n\n${error}`);
      fetchServerStatus();
    }
  };

  const handleCheckInstallation = async () => {
    try {
      const result = await invoke('check_installation_command');
      alert(`Installation Check:\n\n${result}`);
    } catch (error) {
      alert(`Installation Check Failed:\n\n${error}`);
    }
  };

  const fetchServerModels = async () => {
    setLoadingServerModels(true);
    try {
      const models = await invoke('get_models_command');
      setServerModels(models);
    } catch (error) {
      console.error('Error fetching server models:', error);
      setServerModels([]);
    } finally {
      setLoadingServerModels(false);
    }
  };

  const handlePullModel = async () => {
    if (!modelToPull) return;
    console.log('Starting model pull for:', modelToPull);
    setPullingModel(true);
    setPullModelMessage('');
    setPullModelError('');
    setDownloadStatus({
      modelName: modelToPull,
      status: 'initializing',
      total: 0,
      completed: 0
    });

    try {
      await invoke('pull_model_command', { model_name: modelToPull });
      console.log('Model pull command sent successfully');
    } catch (error) {
      console.error('Error pulling model:', error);
      setPullModelError(`Failed to pull model: ${error}`);
      setDownloadStatus(null);
      setPullingModel(false);
    }
  };



  const mainTabsConfig = [
    { name: 'System', icon: faUserCog },
    { name: 'Addons', icon: faPlug },
    { name: 'Vault', icon: faDatabase }
  ];

  const sidebarItemsConfig = {
    System: [
      { 
        name: 'General', 
        icon: faUserCog,
        subItems: [
          { name: 'The Collective', path: 'collective' },
          { name: 'Startup & Initialization', path: 'startup' }
        ]
      },
      { 
        name: 'Server/Model', 
        icon: faRobot,
        subItems: [
          { name: 'Server', path: 'server' },
          { name: 'Model', path: 'model' }
        ]
      },
      { 
        name: 'Logic', 
        icon: faCog,
        subItems: [
          { name: 'Hub', path: 'hub' },
          { name: 'System Prompts', path: 'system-prompts' },
          { name: 'Modals', path: 'modals' },
          { name: 'Agents', path: 'agents' }
        ]
      },
      { name: 'Extensions', icon: faPlug },
      { 
        name: 'Network', 
        icon: faServer,
        subItems: [
          { name: 'Project', path: 'project' }
        ]
      },
      { 
        name: 'Devices', 
        icon: faHardDrive,
        subItems: [
          { name: 'Bluetooth & Devices', path: 'bluetooth' }
        ]
      },
      { name: 'Notification Center', icon: faBell },
      { name: 'Appearance', icon: faPalette },
      { name: 'Data Controls', icon: faShieldAlt },
      { name: 'Storage', icon: faDatabase },
      { name: 'System Log', icon: faBook },
    ],
    Addons: [
      { name: 'Plugins', icon: faPlug },
      { name: 'MCP', icon: faCog }, // MCP (Model Context Protocol) remains under Addons
      { name: 'APIs', icon: faBell }
    ],
    Vault: [
      { name: 'File Management', icon: faFolder },
      { name: 'Backup & Restore', icon: faDownload },
      { name: 'Encryption', icon: faShieldAlt }
    ]
    // Removed Storage main tab configuration
  };

  // Initialize all data on component mount (no more lazy loading)
  useEffect(() => {
    const initializeAllData = async () => {
      console.log('🚀 Settings: Initializing all data on mount');

      // Load all data regardless of active tab
      try {
        await Promise.all([
          fetchServerStatus(),
          fetchServerModels(),
          loadServerProfiles(),
          // loadModelProfiles(), // Removed - using direct server queries
          fetchPlugins()
        ]);
        console.log('✅ Settings: All data initialized');
      } catch (error) {
        console.error('❌ Settings: Failed to initialize data:', error);
      }
    };

    initializeAllData();
  }, []); // Only run once on mount

  // Load Logic files when the component mounts and whenever the Logic path is available
  // This is now handled by FileManager component automatically
  useEffect(() => {
    // The FileManager will handle file loading automatically via useEffect
  }, []);

  const loadServerProfiles = async () => {
    console.log('=== FRONTEND: Loading server profiles ===');
    try {
      setLoadingProfiles(true);
      const profiles = await invoke('get_server_profiles');
      console.log('Raw server profiles response:', profiles);
      setServerProfiles(profiles);
      console.log('Server profiles set to:', profiles);
      
      // Load active server profile
      try {
        const activeProfile = await invoke('get_active_server_profile');
        setActiveServerProfile(activeProfile?.name || '');
        console.log('Active server profile:', activeProfile?.name || 'None');
      } catch (error) {
        console.warn('No active server profile set:', error);
        setActiveServerProfile('');
      }
    } catch (error) {
      console.error('Error loading server profiles:', error);
    } finally {
      setLoadingProfiles(false);
    }
  };

  const loadModelProfiles = async () => {
    try {
      const profiles = await invoke('get_model_profiles');
      setModelProfiles(profiles);
      console.log('Loaded model profiles:', profiles);
      
      // Load active model profile
      try {
        const activeProfile = await invoke('get_active_model_profile');
        setActiveModelProfile(activeProfile?.name || '');
        console.log('Active model profile:', activeProfile?.name || 'None');
      } catch (error) {
        console.warn('No active model profile set:', error);
        setActiveModelProfile('');
      }
    } catch (error) {
      console.error('Error loading model profiles:', error);
    }
  };

  const handleToggleServer = async (serverName, enabled) => {
    try {
      await invoke('toggle_server_profile', { serverName, enabled });
      loadServerProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling server:', error);
    }
  };

  const handleToggleModel = async (modelName, enabled) => {
    try {
      await invoke('toggle_model_profile', { modelName, enabled });
      loadModelProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error toggling model:', error);
    }
  };

  const handleSetActiveServer = async (serverName) => {
    try {
      await invoke('set_active_server_profile', { serverName });
      setActiveServerProfile(serverName);
      console.log('Set active server profile:', serverName);
    } catch (error) {
      console.error('Error setting active server:', error);
      alert(`Failed to set active server: ${error}`);
    }
  };

  const handleSetActiveModel = async (modelName) => {
    try {
      await invoke('set_active_model_profile', { modelName });
      setActiveModelProfile(modelName);
      console.log('Set active model profile:', modelName);
    } catch (error) {
      console.error('Error setting active model:', error);
      alert(`Failed to set active model: ${error}`);
    }
  };

  const handleRefreshServer = async (serverName) => {
    try {
      await invoke('refresh_server_profile_command', { serverName });
      loadServerProfiles(); // Refresh the list
    } catch (error) {
      console.error('Error refreshing server profile:', error);
      alert(`Failed to refresh server profile: ${error}`);
    }
  };

  const handleDeleteServer = async (serverName) => {
    if (window.confirm(`Are you sure you want to delete the server profile "${serverName}"?`)) {
      try {
        await invoke('delete_server_profile', { serverName });
        loadServerProfiles(); // Refresh the list
      } catch (error) {
        console.error('Error deleting server profile:', error);
        alert(`Failed to delete server profile: ${error}`);
      }
    }
  };

  const handleDeleteModel = async (modelName) => {
    if (window.confirm(`Are you sure you want to delete the model profile "${modelName}"?`)) {
      try {
        await invoke('delete_model_profile', { model_name: modelName });
        loadModelProfiles(); // Refresh the list
      } catch (error) {
        console.error('Error deleting model profile:', error);
        alert(`Failed to delete model profile: ${error}`);
      }
    }
  };



  // Test function to check if tauri invoke is working
  const testTauriConnection = async () => {
    console.log('=== TESTING TAURI CONNECTION ===');
    try {
      const result = await invoke('greet', { name: 'Test' });
      console.log('Greet result:', result);
      alert(`Tauri connection works! Result: ${result}`);
    } catch (error) {
      console.error('Tauri connection failed:', error);
      alert(`Tauri connection failed: ${error}`);
    }
  };

  const handleServerPathSelected = async (path) => {
    console.log('Server path selected:', path);
    await listAndDisplayDirectoryContents(path, setServerDirectoryContents);
    // Load server profiles after path is selected
    loadServerProfiles();
  };

  const handleModelPathSelected = async (path) => {
    console.log('Model path selected:', path);
    await listAndDisplayDirectoryContents(path, setModelDirectoryContents);
    // Load model profiles after path is selected
    loadModelProfiles();
  };

  const handleStoragePathSelected = (path) => {
    console.log('Storage path selected:', path);
  };

  const handleVaultPathSelected = (path) => {
    console.log('Vault path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleSystemPromptsPathSelected = (path) => {
    console.log('System prompts path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleLogicHubPathSelected = (path) => {
    console.log('Logic hub path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleLogicSystemPromptsPathSelected = (path) => {
    console.log('Logic system prompts path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleLogicModalsPathSelected = (path) => {
    console.log('Logic modals path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleLogicAgentsPathSelected = (path) => {
    console.log('Logic agents path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  const handleExtensionsPathSelected = (path) => {
    console.log('Extensions path selected:', path);
    // The FileManager will handle file loading automatically via useEffect
  };

  // Logic file loading is now handled by FileManager component

  const handlePluginsPathSelected = async (path) => {
    console.log('Plugins path selected:', path);
    // Trigger a refresh of the plugin list here
    await fetchPlugins();
    // Also list the contents of the selected directory
    await listAndDisplayDirectoryContents(path);
  };

  const listAndDisplayDirectoryContents = async (path) => {
    try {
      // Use browse_directory instead of the non-existent list_directory_contents
      const result = await invoke('browse_directory', { path });
      // Convert browse_directory format to the expected format
      const contents = [
        ...(result.directories || []).map(dir => ({ 
          name: dir.name || dir, 
          path: dir.path || `${path}\\${dir.name || dir}`, 
          is_dir: true 
        })),
        ...(result.files || []).map(file => ({ 
          name: file.name || file, 
          path: file.path || `${path}\\${file.name || file}`, 
          is_dir: false 
        }))
      ];
      setPluginsDirectoryContents(contents);
    } catch (error) {
      console.error('Error listing directory contents:', error);
      setPluginsDirectoryContents([]);
    }
  };

  const fetchPlugins = async () => {
    try {
      setLoadingPlugins(true);
      const pluginsList = await invoke('get_plugins');
      setPlugins(pluginsList);
      setPluginError(null);
    } catch (err) {
      console.error('Error fetching plugins:', err);
      setPluginError('Failed to load plugins. Make sure plugins path is configured and contains valid plugins.');
    } finally {
      setLoadingPlugins(false);
    }
  };

  const togglePlugin = async (pluginName, currentStatus) => {
    try {
      const newStatus = !currentStatus;
      await invoke('toggle_plugin', { plugin_name: pluginName, enabled: newStatus });
      // Update local state to reflect the change
      setPlugins(plugins.map(p => p.name === pluginName ? { ...p, enabled: newStatus } : p));
    } catch (err) {
      console.error(`Error toggling plugin ${pluginName}:`, err);
      setPluginError(`Failed to update plugin status for ${pluginName}.`);
    }
  };

  const handleMainTabChange = (value) => {
    setActiveMainTab(value);
    if (sidebarItemsConfig[value] && sidebarItemsConfig[value].length > 0) {
      setActiveSidebarItem(sidebarItemsConfig[value][0].name);
    } else {
      setActiveSidebarItem('');
    }
  };

  const currentSidebarItems = sidebarItemsConfig[activeMainTab] || [];
  const filteredSidebarItems = currentSidebarItems.filter(item =>
    item.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Server Status and Model Pull Section (Top Priority)
  const renderServerStatusSection = () => (
    <div className="space-y-6 mb-8">
      {/* Server Status & Control */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <div className={`w-4 h-4 rounded-full ${
              serverStatus === 'running' ? 'bg-green-500 animate-pulse' :
              serverStatus === 'starting' ? 'bg-yellow-500 animate-pulse' :
              'bg-red-500'
            }`}></div>
            <span>AI Server Status</span>
          </CardTitle>
          <CardDescription>
            Current Status: {
              serverStatus === 'running' ? 'Running and Ready' :
              serverStatus === 'starting' ? 'Starting Up...' :
              serverStatus === 'stopped' ? 'Stopped' :
              serverStatus === 'no_server_profile' ? 'No Server Profile Configured' :
              'Error or Unknown'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex space-x-2">
            <Button
              onClick={handleStartServer}
              size="sm"
              variant="default"
              disabled={serverStatus === 'running' || serverStatus === 'starting' || serverStatus === 'no_server_profile'}
              className="flex-1"
            >
              {serverStatus === 'starting' ? 'Starting...' : 'Start Server'}
            </Button>
            <Button
              onClick={handleStopServer}
              size="sm"
              variant="destructive"
              disabled={serverStatus === 'stopped' || serverStatus === 'no_server_profile' || serverStatus === 'error'}
              className="flex-1"
            >
              Stop Server
            </Button>
            <Button onClick={fetchServerStatus} size="sm" variant="outline">
              Refresh Status
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Model Pull Section */}
      <Card>
        <CardHeader>
          <CardTitle>Download AI Models</CardTitle>
          <CardDescription>Pull and download AI models for use with your server</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Model Pull Input */}
          <div className="flex space-x-2">
            <StandardInput
              id="model-pull-input"
              value={modelToPull}
              onChange={(e) => setModelToPull(e.target.value)}
              placeholder="e.g. qwen2:0.5b, llama3, codellama"
              disabled={pullingModel || serverStatus !== 'running'}
              className={serverStatus !== 'running' ? 'opacity-50' : ''}
              containerClassName="m-0 flex-1"
            />
            <Button
              onClick={handlePullModel}
              disabled={pullingModel || !modelToPull || serverStatus !== 'running'}
              className="min-w-[100px]"
            >
              {pullingModel ? 'Pulling...' : 'Pull Model'}
            </Button>
          </div>

          {/* Status Messages */}
          {serverStatus !== 'running' && (
            <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
              <p className="text-sm text-yellow-800 dark:text-yellow-200">
                ⚠️ Server must be running to pull models.
                {serverStatus === 'stopped' && ' Click "Start Server" above to begin.'}
                {serverStatus === 'no_server_profile' && ' Please configure a server profile first.'}
              </p>
            </div>
          )}

          <div className="text-xs text-gray-500">
            Popular models: qwen2:0.5b, llama3, codellama, mistral, phi3
          </div>

          <ModelDownloadStatus downloadStatus={downloadStatus} />
          {pullModelMessage && (
            <div className="p-3 rounded-lg bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800">
              <p className="text-sm text-green-800 dark:text-green-200">✅ {pullModelMessage}</p>
            </div>
          )}
          {pullModelError && (
            <div className="p-3 rounded-lg bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800">
              <p className="text-sm text-red-800 dark:text-red-200">❌ {pullModelError}</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );



  const renderContent = () => {
    console.log('renderContent called with activeSidebarItem:', activeSidebarItem);
    
    if (activeSidebarItem === 'Appearance') {
      return (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Appearance</h2>
              <p className="text-muted-foreground">Customize the look and feel of the application</p>
            </div>
          </div>
          
          <Tabs 
            value={activeAppearanceTab} 
            onValueChange={setActiveAppearanceTab}
            className="space-y-4"
            defaultValue="theme"
          >
            <TabsList>
              <TabsTrigger value="theme">Theme</TabsTrigger>
              <TabsTrigger value="layout">Layout</TabsTrigger>
              <TabsTrigger value="advanced">Advanced</TabsTrigger>
            </TabsList>
            
            <Card>
              <CardContent className="pt-6">
                {activeAppearanceTab === 'theme' && <ThemeSettings />}
                
                {activeAppearanceTab === 'layout' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Layout Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Customize the application layout and spacing
                    </p>
                    {/* Layout settings will go here */}
                  </div>
                )}
                
                {activeAppearanceTab === 'advanced' && (
                  <div>
                    <h3 className="text-lg font-medium mb-4">Advanced Settings</h3>
                    <p className="text-sm text-muted-foreground">
                      Advanced appearance customization options
                    </p>
                    {/* Advanced settings will go here */}
                  </div>
                )}
              </CardContent>
            </Card>
          </Tabs>
        </div>
      );
    }

    if (activeSidebarItem === 'Notification Center') {
      return (
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold">Notification Center</h2>
              <p className="text-muted-foreground">Configure notifications and save system settings</p>
            </div>
          </div>

          <Card>
            <CardContent className="pt-6">
              <NotificationSettings />
            </CardContent>
          </Card>
        </div>
      );
    }

    if (!activeSidebarItem) {
      return (
        <div className="flex flex-col items-center justify-center h-full text-center text-muted-foreground p-8">
          <FontAwesomeIcon icon={faCog} className="h-16 w-16 mb-6 text-primary" />
          <h2 className="text-2xl font-semibold mb-2">Select a setting</h2>
          <p>Choose an item from the sidebar to view or modify its settings.</p>
        </div>
      );
    }

    switch (`${activeMainTab}-${activeSidebarItem}`) {
      case 'System-General-collective':
        return (
          <Card className="m-6 overflow-hidden">
            <CardHeader className="pb-4">
              <CardTitle>About The Collective</CardTitle>
              <CardDescription>Basic application information and about section.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-2">
              {/* About Section */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">About The Collective</h4>
                <p className="text-sm text-muted-foreground">Version 0.1.0 - A PC-based AI assistant with modular functionality.</p>
              </div>

              {/* Version Information */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">Version Information</h4>
                <div className="mt-2 space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Version:</span>
                    <span>0.1.0</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Build Date:</span>
                    <span>August 30, 2025</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Platform:</span>
                    <span>Windows</span>
                  </div>
                </div>
              </div>
              
              {/* License Information */}
              <div className="p-4 border rounded-lg">
                <h4 className="font-medium">License</h4>
                <p className="text-sm text-muted-foreground mt-2">© 2025 The Collective. All rights reserved.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Server/Model-server':
        return (
          <Card className="m-6 overflow-hidden">
            <CardHeader className="pb-4">
              <CardTitle>Server Management</CardTitle>
              <CardDescription>Configure and control your AI server.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-2">
              {/* Server Status & Control */}
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center space-x-2">
                    <div className={`w-4 h-4 rounded-full ${
                      serverStatus === 'running' ? 'bg-green-500 animate-pulse' :
                      serverStatus === 'starting' ? 'bg-yellow-500 animate-pulse' :
                      'bg-red-500'
                    }`}></div>
                    <span>AI Server Status</span>
                  </CardTitle>
                  <CardDescription>
                    Current Status: {
                      serverStatus === 'running' ? 'Running and Ready' :
                      serverStatus === 'starting' ? 'Starting Up...' :
                      serverStatus === 'stopped' ? 'Stopped' :
                      serverStatus === 'no_server_profile' ? 'No Server Profile Configured' :
                      'Error or Unknown'
                    }
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="flex space-x-2">
                    <Button
                      onClick={handleStartServer}
                      size="sm"
                      variant="default"
                      disabled={serverStatus === 'running' || serverStatus === 'starting' || serverStatus === 'no_server_profile'}
                      className="flex-1"
                    >
                      {serverStatus === 'starting' ? 'Starting...' : 'Start Server'}
                    </Button>
                    <Button
                      onClick={handleStopServer}
                      size="sm"
                      variant="destructive"
                      disabled={serverStatus === 'stopped' || serverStatus === 'no_server_profile' || serverStatus === 'error'}
                      className="flex-1"
                    >
                      Stop Server
                    </Button>
                    <Button onClick={fetchServerStatus} size="sm" variant="outline">
                      Refresh Status
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Server Configuration Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Server Management</CardTitle>
                  <CardDescription>Manage your AI server instances.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <SimpleFolderSelector
                    label="Server Path"
                    description="Select the directory where the server executable is located."
                    fetchCommand="get_servers_path"
                    saveCommand="set_servers_path"
                    onFolderSelected={handleServerPathSelected}
                  />
                  {serverDirectoryContents.length > 0 && (
                    <div className="mt-4">
                      <h4 className="text-md font-semibold mb-2">Server Directory Contents:</h4>
                      <ul className="list-disc pl-5">
                        {serverDirectoryContents.map((entry, index) => (
                          <li key={index} className="text-sm">
                            {entry.name} {entry.is_dir ? '/' : ''}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}

                  {/* Server Profile Cards */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-md font-semibold">Available Servers:</h4>
                      <Button onClick={loadServerProfiles} size="sm" variant="outline">
                        <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
                        Refresh
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mb-2">
                      New servers are detected by adding folders to the server path.
                    </p>
                    {loadingProfiles ? (
                      <div className="text-center py-2">Loading...</div>
                    ) : serverProfiles.length > 0 ? (
                      <div className="space-y-2">
                        {serverProfiles.map((profile, index) => (
                          <ServerProfileCard
                            key={index}
                            profile={profile}
                            onToggleEnabled={(enabled) => handleToggleServer(profile.name, enabled)}
                            onDelete={() => handleDeleteServer(profile.name)}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-2 text-gray-500 text-sm">
                        No server folders found
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        );

      case 'System-Server/Model-model':
        return (
          <Card className="m-6 overflow-hidden">
            <CardHeader className="pb-4">
              <CardTitle>Model Management</CardTitle>
              <CardDescription>Download and manage AI models for your server.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6 pt-2">
              {/* Model Pull Section */}
              <Card>
                <CardHeader>
                  <CardTitle>Download AI Models</CardTitle>
                  <CardDescription>Pull and download AI models for use with your server</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Model Pull Input */}
                  <div className="flex space-x-2">
                    <StandardInput
                      id="model-pull-input"
                      value={modelToPull}
                      onChange={(e) => setModelToPull(e.target.value)}
                      placeholder="e.g. qwen2:0.5b, llama3, codellama"
                      disabled={pullingModel || serverStatus !== 'running'}
                      className={serverStatus !== 'running' ? 'opacity-50' : ''}
                      containerClassName="m-0 flex-1"
                    />
                    <Button
                      onClick={handlePullModel}
                      disabled={pullingModel || !modelToPull || serverStatus !== 'running'}
                      className="min-w-[100px]"
                    >
                      {pullingModel ? 'Pulling...' : 'Pull Model'}
                    </Button>
                  </div>

                  {/* Status Messages */}
                  {serverStatus !== 'running' && (
                    <div className="p-3 rounded-lg bg-yellow-50 border border-yellow-200 dark:bg-yellow-900/20 dark:border-yellow-800">
                      <p className="text-sm text-yellow-800 dark:text-yellow-200">
                        ⚠️ Server must be running to pull models.
                        {serverStatus === 'stopped' && ' Click "Start Server" above to begin.'}
                        {serverStatus === 'no_server_profile' && ' Please configure a server profile first.'}
                      </p>
                    </div>
                  )}

                  <div className="text-xs text-gray-500">
                    Popular models: qwen2:0.5b, llama3, codellama, mistral, phi3
                  </div>

                  <ModelDownloadStatus downloadStatus={downloadStatus} />
                  {pullModelMessage && (
                    <div className="p-3 rounded-lg bg-green-50 border border-green-200 dark:bg-green-900/20 dark:border-green-800">
                      <p className="text-sm text-green-800 dark:text-green-200">✅ {pullModelMessage}</p>
                    </div>
                  )}
                  {pullModelError && (
                    <div className="p-3 rounded-lg bg-red-50 border border-red-200 dark:bg-red-900/20 dark:border-red-800">
                      <p className="text-sm text-red-800 dark:text-red-200">❌ {pullModelError}</p>
                    </div>
                  )}
                </CardContent>
              </Card>

              {/* Model Management Card */}
              <Card>
                <CardHeader>
                  <CardTitle>Model Management</CardTitle>
                  <CardDescription>Manage your AI model storage and downloaded models.</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <SimpleFolderSelector
                    label="Model Storage Path"
                    description="Select the directory where AI models are stored."
                    fetchCommand="get_models_path"
                    saveCommand="set_models_path"
                    onFolderSelected={handleModelPathSelected}
                  />
                  <div className="border rounded-lg p-4">
                    <h4 className="text-md font-semibold mb-2">Model Files Explorer</h4>
                    <p className="text-xs text-gray-500 mb-3">Browse and manage your AI model files</p>
                    <PluginLoader 
                      pluginName="File Explorer" 
                      basePath={userSettings?.models_path || "E:\\TheCollective\\Storage\\System\\Models"}
                    />
                  </div>

                  {/* Model Profile Cards */}
                  <div>
                    <div className="flex justify-between items-center mb-2">
                      <h4 className="text-md font-semibold">Available Models:</h4>
                      <Button onClick={loadModelProfiles} size="sm" variant="outline">
                        <FontAwesomeIcon icon={faRefresh} className="mr-2 h-4 w-4" />
                        Refresh
                      </Button>
                    </div>
                    <p className="text-xs text-gray-500 mb-2">
                      New models are detected by adding folders to the model storage path.
                    </p>
                    {modelProfiles.length > 0 ? (
                      <div className="space-y-2">
                        {modelProfiles.map((profile, index) => (
                          <ModelProfileCard
                            key={index}
                            profile={profile}
                            onToggleEnabled={(enabled) => handleToggleModel(profile.name, enabled)}
                            onDelete={() => handleDeleteModel(profile.name)}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-2 text-gray-500 text-sm">
                        No model folders found
                      </div>
                    )}
                  </div>

                </CardContent>
              </Card>
            </CardContent>
          </Card>
        );
      case 'System-Logic-hub':
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>Logic Hub Configuration</CardTitle>
                <CardDescription>Configure the directory where AI logic hub files are stored.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Logic Hub Directory"
                  description="Select the directory where logic hub files are stored."
                  fetchCommand="get_logic_hub_path"
                  saveCommand="set_logic_hub_path"
                  onFolderSelected={handleLogicHubPathSelected}
                />
              </CardContent>
            </Card>

            {/* Logic Hub FileManager */}
            <ErrorBoundary
              title="Logic Hub Error"
              message="An error occurred in the Logic Hub component. Other parts of the page should still work."
              showDetails={true}
            >
              <FileManager 
                basePath={userSettings?.logic_hub_path}
                sections={[
                  {
                    key: 'logicHub',
                    name: 'Logic Hub',
                    icon: faCog,
                    color: 'gray',
                    description: 'Logic hub files',
                    folderNames: ['Hub'],
                    fileExtensions: ['.md', '.txt'],
                    cascadeLevel: 0,
                    hasMetadata: false
                  }
                ]}
                onPathSelected={handleLogicHubPathSelected}
                title="Logic Hub"
                description="Manage logic hub files"
                enableCascadeView={false}
              />
            </ErrorBoundary>
          </div>
        );
      case 'System-Logic-system-prompts':
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>System Prompts Configuration</CardTitle>
                <CardDescription>Configure the directory where AI system prompts are stored.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="System Prompts Directory"
                  description="Select the directory where system prompts are stored. These files form the foundation of AI behavior."
                  fetchCommand="get_logic_system_prompts_path"
                  saveCommand="set_logic_system_prompts_path"
                  onFolderSelected={handleLogicSystemPromptsPathSelected}
                />
              </CardContent>
            </Card>

            {/* System Prompts FileManager */}
            <ErrorBoundary
              title="System Prompts Error"
              message="An error occurred in the System Prompts component. Other parts of the page should still work."
              showDetails={true}
            >
              <FileManager 
                basePath={userSettings?.logic_system_prompts_path}
                sections={[
                  {
                    key: 'systemPrompts',
                    name: 'System Prompts',
                    icon: faFile,
                    color: 'blue',
                    description: 'Text-based prompts that define core AI behavior (Level 1 - Foundation)',
                    addLabel: 'System Prompt',
                    addDescription: 'Create new system prompt (.txt)',
                    folderNames: ['SystemPrompts'],
                    fileExtensions: ['.txt', '.md'],
                    cascadeLevel: 1,
                    hasMetadata: true
                  }
                ]}
                onPathSelected={handleLogicSystemPromptsPathSelected}
                title="System Prompts"
                description="Manage AI system prompts that define core behavior"
                enableCascadeView={true}
              />
            </ErrorBoundary>
          </div>
        );
      case 'System-Logic-modals':
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>Modals Configuration</CardTitle>
                <CardDescription>Configure the directory where AI modals are stored.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Modals Directory"
                  description="Select the directory where modals are stored. These files define specialized nano-algorithms."
                  fetchCommand="get_logic_modals_path"
                  saveCommand="set_logic_modals_path"
                  onFolderSelected={handleLogicModalsPathSelected}
                />
              </CardContent>
            </Card>

            {/* Modals FileManager */}
            <ErrorBoundary
              title="Modals Error"
              message="An error occurred in the Modals component. Other parts of the page should still work."
              showDetails={true}
            >
              <FileManager 
                basePath={userSettings?.logic_modals_path}
                sections={[
                  {
                    key: "modals",
                    name: "Modals",
                    icon: faBoxOpen,
                    color: "green",
                    description: "Modal configurations",
                    folderNames: ["Modals"],
                    fileExtensions: [".json"],
                    cascadeLevel: 2,
                    hasMetadata: true
                  }
                ]}
                onPathSelected={handleLogicModalsPathSelected}
                title="Modals"
                description="Manage AI modals that define specialized nano-algorithms"
                enableCascadeView={true}
              />
            </ErrorBoundary>
          </div>
        );
      case 'System-Logic-agents':
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>Agents Configuration</CardTitle>
                <CardDescription>Configure the directory where AI agents are stored.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Agents Directory"
                  description="Select the directory where agents are stored. These files define task-specialized AI profiles."
                  fetchCommand="get_logic_agents_path"
                  saveCommand="set_logic_agents_path"
                  onFolderSelected={handleLogicAgentsPathSelected}
                />
              </CardContent>
            </Card>

            {/* Agents FileManager */}
            <ErrorBoundary
              title="Agents Error"
              message="An error occurred in the Agents component. Other parts of the page should still work."
              showDetails={true}
            >
              <FileManager 
                basePath={userSettings?.logic_agents_path}
                sections={[
                  {
                    key: "agents",
                    name: "Agents",
                    icon: faRobot,
                    color: "purple",
                    description: "AI agents",
                    folderNames: ["Agents"],
                    fileExtensions: [".json"],
                    cascadeLevel: 3,
                    hasMetadata: true
                  }
                ]}
                onPathSelected={handleLogicAgentsPathSelected}
                title="Agents"
                description="Manage AI agents with task specializations"
                enableCascadeView={true}
              />
            </ErrorBoundary>
          </div>
        );
      case 'System-Storage':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Storage Settings</CardTitle>
              <CardDescription>Manage the directory to be indexed for searching.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="Indexed Directory"
                description="Select the directory that the assistant should scan and index for files. Should be ./Storage/ for portable operation."
                fetchCommand="get_indexed_directory"
                saveCommand="set_indexed_directory"
                onFolderSelected={handleStoragePathSelected}
              />
            </CardContent>
          </Card>
        );
      case 'System-System Log':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>System Log Settings</CardTitle>
              <CardDescription>Configure system log storage and behavior.</CardDescription>
            </CardHeader>
            <CardContent>
              <SimpleFolderSelector
                label="System Log Path"
                description="Select the directory where system logs will be stored."
                fetchCommand="get_system_log_path"
                saveCommand="set_system_log_path"
              />
            </CardContent>
          </Card>
        );
      case 'Vault-File Management': // Added Vault section content
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>File Management</CardTitle>
                <CardDescription>Manage your file organization and storage settings.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Vault Directory"
                  description="Select the directory where vault files are stored."
                  fetchCommand="get_indexed_directory"
                  saveCommand="set_indexed_directory"
                  defaultPath="E:\\TheCollective\\Storage\\Vault"
                  onFolderSelected={handleVaultPathSelected}
                />
              </CardContent>
            </Card>

            {/* File Explorer Component */}
            <ErrorBoundary
              title="Vault Error"
              message="An error occurred in the Vault component. Other parts of the page should still work."
              showDetails={true}
            >
              <PluginLoader
                pluginName="File Explorer"
                basePath="E:\\TheCollective\\Storage"
              />
            </ErrorBoundary>
          </div>
        );
      case 'Vault-Backup & Restore': // Added Vault section content
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Backup & Restore</CardTitle>
              <CardDescription>Backup and restore your data and settings.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Backup Settings</h4>
                  <p className="text-sm text-muted-foreground">Configure backup schedules and restore options.</p>
                </div>
                {/* Placeholder for actual backup content */}
                <div className="mt-4 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <h5 className="font-medium text-blue-800 dark:text-blue-200">Backup Options</h5>
                  <p className="text-sm text-blue-600 dark:text-blue-300 mt-1">
                    Automatic backups are scheduled daily at 2:00 AM
                  </p>
                  <div className="mt-3 flex space-x-2">
                    <Button variant="outline" size="sm">
                      Create Backup Now
                    </Button>
                    <Button variant="outline" size="sm">
                      Restore from Backup
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'Vault-Encryption': // Added Vault section content
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Encryption</CardTitle>
              <CardDescription>Manage data encryption settings.</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="p-4 border rounded-lg">
                <div>
                  <h4 className="font-medium">Encryption Settings</h4>
                  <p className="text-sm text-muted-foreground">Configure encryption for your sensitive data.</p>
                </div>
                {/* Placeholder for actual encryption content */}
                <div className="mt-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                  <h5 className="font-medium text-yellow-800 dark:text-yellow-200">Encryption Status</h5>
                  <p className="text-sm text-yellow-600 dark:text-yellow-300 mt-1">
                    Encryption is currently disabled
                  </p>
                  <div className="mt-3 flex items-center">
                    <Switch id="encryption-toggle" />
                    <Label htmlFor="encryption-toggle" className="ml-2">
                      Enable Encryption
                    </Label>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'Addons-Plugins': // Changed from Plugins-Manage Plugins
        // Deduplicate plugins
        const uniquePlugins = [...new Map(plugins.map(p => [p.name, p])).values()];
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Plugins</CardTitle>
              <CardDescription>Enable or disable installed plugins. Configure the path to your plugins directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Section 1: Search Bar */}
              <div className="space-y-2">
                <h3 className="text-lg font-semibold">Search Plugins</h3>
                <StandardInput
                  id="plugin-search"
                  type="search"
                  placeholder="Search plugins..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200"
                />
              </div>

              {/* Section 2: Path Configuration */}
              <div className="space-y-4">
                <SimpleFolderSelector
                  label="Plugin Directory"
                  description="Select the main directory where plugins are located. The application will scan this directory for valid plugins."
                  fetchCommand="get_plugins_path"
                  saveCommand="set_plugins_path"
                  onFolderSelected={handlePluginsPathSelected}
                />
              </div>

              {/* Section 3: Plugin Display */}
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Installed Plugins</h3>
                {loadingPlugins && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">Loading plugins...</p>
                  </div>
                )}

                {pluginError && (
                  <div className="text-center py-8">
                    <p className="text-destructive">{pluginError}</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length === 0 && (
                  <div className="text-center py-8">
                    <p className="text-muted-foreground">No plugins found or installed.</p>
                  </div>
                )}

                {!loadingPlugins && !pluginError && uniquePlugins.length > 0 && (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {uniquePlugins
                      .filter(plugin =>
                        plugin.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                        (plugin.description && plugin.description.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((plugin) => (
                        <Card key={plugin.name} className="p-4">
                          <div className="flex flex-col space-y-3">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-semibold text-base">{plugin.name}</h4>
                                <span className="text-xs text-muted-foreground">v{plugin.version}</span>
                              </div>
                              <Switch
                                id={`plugin-${plugin.name}`}
                                checked={plugin.enabled}
                                onCheckedChange={() => togglePlugin(plugin.name, plugin.enabled)}
                              />
                            </div>
                            <p className="text-sm text-muted-foreground line-clamp-2">
                              {plugin.description || 'No description available.'}
                            </p>
                            <div className="flex items-center justify-between">
                              <Label htmlFor={`plugin-${plugin.name}`} className="text-sm font-medium">
                                {plugin.enabled ? 'Enabled' : 'Disabled'}
                              </Label>
                            </div>
                          </div>
                        </Card>
                      ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Extensions': // Content for Extensions under System
        return (
          <div className="m-6 space-y-6">
            {/* File Path Selector at the top */}
            <Card>
              <CardHeader>
                <CardTitle>Extensions Configuration</CardTitle>
                <CardDescription>Configure the directory where custom file extensions are stored.</CardDescription>
              </CardHeader>
              <CardContent>
                <SimpleFolderSelector
                  label="Extensions Directory"
                  description="Select the directory where custom file extensions are stored."
                  fetchCommand="get_extensions_path"
                  saveCommand="set_extensions_path"
                  onFolderSelected={handleExtensionsPathSelected}
                />
              </CardContent>
            </Card>

            {/* Extensions Registry Component */}
            <ExtensionRegistry />
          </div>
        );
      case 'Addons-APIs':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>APIs</CardTitle>
              <CardDescription>Configure the path to your API configurations directory or file.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="APIs Directory"
                description="Select the directory containing API configurations or a central API configuration file."
                fetchCommand="get_apis_path"
                saveCommand="set_apis_path"
              />
              {/* Placeholder for API keys management or list */}
              <p className="mt-4 text-muted-foreground">API configuration and key management interface will be here.</p>
            </CardContent>
          </Card>
        );
      case 'Addons-MCP':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>MCP</CardTitle>
              <CardDescription>Configure the path to your MCP (Model Context Protocol) directory.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <SimpleFolderSelector
                label="MCP Directory"
                description="Select the directory where MCPs are stored. The application will scan this directory for available MCPs."
                fetchCommand="get_mcps_path"
                saveCommand="set_mcps_path"
              />
              {/* Placeholder for MCP management interface */}
              <p className="mt-4 text-muted-foreground">MCP management interface will be here.</p>
            </CardContent>
          </Card>
        );
      case 'System-Network':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Network Settings</CardTitle>
              <CardDescription>Configure network connections, proxy settings, and projection options.</CardDescription>
            </CardHeader>
            <CardContent className="space-y-8">
              {/* Network Configuration Section */}
              <div className="border-b border-border pb-8">
                <h3 className="text-lg font-medium mb-4">Network Configuration</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <StandardInput
                    id="server-host"
                    label="Server Host"
                    description="The host address for the AI server"
                    placeholder="127.0.0.1"
                    value={userSettings?.server_host || '127.0.0.1'}
                    onChange={(e) => saveUserSetting('server_host', e.target.value)}
                  />
                  
                  <StandardInput
                    id="server-port"
                    label="Server Port"
                    description="The port number for the AI server"
                    type="number"
                    placeholder="11435"
                    value={userSettings?.server_port || 11435}
                    onChange={(e) => saveUserSetting('server_port', parseInt(e.target.value) || 11435)}
                  />
                  
                  <StandardSelect
                    id="network-protocol"
                    label="Network Protocol"
                    description="Select the network protocol to use"
                    options={[
                      { value: 'http', label: 'HTTP' },
                      { value: 'https', label: 'HTTPS' }
                    ]}
                    value={userSettings?.network_protocol || 'http'}
                    onValueChange={(value) => saveUserSetting('network_protocol', value)}
                  />
                  
                  <StandardSelect
                    id="proxy-mode"
                    label="Proxy Mode"
                    description="Configure proxy settings"
                    options={[
                      { value: 'none', label: 'No Proxy' },
                      { value: 'system', label: 'Use System Proxy' },
                      { value: 'manual', label: 'Manual Proxy' }
                    ]}
                    value={userSettings?.proxy_mode || 'none'}
                    onValueChange={(value) => saveUserSetting('proxy_mode', value)}
                  />
                </div>
                
                {userSettings?.proxy_mode === 'manual' && (
                  <div className="mt-6 grid grid-cols-1 md:grid-cols-2 gap-6">
                    <StandardInput
                      id="proxy-host"
                      label="Proxy Host"
                      description="Proxy server address"
                      placeholder="proxy.example.com"
                      value={userSettings?.proxy_host || ''}
                      onChange={(e) => saveUserSetting('proxy_host', e.target.value)}
                    />
                    
                    <StandardInput
                      id="proxy-port"
                      label="Proxy Port"
                      description="Proxy server port"
                      type="number"
                      placeholder="8080"
                      value={userSettings?.proxy_port || ''}
                      onChange={(e) => saveUserSetting('proxy_port', parseInt(e.target.value) || 0)}
                    />
                  </div>
                )}
              </div>
              
              {/* Connection Settings Section */}
              <div className="border-b border-border pb-8">
                <h3 className="text-lg font-medium mb-4">Connection Settings</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <StandardInput
                    id="connection-timeout"
                    label="Connection Timeout"
                    description="Timeout for network connections (seconds)"
                    type="number"
                    placeholder="30"
                    value={userSettings?.connection_timeout || 30}
                    onChange={(e) => saveUserSetting('connection_timeout', parseInt(e.target.value) || 30)}
                  />
                  
                  <StandardInput
                    id="max-retries"
                    label="Max Retries"
                    description="Maximum number of retry attempts"
                    type="number"
                    placeholder="3"
                    value={userSettings?.max_retries || 3}
                    onChange={(e) => saveUserSetting('max_retries', parseInt(e.target.value) || 3)}
                  />
                  
                  <StandardCheckbox
                    id="enable-ssl"
                    label="Enable SSL"
                    description="Use SSL/TLS encryption for connections"
                    checked={userSettings?.enable_ssl || false}
                    onCheckedChange={(checked) => saveUserSetting('enable_ssl', checked)}
                  />
                  
                  <StandardCheckbox
                    id="verify-certificates"
                    label="Verify Certificates"
                    description="Verify SSL certificates"
                    checked={userSettings?.verify_certificates || true}
                    onCheckedChange={(checked) => saveUserSetting('verify_certificates', checked)}
                  />
                </div>
              </div>
              
              {/* Projection Settings Section */}
              <div>
                <h3 className="text-lg font-medium mb-4">Projection Settings</h3>
                <p className="text-muted-foreground mb-4">
                  Configure screen projection and streaming options.
                </p>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <StandardInput
                    id="projection-port"
                    label="Projection Port"
                    description="Port used for projection services"
                    type="number"
                    placeholder="8080"
                    value={userSettings?.projection_port || 8080}
                    onChange={(e) => saveUserSetting('projection_port', parseInt(e.target.value) || 8080)}
                  />
                  
                  <StandardSelect
                    id="projection-protocol"
                    label="Projection Protocol"
                    description="Select the primary projection protocol"
                    options={[
                      { value: 'auto', label: 'Automatic' },
                      { value: 'rtsp', label: 'RTSP' },
                      { value: 'http', label: 'HTTP' },
                      { value: 'websocket', label: 'WebSocket' }
                    ]}
                    value={userSettings?.projection_protocol || 'auto'}
                    onValueChange={(value) => saveUserSetting('projection_protocol', value)}
                  />
                </div>
                
                <div className="mt-6 flex items-center space-x-4">
                  <StandardCheckbox
                    id="projection-local-only"
                    label="Local Network Only"
                    description="Restrict projection to local network devices only"
                    checked={userSettings?.projection_local_only || true}
                    onCheckedChange={(checked) => saveUserSetting('projection_local_only', checked)}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Network-project':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Project Network Settings</CardTitle>
              <CardDescription>Configure project-specific network settings and projection options.</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-8">
                {/* Projection Settings Section */}
                <div className="border-b border-border pb-8">
                  <h3 className="text-lg font-medium mb-4">Projection Settings</h3>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* iOS Projection */}
                    <Card className="border border-border">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center">
                          <div className="w-3 h-3 rounded-full bg-blue-500 mr-2"></div>
                          iOS Projection (AirPlay)
                        </CardTitle>
                        <CardDescription>Stream to Apple devices</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex flex-row items-center gap-4 py-3">
                          <div className="flex-1">
                            <div className="font-medium mb-1">Enable iOS Projection</div>
                            <div className="text-sm text-muted-foreground">Allow projection to iOS devices via AirPlay</div>
                          </div>
                          <Switch
                            id="ios-projection-enabled"
                            checked={userSettings?.ios_projection_enabled || false}
                            onCheckedChange={(checked) => {
                              console.log('iOS Projection switch changed to:', checked);
                              saveUserSetting('ios_projection_enabled', checked);
                            }}
                          />
                        </div>
                        
                        {userSettings?.ios_projection_enabled && (
                          <>
                            <StandardInput
                              id="ios-device-name"
                              label="Device Name"
                              description="Name that will appear on iOS devices"
                              placeholder="The Collective"
                              value={userSettings?.ios_device_name || 'The Collective'}
                              onChange={(e) => saveUserSetting('ios_device_name', e.target.value)}
                            />
                            <StandardInput
                              id="android-device-name"
                              label="Device Name"
                              description="Name that will appear on Android devices"
                              placeholder="The Collective"
                              value={userSettings?.android_device_name || 'The Collective'}
                              onChange={(e) => saveUserSetting('android_device_name', e.target.value)}
                            />
                            
                            <StandardSelect
                              id="ios-quality"
                              label="Quality"
                              description="Select projection quality for iOS devices"
                              options={[
                                { value: 'auto', label: 'Automatic' },
                                { value: 'high', label: 'High (1080p)' },
                                { value: 'medium', label: 'Medium (720p)' },
                { value: 'low', label: 'Low (480p)' }
                              ]}
                              value={userSettings?.ios_projection_quality || 'auto'}
                              onValueChange={(value) => saveUserSetting('ios_projection_quality', value)}
                            />
                          </>
                        )}
                      </CardContent>
                    </Card>
                    
                    {/* Android Projection */}
                    <Card className="border border-border">
                      <CardHeader className="pb-3">
                        <CardTitle className="text-base flex items-center">
                          <div className="w-3 h-3 rounded-full bg-green-500 mr-2"></div>
                          Android Projection (Chromecast)
                        </CardTitle>
                        <CardDescription>Stream to Android devices and Chromecast</CardDescription>
                      </CardHeader>
                      <CardContent className="space-y-4">
                        <div className="flex flex-row items-center gap-4 py-3">
                          <div className="flex-1">
                            <div className="font-medium mb-1">Enable Android Projection</div>
                            <div className="text-sm text-muted-foreground">Allow projection to Android devices via Chromecast</div>
                          </div>
                          <Switch
                            id="android-projection-enabled"
                            checked={userSettings?.android_projection_enabled || false}
                            onCheckedChange={(checked) => {
                              console.log('Android Projection switch changed to:', checked);
                              saveUserSetting('android_projection_enabled', checked);
                            }}
                          />
                        </div>
                        
                        {userSettings?.android_projection_enabled && (
                          <>
                            <StandardInput
                              id="android-device-name"
                              label="Device Name"
                              description="Name that will appear on Android devices"
                              placeholder="The Collective"
                              value={userSettings?.android_device_name || 'The Collective'}
                              onChange={(e) => saveUserSetting('android_device_name', e.target.value)}
                            />
                            
                            <StandardSelect
                              id="android-quality"
                              label="Quality"
                              description="Select projection quality for Android devices"
                              options={[
                                { value: 'auto', label: 'Automatic' },
                                { value: 'high', label: 'High (1080p)' },
                                { value: 'medium', label: 'Medium (720p)' },
                                { value: 'low', label: 'Low (480p)' }
                              ]}
                              value={userSettings?.android_projection_quality || 'auto'}
                              onValueChange={(value) => saveUserSetting('android_projection_quality', value)}
                            />
                          </>
                        )}
                      </CardContent>
                    </Card>
                  </div>
                  
                  {/* Other Projection Options */}
                  <Card className="mt-6 border border-border">
                    <CardHeader className="pb-3">
                      <CardTitle className="text-base flex items-center">
                        <div className="w-3 h-3 rounded-full bg-purple-500 mr-2"></div>
                        Other Projection Options
                      </CardTitle>
                      <CardDescription>Additional projection protocols and settings</CardDescription>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="flex flex-row items-center gap-4 py-3">
                        <div className="flex-1">
                          <div className="font-medium mb-1">Enable Miracast</div>
                          <div className="text-sm text-muted-foreground">Allow projection to Windows devices via Miracast</div>
                        </div>
                        <Switch
                          id="miracast-enabled"
                          checked={userSettings?.miracast_enabled || false}
                          onCheckedChange={(checked) => saveUserSetting('miracast_enabled', checked)}
                        />
                      </div>
                      
                      <div className="flex flex-row items-center gap-4 py-3">
                        <div className="flex-1">
                          <div className="font-medium mb-1">Enable DLNA</div>
                          <div className="text-sm text-muted-foreground">Allow projection to DLNA-compatible devices</div>
                        </div>
                        <Switch
                          id="dlna-enabled"
                          checked={userSettings?.dlna_enabled || false}
                          onCheckedChange={(checked) => saveUserSetting('dlna_enabled', checked)}
                        />
                      </div>
                      

                    </CardContent>
                  </Card>
                </div>
                
                {/* Network Configuration Section */}
                <div>
                  <h3 className="text-lg font-medium mb-4">Network Configuration</h3>
                  <p className="text-muted-foreground mb-4">
                    Configure network settings for optimal projection performance.
                  </p>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <StandardInput
                      id="projection-port"
                      label="Projection Port"
                      description="Port used for projection services"
                      type="number"
                      placeholder="8080"
                      value={userSettings?.projection_port || 8080}
                      onChange={(e) => saveUserSetting('projection_port', parseInt(e.target.value) || 8080)}
                    />
                    
                    <StandardSelect
                      id="projection-protocol"
                      label="Projection Protocol"
                      description="Select the primary projection protocol"
                      options={[
                        { value: 'auto', label: 'Automatic' },
                        { value: 'rtsp', label: 'RTSP' },
                        { value: 'http', label: 'HTTP' },
                        { value: 'websocket', label: 'WebSocket' }
                      ]}
                      value={userSettings?.projection_protocol || 'auto'}
                      onValueChange={(value) => saveUserSetting('projection_protocol', value)}
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Devices':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Device Management</CardTitle>
              <CardDescription>Manage connected devices and hardware integrations.</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="text-center py-12 text-muted-foreground">
                <FontAwesomeIcon icon={faHardDrive} className="h-12 w-12 mb-4 text-muted-foreground opacity-50" />
                <h3 className="text-lg font-medium mb-2">Device Management</h3>
                <p>Device management features will be implemented in a future update.</p>
              </div>
            </CardContent>
          </Card>
        );
      case 'System-Devices-bluetooth':
        return (
          <Card className="m-6">
            <CardHeader>
              <CardTitle>Bluetooth & Devices</CardTitle>
              <CardDescription>Manage Bluetooth connections and connected devices.</CardDescription>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                <div className="text-center py-8 text-muted-foreground">
                  <FontAwesomeIcon icon={faHardDrive} className="h-12 w-12 mb-4 text-muted-foreground opacity-50" />
                  <h3 className="text-lg font-medium mb-2">Bluetooth & Device Management</h3>
                  <p>Bluetooth and device management features will be implemented in a future update.</p>
                </div>
              </div>
            </CardContent>
          </Card>
        );
      // Add more cases for other settings pages as needed
      default:
        return (
          <div className="p-6">
            <Card className="overflow-hidden">
              <CardHeader className="pb-4">
                <CardTitle>{activeSidebarItem}</CardTitle>
                <CardDescription>Configure {activeSidebarItem.toLowerCase()} settings and preferences.</CardDescription>
              </CardHeader>
              <CardContent className="pt-2">
                <p className="text-muted-foreground">
                  Settings for {activeSidebarItem} will be displayed here. This is a placeholder.
                </p>
              </CardContent>
            </Card>
          </div>
        );
    }
  };

  const handleChatSubmit = (message) => {
    // Handle chat submission in settings page
    console.log('Chat message from settings:', message);
    // You can implement chat functionality here
  };

  // Generate breadcrumb path
  const generateBreadcrumbPath = () => {
    const path = [
      { name: 'Dashboard', icon: faHome, path: '/' },
      { name: 'Settings', icon: faCog, path: '/settings' }
    ];
    
    if (activeMainTab) {
      path.push({ name: activeMainTab, icon: mainTabsConfig.find(tab => tab.name === activeMainTab)?.icon || faCog, path: null });
    }
    
    if (activeSidebarItem) {
      const sidebarIcon = sidebarItemsConfig[activeMainTab]?.find(item => item.name === activeSidebarItem)?.icon || null;
      path.push({ name: activeSidebarItem, icon: sidebarIcon, path: null });
    }
    
    return path;
  };

  const breadcrumbPath = generateBreadcrumbPath();

  return (
    <div className="flex flex-col h-screen bg-background text-foreground overflow-hidden">


      {/* Modern Tab Navigation */}
      <Tabs value={activeMainTab} onValueChange={handleMainTabChange} className="border-b border-border">
        <TabsList className="flex justify-start px-4 pt-2 bg-card rounded-none w-full">
          {mainTabsConfig.map(tab => (
            <TabsTrigger 
              key={tab.name} 
              value={tab.name} 
              className="px-6 py-3 text-base data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary data-[state=active]:shadow-none rounded-none transition-all duration-200"
            >
              <div className="flex items-center space-x-2">
                <FontAwesomeIcon icon={tab.icon} className="h-5 w-5" />
                <span>{tab.name}</span>
              </div>
            </TabsTrigger>
          ))}
        </TabsList>
      </Tabs>

      <div className="flex flex-1 overflow-hidden">
        {/* Enhanced Sidebar with Modern Design */}
        <aside className="w-72 border-r border-border bg-card flex flex-col">
          <div className="p-4">
            <div className="relative">
              <FontAwesomeIcon icon={faSearch} className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground z-10" />
              <StandardInput
                id="settings-search"
                type="search"
                placeholder="Search settings..."
                className="pl-10 bg-slate-50 dark:bg-slate-800/60 border-slate-200 dark:border-slate-700 hover:border-blue-300 dark:hover:border-blue-600 focus-visible:ring-blue-500/30 transition-all duration-200 h-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                containerClassName="m-0"
              />
            </div>
          </div>
          
          {/* Section Header */}
          <div className="px-4 py-1 text-xs font-semibold text-muted-foreground uppercase tracking-wider">
            {activeMainTab} Settings
          </div>
          
          <nav className="flex-1 overflow-y-auto px-2 pb-4 thin-scrollbar">
            {filteredSidebarItems.map(item => {
              const hasSubItems = item.subItems && item.subItems.length > 0;
              const isExpanded = expandedSidebarItems.includes(item.name);
              const isActive = activeSidebarItem.startsWith(item.name);
              const activePath = activeSidebarItem.split('-')[1];
              
              return (
                <div key={item.name} className="mb-1">
                  <Button
                    variant={isActive ? "secondary" : "ghost"}
                    className={`w-full justify-between text-base font-normal h-11 transition-all duration-200 ${
                      isActive 
                        ? 'bg-primary/10 text-primary shadow-sm' 
                        : 'hover:bg-accent hover:text-accent-foreground'
                    }`}
                    onClick={() => {
                      if (hasSubItems) {
                        // Toggle expanded state
                        setExpandedSidebarItems(prev => 
                          prev.includes(item.name)
                            ? prev.filter(name => name !== item.name)
                            : [...prev, item.name]
                        );
                        
                        // If clicking on an item with subitems and it's not already selected, 
                        // or if it's a parent item without a specific subitem selected,
                        // select the first subitem
                        if (!isActive || !activePath) {
                          if (item.subItems.length > 0) {
                            setActiveSidebarItem(`${item.name}-${item.subItems[0].path}`);
                          }
                        }
                      } else {
                        // For items without subitems, just select the item directly
                        setActiveSidebarItem(item.name);
                      }
                    }}
                  >
                    <div className="flex items-center">
                      <FontAwesomeIcon 
                        icon={item.icon} 
                        className={`mr-3 h-5 w-5 ${isActive ? 'text-primary' : 'text-muted-foreground'}`} 
                      />
                      <span className="truncate">{item.name}</span>
                    </div>
                    {hasSubItems && (
                      <ChevronDown className={`h-4 w-4 transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`} />
                    )}
                  </Button>
                  
                  {/* Sub-items dropdown */}
                  {hasSubItems && isExpanded && (
                    <div className="ml-6 mt-1 space-y-1 pl-4 border-l border-border">
                      {item.subItems.map(subItem => (
                        <Button
                          key={subItem.path}
                          variant={activePath === subItem.path ? "secondary" : "ghost"}
                          className={`w-full justify-start text-sm font-normal h-9 transition-all duration-200 ${
                            activePath === subItem.path 
                              ? 'bg-primary/10 text-primary' 
                              : 'hover:bg-accent hover:text-accent-foreground'
                          }`}
                          onClick={() => setActiveSidebarItem(`${item.name}-${subItem.path}`)}
                        >
                          <span className="truncate">{subItem.name}</span>
                        </Button>
                      ))}
                    </div>
                  )}
                </div>
              );
            })}
            {filteredSidebarItems.length === 0 && searchTerm && (
                <p className='text-sm text-muted-foreground text-center py-4'>No settings found for "{searchTerm}".</p>
            )}
          </nav>
        </aside>

        <main className="flex-1 overflow-hidden bg-muted/20">
          <div className="h-full overflow-y-auto thin-scrollbar">
            <div className="max-w-6xl mx-auto p-6">
              {renderContent()}
            </div>
          </div>
        </main>
      </div>
    </div>
  );
});

// Add display name for better debugging
Settings.displayName = 'Settings';

export default Settings;