import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Switch, 
  FormControlLabel,
  TextField,
  Button,
  Divider,
  Grid,
  InputAdornment,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  VpnKey as VpnKeyIcon,
  Lock as LockIcon,
  Public as PublicIcon,
  Info as InfoIcon,
} from '@mui/icons-material';

const NetworkProxyVpn = () => {
  const [useProxy, setUseProxy] = React.useState(false);
  const [proxySettings, setProxySettings] = React.useState({
    host: '',
    port: '',
    username: '',
    password: '',
  });
  const [useVpn, setUseVpn] = React.useState(false);
  const [vpnSettings, setVpnSettings] = React.useState({
    server: '',
    protocol: 'OpenVPN',
    username: '',
    password: '',
  });
  const [isLoading, setIsLoading] = React.useState(false);
  const [error, setError] = React.useState('');
  const [success, setSuccess] = React.useState('');

  const handleProxyChange = (e) => {
    const { name, value } = e.target;
    setProxySettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleVpnChange = (e) => {
    const { name, value } = e.target;
    setVpnSettings(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSaveProxy = async () => {
    try {
      setIsLoading(true);
      setError('');
      // TODO: Implement proxy save logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess('Proxy settings saved successfully');
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError('Failed to save proxy settings');
    } finally {
      setIsLoading(false);
    }
  };

  const handleConnectVpn = async () => {
    try {
      setIsLoading(true);
      setError('');
      // TODO: Implement VPN connection logic
      await new Promise(resolve => setTimeout(resolve, 1000));
      setSuccess(useVpn ? 'VPN disconnected successfully' : 'VPN connected successfully');
      setUseVpn(!useVpn);
      setTimeout(() => setSuccess(''), 3000);
    } catch (err) {
      setError(useVpn ? 'Failed to disconnect VPN' : 'Failed to connect to VPN');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}
      
      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      {/* Proxy Settings */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" component="h2">
            <PublicIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            Proxy Settings
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={useProxy}
                onChange={(e) => setUseProxy(e.target.checked)}
                color="primary"
              />
            }
            label={useProxy ? 'Proxy Enabled' : 'Proxy Disabled'}
          />
        </Box>

        {useProxy && (
          <Box>
            <Grid container spacing={2}>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Proxy Host"
                  name="host"
                  value={proxySettings.host}
                  onChange={handleProxyChange}
                  margin="normal"
                  variant="outlined"
                  placeholder="proxy.example.com"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Port"
                  name="port"
                  type="number"
                  value={proxySettings.port}
                  onChange={handleProxyChange}
                  margin="normal"
                  variant="outlined"
                  placeholder="8080"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Username (Optional)"
                  name="username"
                  value={proxySettings.username}
                  onChange={handleProxyChange}
                  margin="normal"
                  variant="outlined"
                />
              </Grid>
              <Grid item xs={12} md={6}>
                <TextField
                  fullWidth
                  label="Password"
                  name="password"
                  type="password"
                  value={proxySettings.password}
                  onChange={handleProxyChange}
                  margin="normal"
                  variant="outlined"
                />
              </Grid>
            </Grid>

            <Box sx={{ mt: 3, display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                variant="contained"
                color="primary"
                onClick={handleSaveProxy}
                disabled={isLoading || !proxySettings.host || !proxySettings.port}
                startIcon={isLoading ? <CircularProgress size={20} /> : null}
              >
                {isLoading ? 'Saving...' : 'Save Proxy Settings'}
              </Button>
            </Box>
          </Box>
        )}
      </Paper>

      <Divider sx={{ my: 4 }} />

      {/* VPN Settings */}
      <Paper sx={{ p: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
          <Typography variant="h6" component="h2">
            <VpnKeyIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
            VPN Settings
          </Typography>
          <FormControlLabel
            control={
              <Switch
                checked={useVpn}
                onChange={handleConnectVpn}
                color="primary"
                disabled={isLoading}
              />
            }
            label={useVpn ? 'VPN Connected' : 'VPN Disconnected'}
          />
        </Box>

        <Grid container spacing={2}>
          <Grid item xs={12}>
            <TextField
              fullWidth
              select
              label="Protocol"
              name="protocol"
              value={vpnSettings.protocol}
              onChange={handleVpnChange}
              margin="normal"
              variant="outlined"
              SelectProps={{
                native: true,
              }}
            >
              <option value="OpenVPN">OpenVPN</option>
              <option value="WireGuard">WireGuard</option>
              <option value="IKEv2">IKEv2</option>
              <option value="L2TP/IPsec">L2TP/IPsec</option>
              <option value="PPTP">PPTP</option>
            </TextField>
          </Grid>
          
          <Grid item xs={12}>
            <TextField
              fullWidth
              label="Server Address"
              name="server"
              value={vpnSettings.server}
              onChange={handleVpnChange}
              margin="normal"
              variant="outlined"
              placeholder="vpn.example.com"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <PublicIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Username"
              name="username"
              value={vpnSettings.username}
              onChange={handleVpnChange}
              margin="normal"
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <VpnKeyIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
          
          <Grid item xs={12} md={6}>
            <TextField
              fullWidth
              label="Password"
              name="password"
              type="password"
              value={vpnSettings.password}
              onChange={handleVpnChange}
              margin="normal"
              variant="outlined"
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <LockIcon />
                  </InputAdornment>
                ),
              }}
            />
          </Grid>
        </Grid>

        <Box sx={{ mt: 3, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <InfoIcon color="action" sx={{ mr: 1 }} />
            <Typography variant="body2" color="text.secondary">
              {useVpn 
                ? 'Your connection is secured with VPN.' 
                : 'Connect to a VPN for enhanced privacy and security.'}
            </Typography>
          </Box>
          
          <Button
            variant="contained"
            color={useVpn ? 'error' : 'primary'}
            onClick={handleConnectVpn}
            disabled={isLoading || !vpnSettings.server || !vpnSettings.username || !vpnSettings.password}
            startIcon={
              isLoading ? (
                <CircularProgress size={20} color="inherit" />
              ) : useVpn ? (
                <LockIcon />
              ) : (
                <VpnKeyIcon />
              )
            }
          >
            {isLoading 
              ? 'Processing...' 
              : useVpn 
                ? 'Disconnect VPN' 
                : 'Connect to VPN'}
          </Button>
        </Box>
      </Paper>
    </Box>
  );
};

export default NetworkProxyVpn;
