import { create } from 'zustand';
import { invoke } from '@tauri-apps/api/core';

const usePluginStore = create((set, get) => ({
  plugins: [],
  loading: false,
  error: null,
  fetchPlugins: async (force = false) => {
    if (!force && (get().plugins.length > 0 || get().loading)) {
      return get().plugins;
    }

    set({ loading: true, error: null });
    try {
      const plugins = await invoke('get_plugins');
      set({ plugins, loading: false });
      return plugins;
    } catch (error) {
      set({ error, loading: false });
      console.error("Failed to fetch plugins:", error);
    }
  },
}));

export default usePluginStore;