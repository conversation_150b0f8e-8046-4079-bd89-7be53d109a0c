import React from 'react';
import { useSettings } from '../contexts/SettingsContext';
import { useTheme } from '../contexts/ThemeContext';
import { useUIState } from '../contexts/UIStateContext';
import { useNotifications } from '../contexts/NotificationContext';
import saveManager from '../utils/SaveManager';

const SaveSystemTest = () => {
  const { userSettings, updateSetting } = useSettings();
  const { selectColorScheme, availableColorSchemes } = useTheme();
  const { uiState, updateUIState } = useUIState();
  const { addNotification } = useNotifications();

  const testThemeSave = async () => {
    const randomScheme = availableColorSchemes[Math.floor(Math.random() * availableColorSchemes.length)];
    await selectColorScheme(randomScheme.id);
    addNotification({
      type: 'info',
      message: `Testing theme save: ${randomScheme.name}`
    });
  };

  const testSettingSave = async () => {
    const randomValue = Math.random() > 0.5;
    await updateSetting('appearance', { dark_mode: randomValue });
    addNotification({
      type: 'info',
      message: `Testing setting save: dark_mode = ${randomValue}`
    });
  };

  const testUIStateSave = async () => {
    const randomTab = ['chat', 'settings', 'tools'][Math.floor(Math.random() * 3)];
    await updateUIState({ active_tab: randomTab });
    addNotification({
      type: 'info',
      message: `Testing UI state save: active_tab = ${randomTab}`
    });
  };

  const showStats = () => {
    const stats = saveManager.getStats();
    addNotification({
      type: 'info',
      message: `Save stats: ${stats.saveCount} saves, last: ${new Date(stats.lastSaveTime).toLocaleTimeString()}`
    });
  };

  return (
    <div className="p-4 bg-card rounded-lg border">
      <h3 className="text-lg font-semibold mb-4">Save System Test Panel</h3>
      
      <div className="space-y-2 mb-4">
        <button 
          onClick={testThemeSave}
          className="px-4 py-2 bg-primary text-primary-foreground rounded hover:bg-primary/90"
        >
          Test Theme Save
        </button>
        
        <button 
          onClick={testSettingSave}
          className="px-4 py-2 bg-secondary text-secondary-foreground rounded hover:bg-secondary/90"
        >
          Test Setting Save
        </button>
        
        <button 
          onClick={testUIStateSave}
          className="px-4 py-2 bg-accent text-accent-foreground rounded hover:bg-accent/90"
        >
          Test UI State Save
        </button>
        
        <button 
          onClick={showStats}
          className="px-4 py-2 bg-muted text-muted-foreground rounded hover:bg-muted/90"
        >
          Show Save Stats
        </button>
      </div>

      <div className="text-sm text-muted-foreground space-y-1">
        <div>Current Theme: {userSettings?.appearance?.color_scheme || 'Unknown'}</div>
        <div>Dark Mode: {userSettings?.appearance?.dark_mode ? 'Yes' : 'No'}</div>
        <div>Active Tab: {uiState?.active_tab || 'Unknown'}</div>
        <div>Last Save: {saveManager.getStats().lastSaveTime.toLocaleTimeString()}</div>
      </div>
    </div>
  );
};

export default SaveSystemTest;
