import React, { useEffect, useState } from 'react';
import { useTheme } from '../../contexts/ThemeContext';
// Simple toast replacement
const toast = {
  success: (message) => alert(`✅ ${message}`),
  error: (message) => alert(`❌ ${message}`)
};
import { Card, CardHeader, CardTitle, CardContent, CardDescription } from '../ui/card';
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { ThemeExporter } from "./ThemeExporter";
import { ThemeImporter } from "./ThemeImporter";
import { cn } from '../../lib/utils';
import { motion, AnimatePresence } from 'framer-motion';

const ColorSchemePreview = ({ scheme, isSelected, onClick }) => {
  const colors = [
    { name: 'Background', value: scheme.colors.background },
    { name: 'Surface', value: scheme.colors.surface },
    { name: 'Primary', value: scheme.colors.primary },
    { name: 'Accent', value: scheme.colors.accent },
    { name: 'Text', value: scheme.colors.text },
  ];

  return (
    <motion.div 
      className={cn(
        "relative rounded-xl overflow-hidden border-2 cursor-pointer transition-all duration-200",
        isSelected 
          ? "ring-2 ring-primary ring-offset-2 ring-offset-background scale-[1.02] shadow-lg" 
          : "border-border hover:border-primary/50 hover:shadow-md"
      )}
      onClick={onClick}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className="h-20 flex">
        {colors.map((color, i) => (
          <div 
            key={i}
            className="h-full flex-1 relative group"
            style={{ backgroundColor: color.value }}
            title={`${color.name}: ${color.value}`}
          >
            <div className="absolute inset-0 bg-black/10 opacity-0 group-hover:opacity-100 transition-opacity" />
          </div>
        ))}
      </div>
      <div className="p-3 bg-card">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-foreground">{scheme.name}</span>
          {isSelected && (
            <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-primary/10 text-primary">
              Active
            </span>
          )}
        </div>
        <div className="mt-1 flex items-center space-x-1">
          {colors.map((color, i) => (
            <div 
              key={i}
              className="h-3 w-3 rounded-full border border-border/50"
              style={{ backgroundColor: color.value }}
              title={`${color.name}: ${color.value}`}
            />
          ))}
        </div>
      </div>
    </motion.div>
  );
};

const BackgroundOption = ({ option, isSelected, onClick }) => {
  const [isHovered, setIsHovered] = useState(false);
  
  return (
    <motion.div 
      className={cn(
        "relative rounded-xl overflow-hidden border-2 cursor-pointer transition-all duration-200",
        isSelected 
          ? "ring-2 ring-primary ring-offset-2 ring-offset-background scale-[1.02] shadow-lg" 
          : "border-border hover:border-primary/50 hover:shadow-md"
      )}
      onClick={onClick}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      <div className={`h-20 rounded-t-lg ${option.preview || option.value} flex items-center justify-center`}>
        <AnimatePresence>
          {isHovered && (
            <motion.div 
              className="absolute inset-0 flex items-center justify-center bg-black/30"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              <Button 
                variant="outline" 
                size="sm"
                className="backdrop-blur-sm bg-background/80"
              >
                {isSelected ? 'Selected' : 'Select'}
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
        {isSelected && (
          <div className="absolute top-2 right-2 w-5 h-5 rounded-full bg-primary flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
              <polyline points="20 6 9 17 4 12"></polyline>
            </svg>
          </div>
        )}
      </div>
      <div className="p-3 bg-card border-t">
        <div className="text-sm font-medium text-foreground">{option.name}</div>
      </div>
    </motion.div>
  );
};

const FontOption = ({ font, isSelected, onClick }) => {
  const fontClasses = {
    sans: 'font-sans',
    serif: 'font-serif',
    mono: 'font-mono',
    display: 'font-display',
    rounded: 'font-sans', // Using sans as fallback
    tech: 'font-sans',    // Using sans as fallback
  };

  return (
    <motion.div 
      className={cn(
        "p-4 border rounded-xl cursor-pointer transition-all duration-200 relative overflow-hidden",
        isSelected 
          ? 'border-primary bg-primary/10 ring-1 ring-primary/20' 
          : 'border-border hover:border-primary/50 hover:bg-accent/10',
        fontClasses[font.id] || 'font-sans'
      )}
      onClick={onClick}
      whileHover={{ y: -2 }}
      whileTap={{ scale: 0.98 }}
    >
      {isSelected && (
        <div className="absolute top-2 right-2 w-5 h-5 rounded-full bg-primary flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
            <polyline points="20 6 9 17 4 12"></polyline>
          </svg>
        </div>
      )}
      <div className="font-medium text-foreground">{font.name}</div>
      <div className="text-sm text-muted-foreground mt-1">Aa Bb Cc Xx Yy Zz</div>
      <div className="text-xs text-muted-foreground mt-2 opacity-70">
        {font.id === 'sans' && 'Clean and modern interface text'}
        {font.id === 'serif' && 'Classic and professional typography'}
        {font.id === 'mono' && 'Perfect for code and technical content'}
        {font.id === 'display' && 'Bold and attention-grabbing'}
        {font.id === 'rounded' && 'Friendly and approachable style'}
        {font.id === 'tech' && 'Technical and futuristic feel'}
      </div>
    </motion.div>
  );
};

const Section = ({ title, description, children, className }) => (
  <div className={cn("space-y-4", className)}>
    <div>
      <h3 className="text-lg font-medium text-foreground">{title}</h3>
      {description && (
        <p className="text-sm text-muted-foreground">{description}</p>
      )}
    </div>
    {children}
  </div>
);

export default function ThemeSettings() {
  const [activeTab, setActiveTab] = useState('colors');
  const [isTransitioning, setIsTransitioning] = useState(false);
  
  const {
    colorScheme: activeScheme,
    background: activeBackground,
    fontFamily,
    isDarkMode,
    selectColorScheme,
    selectBackground,
    selectFont,
    toggleDarkMode,
    addColorScheme,
    updateColorScheme,
    deleteColorScheme,
    addBackground,
    updateBackground,
    deleteBackground,
    updateSetting,
    availableColorSchemes,
    availableBackgrounds,
    availableFonts
  } = useTheme();

  // Defensive fallbacks to avoid runtime crashes
  const safeColorScheme = activeScheme || { id: 'voidCircuit', name: 'Void Circuit', colors: { background: '#000', surface: '#111', primary: '#222', accent: '#333', text: '#fff' } };
  const safeBackground = activeBackground || { id: 'solid', name: 'Solid', value: 'bg-background', preview: 'bg-background' };
  const safeAvailableColorSchemes = Array.isArray(availableColorSchemes) ? availableColorSchemes : [];
  const safeAvailableBackgrounds = Array.isArray(availableBackgrounds) ? availableBackgrounds : [];
  const safeAvailableFonts = Array.isArray(availableFonts) ? availableFonts : [];
  
  const handleTabChange = (tab) => {
    setIsTransitioning(true);
    setTimeout(() => {
      setActiveTab(tab);
      setTimeout(() => setIsTransitioning(false), 50);
    }, 200);
  };

  // Local editing state for a color scheme
  const [editorOpen, setEditorOpen] = useState(false);
  const [draftScheme, setDraftScheme] = useState(null);
  const [bgEditorOpen, setBgEditorOpen] = useState(false);
  const [draftBackground, setDraftBackground] = useState(null);

  const beginCreateScheme = () => {
    setDraftScheme({
      id: `scheme_${Date.now()}`,
      name: 'New Scheme',
      colors: {
        background: safeColorScheme.colors?.background || '#0F1115',
        surface: safeColorScheme.colors?.surface || '#1C1F26',
        primary: safeColorScheme.colors?.primary || '#8E24AA',
        accent: safeColorScheme.colors?.accent || '#FF7043',
        text: safeColorScheme.colors?.text || '#CFD8DC',
      },
    });
    setEditorOpen(true);
  };

  const beginEditScheme = (scheme) => {
    setDraftScheme(JSON.parse(JSON.stringify(scheme)));
    setEditorOpen(true);
  };

  const applyDraftField = (path, value) => {
    setDraftScheme(prev => {
      const next = JSON.parse(JSON.stringify(prev || {}));
      const keys = path.split('.');
      let obj = next;
      while (keys.length > 1) {
        const k = keys.shift();
        obj[k] = obj[k] || {};
        obj = obj[k];
      }
      obj[keys[0]] = value;
      return next;
    });
  };

  const saveDraftScheme = () => {
    if (!draftScheme || !draftScheme.id) return;
    // Add or update
    const exists = safeAvailableColorSchemes.some(s => s.id === draftScheme.id);
    if (exists) {
      updateColorScheme && updateColorScheme(draftScheme.id, draftScheme);
      selectColorScheme && selectColorScheme(draftScheme.id);
    } else {
      addColorScheme && addColorScheme(draftScheme);
      selectColorScheme && selectColorScheme(draftScheme.id);
    }
    setEditorOpen(false);
  };

  const removeScheme = (id) => {
    if (!id) return;
    deleteColorScheme && deleteColorScheme(id);
  };

  // Background editor helpers
  const beginCreateBackground = () => {
    setDraftBackground({
      id: `bg_${Date.now()}`,
      name: 'New Background',
      value: 'bg-background',
      preview: 'bg-background',
    });
    setBgEditorOpen(true);
  };

  const beginEditBackground = (bg) => {
    setDraftBackground(JSON.parse(JSON.stringify(bg)));
    setBgEditorOpen(true);
  };

  const applyBgField = (key, value) => {
    setDraftBackground(prev => ({ ...(prev || {}), [key]: value }));
  };

  const saveDraftBackground = () => {
    if (!draftBackground || !draftBackground.id) return;
    const exists = safeAvailableBackgrounds.some(b => b.id === draftBackground.id);
    if (exists) {
      updateBackground && updateBackground(draftBackground.id, draftBackground);
      selectBackground && selectBackground(draftBackground.id);
    } else {
      addBackground && addBackground(draftBackground);
      selectBackground && selectBackground(draftBackground.id);
    }
    setBgEditorOpen(false);
  };

  const removeBackground = (id) => {
    if (!id) return;
    deleteBackground && deleteBackground(id);
  };

  return (
    <div className="space-y-6">
      {/* Tabs Navigation */}
      <div className="border-b">
        <div className="flex space-x-2 overflow-x-auto pb-px">
          {[
            { id: 'colors', label: 'Colors' },
            { id: 'backgrounds', label: 'Backgrounds' },
            { id: 'typography', label: 'Typography' },
            { id: 'appearance', label: 'Appearance' },
          ].map((tab) => (
            <button
              key={tab.id}
              className={cn(
                'px-4 py-2 text-sm font-medium rounded-t-lg transition-colors',
                activeTab === tab.id
                  ? 'text-primary border-b-2 border-primary bg-primary/5'
                  : 'text-muted-foreground hover:text-foreground hover:bg-accent/50'
              )}
              onClick={() => handleTabChange(tab.id)}
            >
              {tab.label}
            </button>
          ))}
        </div>
      </div>

      {/* Tab Content */}
      <div className="min-h-[400px]">
        <AnimatePresence mode="wait">
          <motion.div
            key={activeTab}
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="space-y-6"
          >
            {activeTab === 'colors' && (
              <Section 
                title="Color Scheme" 
                description="Choose a color scheme that matches your style"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="text-sm text-muted-foreground">Select, create, or edit your color schemes.</div>
                  <div className="space-x-2">
                    <Button size="sm" variant="outline" onClick={beginCreateScheme}>New Scheme</Button>
                    <Button size="sm" variant="outline" onClick={() => beginEditScheme(safeColorScheme)}>Edit Current</Button>
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {safeAvailableColorSchemes.map((scheme) => (
                    <ColorSchemePreview
                      key={scheme.id}
                      scheme={scheme}
                      isSelected={safeColorScheme?.id === scheme.id}
                      onClick={() => selectColorScheme(scheme.id)}
                    />
                  ))}
                </div>
                {/* Simple inline editor */}
                <AnimatePresence>
                  {editorOpen && draftScheme && (
                    <motion.div initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -6 }} className="mt-4 p-4 border rounded-xl bg-card">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="scheme-name">Scheme Name</Label>
                          <input id="scheme-name" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftScheme.name || ''} onChange={(e) => applyDraftField('name', e.target.value)} />
                        </div>
                        <div>
                          <Label htmlFor="scheme-id">ID</Label>
                          <input id="scheme-id" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftScheme.id || ''} onChange={(e) => applyDraftField('id', e.target.value)} />
                        </div>
                        {['background','surface','primary','accent','text'].map((k) => (
                          <div key={k} className="flex items-center gap-3">
                            <div className="w-20">
                              <Label>{k.charAt(0).toUpperCase()+k.slice(1)}</Label>
                            </div>
                            <input type="color" className="w-12 h-8 rounded cursor-pointer" value={draftScheme.colors?.[k] || '#000000'} onChange={(e) => applyDraftField(`colors.${k}`, e.target.value)} />
                            <input className="flex-1 px-3 py-2 rounded-md border bg-background" value={draftScheme.colors?.[k] || ''} onChange={(e) => applyDraftField(`colors.${k}`, e.target.value)} />
                          </div>
                        ))}
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Button size="sm" onClick={saveDraftScheme}>Save Scheme</Button>
                        <Button size="sm" variant="outline" onClick={() => setEditorOpen(false)}>Cancel</Button>
                        {safeAvailableColorSchemes.some(s => s.id === draftScheme.id) && (
                          <Button size="sm" variant="destructive" onClick={() => { removeScheme(draftScheme.id); setEditorOpen(false); }}>Delete</Button>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Section>
            )}

            {activeTab === 'backgrounds' && (
              <Section 
                title="Background Style" 
                description="Customize your background style and texture"
              >
                <div className="flex items-center justify-between mb-3">
                  <div className="text-sm text-muted-foreground">Select, create, or edit backgrounds.</div>
                  <div className="space-x-2">
                    <Button size="sm" variant="outline" onClick={beginCreateBackground}>New Background</Button>
                    <Button size="sm" variant="outline" onClick={() => beginEditBackground(safeBackground)}>Edit Current</Button>
                  </div>
                </div>
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                  {safeAvailableBackgrounds.map((bg) => (
                    <BackgroundOption
                      key={bg.id}
                      option={bg}
                      isSelected={safeBackground?.id === bg.id}
                      onClick={() => selectBackground(bg.id)}
                    />
                  ))}
                </div>
                <AnimatePresence>
                  {bgEditorOpen && draftBackground && (
                    <motion.div initial={{ opacity: 0, y: 6 }} animate={{ opacity: 1, y: 0 }} exit={{ opacity: 0, y: -6 }} className="mt-4 p-4 border rounded-xl bg-card">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="bg-name">Background Name</Label>
                          <input id="bg-name" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftBackground.name || ''} onChange={(e) => applyBgField('name', e.target.value)} />
                        </div>
                        <div>
                          <Label htmlFor="bg-id">ID</Label>
                          <input id="bg-id" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftBackground.id || ''} onChange={(e) => applyBgField('id', e.target.value)} />
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="bg-class">Tailwind/CSS Class</Label>
                          <input id="bg-class" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftBackground.value || ''} onChange={(e) => applyBgField('value', e.target.value)} placeholder="e.g., bg-grid-pattern or custom class" />
                          <div className="text-xs text-muted-foreground mt-1">This class will be applied to the app wrapper.</div>
                        </div>
                        <div className="md:col-span-2">
                          <Label htmlFor="bg-preview">Preview Class (optional)</Label>
                          <input id="bg-preview" className="w-full mt-1 px-3 py-2 rounded-md border bg-background" value={draftBackground.preview || ''} onChange={(e) => applyBgField('preview', e.target.value)} placeholder="Defaults to same as class" />
                        </div>
                      </div>
                      <div className="mt-4 flex items-center gap-2">
                        <Button size="sm" onClick={saveDraftBackground}>Save Background</Button>
                        <Button size="sm" variant="outline" onClick={() => setBgEditorOpen(false)}>Cancel</Button>
                        {safeAvailableBackgrounds.some(b => b.id === draftBackground.id) && (
                          <Button size="sm" variant="destructive" onClick={() => { removeBackground(draftBackground.id); setBgEditorOpen(false); }}>Delete</Button>
                        )}
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Section>
            )}

            {activeTab === 'typography' && (
              <Section 
                title="Typography" 
                description="Choose a font family for your interface"
              >
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {safeAvailableFonts.map((font) => (
                    <FontOption
                      key={font.id}
                      font={font}
                      isSelected={fontFamily === font.id}
                      onClick={() => selectFont(font.id)}
                    />
                  ))}
                </div>
              </Section>
            )}

            {activeTab === 'appearance' && (
              <Section 
                title="Appearance" 
                description="Customize the look and feel of the application"
              >
                <div className="space-y-4">
                  <div className="flex items-center justify-between p-4 rounded-lg bg-card border">
                    <div>
                      <div className="font-medium">Dark Mode</div>
                      <div className="text-sm text-muted-foreground">
                        {isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled'}
                      </div>
                    </div>
                    <Switch
                      checked={isDarkMode}
                      onCheckedChange={toggleDarkMode}
                      className="data-[state=checked]:bg-primary"
                    />
                  </div>

                  {/* Direct selection controls */}
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="p-4 rounded-lg bg-card border">
                      <Label className="block mb-2">Color Scheme</Label>
                      <select
                        className="w-full px-3 py-2 rounded-md border bg-background"
                        value={safeColorScheme?.id || ''}
                        onChange={(e) => selectColorScheme(e.target.value)}
                      >
                        {safeAvailableColorSchemes.map((s) => (
                          <option key={s.id} value={s.id}>{s.name}</option>
                        ))}
                      </select>
                    </div>
                    <div className="p-4 rounded-lg bg-card border">
                      <Label className="block mb-2">Background</Label>
                      <select
                        className="w-full px-3 py-2 rounded-md border bg-background"
                        value={safeBackground?.id || ''}
                        onChange={(e) => selectBackground(e.target.value)}
                      >
                        {safeAvailableBackgrounds.map((b) => (
                          <option key={b.id} value={b.id}>{b.name}</option>
                        ))}
                      </select>
                    </div>
                    <div className="p-4 rounded-lg bg-card border">
                      <Label className="block mb-2">Font</Label>
                      <select
                        className="w-full px-3 py-2 rounded-md border bg-background"
                        value={fontFamily}
                        onChange={(e) => selectFont(e.target.value)}
                      >
                        {safeAvailableFonts.map((f) => (
                          <option key={f.id} value={f.id}>{f.name}</option>
                        ))}
                      </select>
                    </div>

                  <div className="p-6 rounded-lg bg-card border space-y-6">
                    <h4 className="font-medium text-lg">UI Preview</h4>
                    
                    {/* Typography */}
                    <div className="space-y-2">
                      <h1 className="text-2xl font-bold text-foreground">Heading 1</h1>
                      <h2 className="text-xl font-semibold text-foreground/90">Heading 2</h2>
                      <p className="text-foreground">This is a paragraph of regular text that shows how body copy will look with the current theme settings.</p>
                      <p className="text-muted-foreground text-sm">Secondary text for less important information.</p>
                    </div>

                    {/* Buttons */}
                    <div className="flex flex-wrap items-center gap-3 pt-2">
                      <Button>Primary</Button>
                      <Button variant="secondary">Secondary</Button>
                      <Button variant="outline">Outline</Button>
                      <Button variant="ghost">Ghost</Button>
                      <Button variant="link">Link</Button>
                      <Button variant="destructive">Destructive</Button>
                    </div>

                    {/* Form Elements */}
                    <div className="grid gap-4 pt-2">
                      <div>
                        <Label htmlFor="preview-input">Input Field</Label>
                        <Input id="preview-input" placeholder="Type something..." className="mt-1" />
                      </div>
                      <div>
                        <Label htmlFor="preview-select">Dropdown</Label>
                        <select
                          id="preview-select"
                          className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                        >
                          <option value="">Select an option</option>
                          <option value="1">Option 1</option>
                          <option value="2">Option 2</option>
                        </select>
                      </div>
                      <div className="flex items-center space-x-2">
                        <input type="checkbox" id="preview-checkbox" className="h-4 w-4 rounded border-primary text-primary focus:ring-primary" />
                        <label htmlFor="preview-checkbox" className="text-sm font-medium leading-none">
                          Checkbox option
                        </label>
                      </div>
                    </div>

                    {/* Card */}
                    <div className="rounded-lg border bg-card text-card-foreground shadow-sm overflow-hidden">
                      <div className="p-4 space-y-3">
                        <h3 className="text-lg font-semibold leading-none tracking-tight">Card Title</h3>
                        <p className="text-sm text-muted-foreground">
                          This is a card component that demonstrates how containers will look with the current theme.
                        </p>
                        <div className="flex justify-between items-center pt-2">
                          <span className="inline-flex items-center rounded-full bg-primary/10 px-2.5 py-0.5 text-xs font-medium text-primary">
                            Tag
                          </span>
                          <Button size="sm">Action</Button>
                        </div>
                      </div>
                      <div className="bg-muted/50 p-4 border-t">
                        <p className="text-xs text-muted-foreground">Card footer content</p>
                      </div>
                    </div>

                    {/* Alert */}
                    <div className="rounded-md bg-primary/10 p-4">
                      <div className="flex">
                        <div className="flex-shrink-0">
                          <svg className="h-5 w-5 text-primary" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
                          </svg>
                        </div>
                        <div className="ml-3">
                          <h3 className="text-sm font-medium text-primary">Heads up!</h3>
                          <div className="mt-2 text-sm text-primary">
                            <p>This is an alert component showing how notifications will appear.</p>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 text-sm">
                        <div className="w-3 h-3 rounded-full bg-primary" />
                        <span className="text-foreground">Primary</span>
                        
                        <div className="w-3 h-3 rounded-full bg-accent ml-3" />
                        <span className="text-foreground">Accent</span>
                        
                        <div className="w-3 h-3 rounded-full bg-muted-foreground ml-3" />
                        <span className="text-foreground">Muted</span>
                      </div>
                    </div>

                    {/* Import/Export Section */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 pt-4">
                      <ThemeExporter
                        themeData={{
                          colors: safeColorScheme.colors,
                          background: safeBackground,
                          isDarkMode: isDarkMode,
                          fontFamily: fontFamily,
                          name: safeColorScheme.name
                        }}
                      />
                      <ThemeImporter 
                        onImport={(importedTheme) => {
                          try {
                            // Update color scheme if it exists, or create new one
                            const existingScheme = availableColorSchemes.find(
                              s => s.name.toLowerCase() === importedTheme.name?.toLowerCase()
                            );

                            if (existingScheme) {
                              // Update existing scheme
                              updateSetting('appearance', {
                                color_schemes: availableColorSchemes.map(s => 
                                  s.id === existingScheme.id 
                                    ? { ...s, colors: importedTheme.colors }
                                    : s
                                )
                              });
                              selectColorScheme(existingScheme.id);
                              toast.success(`Updated "${importedTheme.name}" theme`);
                            } else {
                              // Add new scheme
                              const newScheme = {
                                id: importedTheme.name.toLowerCase().replace(/\s+/g, '-'),
                                name: importedTheme.name || 'Imported Theme',
                                colors: importedTheme.colors
                              };
                              
                              addColorScheme(newScheme);
                              selectColorScheme(newScheme.id);
                              toast.success(`Imported "${newScheme.name}" theme`);
                            }

                            // Update background if it exists
                            if (importedTheme.background) {
                              const bgExists = availableBackgrounds.some(b => b.id === importedTheme.background.id);
                              if (bgExists) {
                                selectBackground(importedTheme.background.id);
                              }
                            }

                            // Update dark mode
                            if (typeof importedTheme.isDarkMode === 'boolean') {
                              toggleDarkMode(importedTheme.isDarkMode);
                            }

                            // Update font if it exists
                            if (importedTheme.fontFamily) {
                              selectFont(importedTheme.fontFamily);
                            }

                          } catch (error) {
                            console.error('Error importing theme:', error);
                            toast.error('Failed to import theme. Please check the file format.');
                          }
                        }}
                      />
                    </div>
                  </div>
                </div>
              </Section>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </div>
  );
}
