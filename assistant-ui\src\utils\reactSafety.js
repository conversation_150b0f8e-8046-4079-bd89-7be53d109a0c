import React from 'react';
import ReactDOM from 'react-dom';

/**
 * React Safety Utilities
 *
 * This module provides utilities to prevent React DOM manipulation errors
 * during component unmounting and portal cleanup.
 */

/**
 * Safe component wrapper that prevents DOM manipulation errors during unmounting
 * @param {React.Component} Component - The component to wrap
 * @returns {React.Component} - The wrapped component
 */
export const withSafeUnmount = (Component) => {
  return React.forwardRef((props, ref) => {
    const [isMounted, setIsMounted] = React.useState(true);
    const componentRef = React.useRef(null);

    React.useEffect(() => {
      return () => {
        setIsMounted(false);
        // Give React time to clean up before marking as unmounted
        setTimeout(() => {
          if (componentRef.current) {
            componentRef.current = null;
          }
        }, 0);
      };
    }, []);

    if (!isMounted) {
      return null;
    }

    return <Component ref={ref || componentRef} {...props} />;
  });
};

/**
 * Safe portal wrapper that handles portal cleanup errors
 * @param {React.ReactNode} children - The content to render in the portal
 * @param {Element} container - The container element (optional)
 * @returns {React.ReactNode} - The portal or fallback content
 */
export const SafePortal = ({ children, container = null }) => {
  const [portalError, setPortalError] = React.useState(false);
  const [isClient, setIsClient] = React.useState(false);

  React.useEffect(() => {
    setIsClient(true);
    setPortalError(false);
  }, []);

  if (!isClient || portalError) {
    // Fallback to regular rendering if portal fails or on server
    return children;
  }

  try {
    const targetContainer = container || document.body;
    
    if (!targetContainer || !document.contains(targetContainer)) {
      console.warn('SafePortal: Target container not found or not in DOM, falling back to regular rendering');
      return children;
    }

    return ReactDOM.createPortal(children, targetContainer);
  } catch (error) {
    console.error('SafePortal: Portal creation failed:', error);
    setPortalError(true);
    return children; // Fallback to regular rendering
  }
};

/**
 * Hook to safely manage component lifecycle and prevent DOM errors
 * @returns {Object} - Lifecycle management utilities
 */
export const useSafeLifecycle = () => {
  const [isMounted, setIsMounted] = React.useState(true);
  const cleanupFunctions = React.useRef([]);

  const addCleanup = React.useCallback((cleanupFn) => {
    if (typeof cleanupFn === 'function') {
      cleanupFunctions.current.push(cleanupFn);
    }
  }, []);

  const safeSetState = React.useCallback((setter) => {
    if (isMounted) {
      setter();
    }
  }, [isMounted]);

  React.useEffect(() => {
    return () => {
      setIsMounted(false);
      
      // Run all cleanup functions safely
      cleanupFunctions.current.forEach((cleanup) => {
        try {
          cleanup();
        } catch (error) {
          console.warn('Safe lifecycle cleanup error:', error);
        }
      });
      
      cleanupFunctions.current = [];
    };
  }, []);

  return {
    isMounted,
    addCleanup,
    safeSetState
  };
};

/**
 * Safe event listener hook that prevents memory leaks and DOM errors
 * @param {string} eventType - The event type to listen for
 * @param {Function} handler - The event handler function
 * @param {Element} element - The element to attach the listener to (default: window)
 * @param {Object} options - Event listener options
 */
export const useSafeEventListener = (eventType, handler, element = null, options = {}) => {
  const { isMounted, addCleanup } = useSafeLifecycle();
  const savedHandler = React.useRef();

  // Update ref.current value if handler changes
  React.useEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  React.useEffect(() => {
    if (!isMounted) return;

    const targetElement = element || window;
    
    if (!targetElement || !targetElement.addEventListener) {
      console.warn('useSafeEventListener: Invalid target element');
      return;
    }

    const eventListener = (event) => {
      if (savedHandler.current && isMounted) {
        try {
          savedHandler.current(event);
        } catch (error) {
          console.error('Safe event listener error:', error);
        }
      }
    };

    try {
      targetElement.addEventListener(eventType, eventListener, options);
      
      const cleanup = () => {
        try {
          if (targetElement && targetElement.removeEventListener) {
            targetElement.removeEventListener(eventType, eventListener, options);
          }
        } catch (error) {
          console.warn('Safe event listener cleanup error:', error);
        }
      };

      addCleanup(cleanup);
      
      return cleanup;
    } catch (error) {
      console.error('useSafeEventListener: Failed to add event listener:', error);
    }
  }, [eventType, element, options, isMounted, addCleanup]);
};

/**
 * Safe timeout hook that clears timeouts on unmount
 * @param {Function} callback - The callback function to execute
 * @param {number} delay - The delay in milliseconds
 * @returns {Function} - Function to clear the timeout
 */
export const useSafeTimeout = (callback, delay) => {
  const { isMounted, addCleanup } = useSafeLifecycle();
  const timeoutRef = React.useRef();

  const clearSafeTimeout = React.useCallback(() => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  const setSafeTimeout = React.useCallback(() => {
    clearSafeTimeout();
    
    if (isMounted && typeof callback === 'function' && typeof delay === 'number') {
      timeoutRef.current = setTimeout(() => {
        if (isMounted) {
          try {
            callback();
          } catch (error) {
            console.error('Safe timeout callback error:', error);
          }
        }
        timeoutRef.current = null;
      }, delay);
      
      addCleanup(clearSafeTimeout);
    }
  }, [callback, delay, isMounted, addCleanup, clearSafeTimeout]);

  React.useEffect(() => {
    if (delay !== null) {
      setSafeTimeout();
    }
    
    return clearSafeTimeout;
  }, [setSafeTimeout, clearSafeTimeout, delay]);

  return clearSafeTimeout;
};
