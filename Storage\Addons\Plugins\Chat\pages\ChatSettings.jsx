import React, { useContext, useState } from 'react';
import { ChatContext } from '../contexts/ChatProvider';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { FontAwesomeIcon } from '../components/icons/FontAwesome.jsx';
import {
  faCog,
  faArrowLeft,
  faPalette,
  faVolumeUp,
  faKeyboard,
  faRobot,
  faDatabase,
  faShield,
  faDownload,
  faUpload,
  faTrash,
  faSave,
  faRefresh
} from '../components/icons/FontAwesome.jsx';

const ChatSettings = ({ onClose }) => {
  const { 
    settings,
    updateSettings,
    conversations,
    exportConversation,
    availableModels
  } = useContext(ChatContext);

  const [activeTab, setActiveTab] = useState('general');
  const [localSettings, setLocalSettings] = useState(settings);

  const tabs = [
    { id: 'general', name: 'General', icon: faCog },
    { id: 'appearance', name: 'Appearance', icon: faPalette },
    { id: 'behavior', name: 'Behavior', icon: faRobot },
    { id: 'data', name: 'Data & Privacy', icon: faDatabase },
    { id: 'advanced', name: 'Advanced', icon: faShield }
  ];

  const handleSettingChange = (key, value) => {
    const newSettings = { ...localSettings, [key]: value };
    setLocalSettings(newSettings);
    updateSettings(newSettings);
  };

  const handleSave = () => {
    updateSettings(localSettings);
    onClose();
  };

  const handleReset = () => {
    const defaultSettings = {
      autoWelcome: true,
      enableStreaming: true,
      showModelInfo: true,
      autoScroll: true,
      typingIndicators: true,
      theme: 'system',
      fontSize: 'medium',
      temperature: 0.7,
      maxTokens: 2048,
      systemPrompt: '',
      autoSave: true,
      exportFormat: 'markdown',
      messageAnimations: true,
      soundEnabled: false,
      showTimestamps: true,
      compactMode: false
    };
    setLocalSettings(defaultSettings);
    updateSettings(defaultSettings);
  };

  const renderGeneralSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Chat Behavior
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Auto-welcome message
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Show a welcome message when starting new conversations
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.autoWelcome}
              onChange={(e) => handleSettingChange('autoWelcome', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Enable streaming responses
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Show responses as they are generated in real-time
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.enableStreaming}
              onChange={(e) => handleSettingChange('enableStreaming', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show model information
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Display current model and conversation stats in header
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.showModelInfo}
              onChange={(e) => handleSettingChange('showModelInfo', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Auto-scroll to new messages
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Automatically scroll to the bottom when new messages arrive
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.autoScroll}
              onChange={(e) => handleSettingChange('autoScroll', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show typing indicators
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Display animated indicators when AI is generating responses
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.typingIndicators}
              onChange={(e) => handleSettingChange('typingIndicators', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Default AI Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Temperature: {localSettings.temperature}
            </label>
            <input
              type="range"
              min="0"
              max="2"
              step="0.1"
              value={localSettings.temperature}
              onChange={(e) => handleSettingChange('temperature', parseFloat(e.target.value))}
              className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer dark:bg-gray-700"
            />
            <div className="flex justify-between text-xs text-gray-500 mt-1">
              <span>Focused</span>
              <span>Balanced</span>
              <span>Creative</span>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Max Tokens
            </label>
            <Input
              type="number"
              min="1"
              max="8192"
              value={localSettings.maxTokens}
              onChange={(e) => handleSettingChange('maxTokens', parseInt(e.target.value))}
              className="w-full"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Default System Prompt
            </label>
            <textarea
              value={localSettings.systemPrompt}
              onChange={(e) => handleSettingChange('systemPrompt', e.target.value)}
              placeholder="Enter a default system prompt for new conversations..."
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 resize-none"
              rows={4}
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderAppearanceSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Visual Settings
        </h3>
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Font Size
            </label>
            <select
              value={localSettings.fontSize}
              onChange={(e) => handleSettingChange('fontSize', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="small">Small</option>
              <option value="medium">Medium</option>
              <option value="large">Large</option>
              <option value="extra-large">Extra Large</option>
            </select>
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Message animations
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Enable smooth animations for message appearance
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.messageAnimations}
              onChange={(e) => handleSettingChange('messageAnimations', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Show timestamps
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Display timestamps on messages
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.showTimestamps}
              onChange={(e) => handleSettingChange('showTimestamps', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Compact mode
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Reduce spacing between messages
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.compactMode}
              onChange={(e) => handleSettingChange('compactMode', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>
        </div>
      </div>
    </div>
  );

  const renderBehaviorSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Audio & Notifications
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Sound notifications
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Play sounds for new messages and events
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.soundEnabled}
              onChange={(e) => handleSettingChange('soundEnabled', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Data Management
        </h3>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <label className="text-sm font-medium text-gray-700 dark:text-gray-300">
                Auto-save conversations
              </label>
              <p className="text-xs text-gray-500 dark:text-gray-400">
                Automatically save conversations as you chat
              </p>
            </div>
            <input
              type="checkbox"
              checked={localSettings.autoSave}
              onChange={(e) => handleSettingChange('autoSave', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Default Export Format
            </label>
            <select
              value={localSettings.exportFormat}
              onChange={(e) => handleSettingChange('exportFormat', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
            >
              <option value="markdown">Markdown</option>
              <option value="json">JSON</option>
              <option value="txt">Plain Text</option>
            </select>
          </div>
        </div>
      </div>
    </div>
  );

  const renderDataSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Data Export & Import
        </h3>
        <div className="space-y-4">
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
            <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
              Export All Conversations
            </h4>
            <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
              Download all your conversations in your preferred format
            </p>
            <div className="flex gap-2">
              <Button
                onClick={() => exportConversation('markdown')}
                size="sm"
                variant="outline"
              >
                <FontAwesomeIcon icon={faDownload} className="h-4 w-4 mr-2" />
                Markdown
              </Button>
              <Button
                onClick={() => exportConversation('json')}
                size="sm"
                variant="outline"
              >
                <FontAwesomeIcon icon={faDownload} className="h-4 w-4 mr-2" />
                JSON
              </Button>
            </div>
          </div>

          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              Storage Information
            </h4>
            <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
              <p>Total conversations: {conversations?.length || 0}</p>
              <p>Available models: {availableModels?.length || 0}</p>
              <p>Storage location: Local database</p>
            </div>
          </div>
        </div>
      </div>

      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Privacy & Security
        </h3>
        <div className="space-y-4">
          <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
            <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-2">
              Data Retention
            </h4>
            <p className="text-sm text-yellow-700 dark:text-yellow-300 mb-3">
              All conversations are stored locally on your device. No data is sent to external servers except for AI model interactions.
            </p>
          </div>
        </div>
      </div>
    </div>
  );

  const renderAdvancedSettings = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
          Advanced Options
        </h3>
        <div className="space-y-4">
          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
              Reset All Settings
            </h4>
            <p className="text-sm text-red-700 dark:text-red-300 mb-3">
              This will reset all chat settings to their default values. This action cannot be undone.
            </p>
            <Button
              onClick={handleReset}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <FontAwesomeIcon icon={faRefresh} className="h-4 w-4 mr-2" />
              Reset Settings
            </Button>
          </div>

          <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <h4 className="font-medium text-red-900 dark:text-red-100 mb-2">
              Clear All Data
            </h4>
            <p className="text-sm text-red-700 dark:text-red-300 mb-3">
              This will permanently delete all conversations and chat data. This action cannot be undone.
            </p>
            <Button
              onClick={() => {
                if (window.confirm('Are you sure you want to delete all conversations? This action cannot be undone.')) {
                  // TODO: Implement clear all data
                  console.log('Clear all data');
                }
              }}
              variant="outline"
              size="sm"
              className="text-red-600 border-red-300 hover:bg-red-50"
            >
              <FontAwesomeIcon icon={faTrash} className="h-4 w-4 mr-2" />
              Clear All Data
            </Button>
          </div>
        </div>
      </div>
    </div>
  );

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general':
        return renderGeneralSettings();
      case 'appearance':
        return renderAppearanceSettings();
      case 'behavior':
        return renderBehaviorSettings();
      case 'data':
        return renderDataSettings();
      case 'advanced':
        return renderAdvancedSettings();
      default:
        return renderGeneralSettings();
    }
  };

  return (
    <div className="h-full flex flex-col bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="h-8 w-8 p-0"
            >
              <FontAwesomeIcon icon={faArrowLeft} className="h-4 w-4" />
            </Button>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              Chat Settings
            </h1>
          </div>
          <Button onClick={handleSave} className="bg-blue-600 hover:bg-blue-700">
            <FontAwesomeIcon icon={faSave} className="h-4 w-4 mr-2" />
            Save Changes
          </Button>
        </div>
      </div>

      <div className="flex-1 flex">
        {/* Sidebar */}
        <div className="w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700">
          <nav className="p-4 space-y-2">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                  activeTab === tab.id
                    ? 'bg-blue-50 dark:bg-blue-900/20 text-blue-600 dark:text-blue-400'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
                }`}
              >
                <FontAwesomeIcon icon={tab.icon} className="h-4 w-4" />
                <span className="font-medium">{tab.name}</span>
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto">
          <div className="p-6 max-w-2xl">
            {renderTabContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ChatSettings;