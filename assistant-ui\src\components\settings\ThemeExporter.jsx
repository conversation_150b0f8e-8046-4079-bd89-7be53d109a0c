import { Button } from "@/components/ui/button";
import { Download } from "lucide-react";

export function ThemeExporter({ themeData }) {
  const exportTheme = () => {
    const dataStr = JSON.stringify(themeData, null, 2);
    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(dataStr)}`;
    
    const exportName = `theme-${new Date().toISOString().split('T')[0]}.json`;
    
    const linkElement = document.createElement('a');
    linkElement.setAttribute('href', dataUri);
    linkElement.setAttribute('download', exportName);
    linkElement.click();
  };

  return (
    <div className="space-y-4">
      <h3 className="font-medium">Export Theme</h3>
      <p className="text-sm text-muted-foreground">
        Export your current theme settings as a JSON file to share or back up.
      </p>
      <Button 
        onClick={exportTheme}
        variant="outline"
        className="w-full sm:w-auto"
      >
        <Download className="mr-2 h-4 w-4" />
        Export Theme
      </Button>
    </div>
  );
}
