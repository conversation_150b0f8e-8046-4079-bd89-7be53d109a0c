import React, { useState, useEffect, useRef, useCallback } from 'react';
import { BrowserRouter as Router, Routes, Route, useNavigate, useLocation, Navigate } from 'react-router-dom';
import { listen } from '@tauri-apps/api/event';
import { invoke } from '@tauri-apps/api/core';
import './App.css';
import Dashboard from './pages/Dashboard.jsx';
import Settings from './pages/Settings.jsx';
import Tools from './pages/Tools.jsx';
import { MainLayout } from './components/MainLayout.jsx';
import { SettingsProvider, useSettings } from './contexts/SettingsContext.jsx';
import { ThemeProvider } from './contexts/ThemeContext.jsx';
import { NotificationProvider } from './contexts/NotificationContext.jsx';
import { UIStateProvider } from './contexts/UIStateContext.jsx';
import PortalProvider from './contexts/PortalContext.jsx';
import ToastContainer from './components/Toast.jsx';
import PluginLoader from './components/PluginLoader.jsx';
import ErrorBoundary from './components/ErrorBoundary.jsx';
import DOMErrorBoundary from './components/DOMErrorBoundary.jsx';
import ErrorPopup from './components/ErrorPopup.jsx';
import ErrorIndicator from './components/ErrorIndicator.jsx';
import { useIsMounted } from './hooks/useIsMounted.js';
import { faHome, faComments, faTools, faCog } from '@fortawesome/free-solid-svg-icons';

// Add missing type definitions for better type checking
/**
 * @typedef {Object} Tab
 * @property {string} id - Unique identifier for the tab
 * @property {string} name - Display name of the tab
 * @property {import('@fortawesome/fontawesome-svg-core').IconDefinition} icon - Icon for the tab
 * @property {string} path - Route path for the tab
 * @property {React.ComponentType} component - React component to render for this tab
 */

// Define initialization steps with default states
const INIT_STEPS = [
  { id: 'start', label: 'Starting Application', status: 'pending' },
  { id: 'servers', label: 'Initializing Server Connections', status: 'pending' },
  { id: 'models', label: 'Loading AI Models', status: 'pending' },
  { id: 'plugins', label: 'Loading Plugins', status: 'pending' },
  { id: 'chat', label: 'Preparing Chat System', status: 'pending' },
  { id: 'complete', label: 'Ready', status: 'pending' },
];

const LoadingScreen = ({ message = 'Initializing...', currentStep = 0, error = null }) => {
  if (error) {
    return (
      <ErrorPopup
        title="Initialization Error"
        message="An error occurred while initializing the application."
        error={error}
        onReload={() => window.location.reload()}
      />
    );
  }

  return (
    <div className="flex items-center justify-center w-full h-full bg-background text-foreground p-6">
      <div className="w-full max-w-2xl">
        <div className="text-center mb-8">
          <h1 className="text-3xl font-bold text-primary mb-2">The Collective</h1>
          <p className="text-muted-foreground">Initializing your workspace</p>
        </div>
        
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary mb-4"></div>
          <p className="text-muted-foreground">Please wait...</p>
        </div>
      </div>
    </div>
  );
};

// Core app tabs that should always be present
const CORE_TABS = [
  { id: 'dashboard', name: 'Dashboard', icon: faHome, path: '/', component: Dashboard },
  { id: 'chat', name: 'Chat', icon: faComments, path: '/chat', component: (props) => <PluginLoader pluginName="Chat" {...props} /> },
  { id: 'tools', name: 'Tools', icon: faTools, path: '/tools', component: (props) => <Tools {...props} /> },
  { id: 'settings', name: 'Settings', icon: faCog, path: '/settings', component: Settings },
];

const AppContent = () => {
  console.log('AppContent: Component rendered.');
  // All hooks at the top level with no conditions
  const { isLoading: isSettingsLoading, error: settingsError } = useSettings();
  const location = useLocation();
  const navigate = useNavigate();
  const isMounted = useIsMounted();

  // State hooks
  const [isBackendReady, setIsBackendReady] = useState(false);
  const [dynamicTabs, setDynamicTabs] = useState([]);
  const [error, setError] = useState(null);
  const [isLoading, setIsLoading] = useState(true); // Added for clarity

  console.log('AppContent State: isLoading', isLoading, 'isBackendReady', isBackendReady, 'dynamicTabs', dynamicTabs.length, 'error', error);

  // Refs
  const tabsRef = useRef([...CORE_TABS]);
  const initialRender = useRef(true);

  // Keep tabsRef updated with both static and dynamic tabs
  useEffect(() => {
    console.log('AppContent: tabsRef useEffect triggered. dynamicTabs changed:', dynamicTabs);
    tabsRef.current = [...CORE_TABS, ...dynamicTabs];
  }, [dynamicTabs]);

  // Handle global errors and backend initialization
  useEffect(() => {
    console.log('AppContent: Global error and backend initialization useEffect triggered. isMounted:', isMounted());
    console.log('App.jsx useEffect triggered.');

    const checkBackendReadyAndSetupListener = async () => {
      console.log('checkBackendReadyAndSetupListener: Starting...');
      try {
        const ready = await invoke('is_backend_ready');
        if (ready && isMounted()) {
          console.log('AppContent: Backend was already ready. Setting states.');
          setIsBackendReady(true);
          setIsLoading(false); // If backend is ready, stop loading
          return; // Exit if backend is already ready
        }
      } catch (error) {
        console.error('AppContent: Error checking backend readiness:', error);
        setError(error);
        setIsLoading(false);
        return;
      }

      if (window.__TAURI__) {
        console.log('App.jsx: window.__TAURI__ is available. Setting up listener.');

        let unlistenFn;

        const setupListener = async () => {
          unlistenFn = await listen('initialization_complete', (event) => {
            console.log('✅ App.jsx: Received initialization_complete event', event.payload);
            setIsLoading(false);
            setIsBackendReady(true); // Set backend ready on event reception
          });
          console.log('App.jsx: Initialization_complete listener set up.');
        };

        setupListener();

        return () => {
          if (unlistenFn) {
            console.log('App.jsx: Cleaning up initialization_complete listener.');
            unlistenFn();
          }
        };

      } else {
        console.log('App.jsx: window.__TAURI__ is NOT available. Cannot set up listener.');
        // If Tauri is not available, assume backend is not ready and stay in loading state or show error
        // For development in browser, you might want to set isLoading(false) and isBackendReady(true) here
        // to bypass Tauri initialization.
        // For now, we'll keep it in a loading state until a proper mock is implemented.
        // setIsLoading(false);
        // setIsBackendReady(true);
      }
    };

    checkBackendReadyAndSetupListener();

    const handleError = (err) => {
      console.error('AppContent: Global error caught:', err);
      if (isMounted()) {
        setError(err);
      }
    };

    const handleWindowError = (e) => handleError(e.error);
    const handleRejection = (e) => handleError(e.reason);

    window.addEventListener('error', handleWindowError);
    window.addEventListener('unhandledrejection', handleRejection);

    return () => {
      console.log('AppContent: Cleaning up global error listeners.');
      window.removeEventListener('error', handleWindowError);
      window.removeEventListener('unhandledrejection', handleRejection);
    };
  }, [isMounted]);

  // Show loading state
  const renderContent = () => {
    console.log('renderContent: isLoading', isLoading, 'isBackendReady', isBackendReady);
    if (isLoading || !isBackendReady) { // Use isLoading state
      console.log('AppContent: Rendering LoadingScreen. isLoading:', isLoading, 'isBackendReady:', isBackendReady);
      return <LoadingScreen message="Initializing application..." />;
    }
    
    if (error || settingsError) {
      return (
        <ErrorBoundary>
          <ErrorPopup
            title="Application Error"
            message={error?.message || 'An error occurred'}
            error={error || settingsError}
            onReload={() => window.location.reload()}
          />
        </ErrorBoundary>
      );
    }
    
    // Combine core tabs and dynamic tabs for rendering
    const allTabs = [...CORE_TABS, ...dynamicTabs];
    
    return (
      <MainLayout tabs={allTabs} addTab={addTab} closeTab={closeTab}>
        <Routes>
          {allTabs.map((tab) => (
            <Route
              key={tab.path}
              path={tab.path}
              element={
                <ErrorBoundary>
                  <tab.component addTab={addTab} closeTab={closeTab} />
                </ErrorBoundary>
              }
            />
          ))}
          <Route path="*" element={<Navigate to="/" replace />} />
        </Routes>
      </MainLayout>
    );
  };
  
  const addTab = useCallback((app) => {
    console.log('addTab called with app:', app);
    try {
      // Skip if already exists in dynamic tabs
      const tabExists = dynamicTabs.some(tab => tab.id === app.id);
      if (tabExists) {
        const existingTab = dynamicTabs.find(tab => tab.id === app.id);
        if (existingTab) {
          navigate(existingTab.path);
        }
        return;
      }

      // Create a new tab with a unique ID
      const tabId = `${app.id}-${Date.now()}`;
      const newTab = {
        id: tabId,
        name: app.name,
        icon: app.icon,
        path: `/${tabId}`,
        component: () => <PluginLoader pluginName={app.component || app.name} />,
      };

      // Add to dynamic tabs
      setDynamicTabs(prevTabs => [...prevTabs, newTab]);
      
      // Navigate to the new tab
      requestAnimationFrame(() => {
        if (isMounted()) {
          navigate(newTab.path);
        }
      });
    } catch (error) {
      console.error('Error adding tab:', error);
      setError(error);
    }
  }, [dynamicTabs, isMounted, navigate]);

  const closeTab = useCallback((tabId) => {
    console.log('closeTab called with:', { tabId, currentPath: location.pathname });
    
    // Defensive check for valid tabId
    if (!tabId) return;
    
    // Don't allow closing core tabs
    if (CORE_TABS.some(tab => tab.id === tabId)) {
      return;
    }
    
    setDynamicTabs(prevTabs => {
      const tabToClose = prevTabs.find(tab => tab.id === tabId);
      if (!tabToClose) return prevTabs;
      
      const updatedTabs = prevTabs.filter(tab => tab.id !== tabId);
      const isActiveTab = location.pathname === tabToClose.path;
      
      // If we're closing the active tab, navigate to another tab
      if (isActiveTab) {
        const activeTabIndex = prevTabs.findIndex(tab => tab.id === tabId);
        const newActiveTab = updatedTabs[Math.min(activeTabIndex, updatedTabs.length - 1)];
        
        requestAnimationFrame(() => {
          if (isMounted()) {
            if (newActiveTab) {
              navigate(newActiveTab.path);
            } else {
              // If no dynamic tabs left, go to dashboard
              navigate('/');
            }
          }
        });
      }
      
      return updatedTabs;
    });
  }, [location.pathname, navigate, isMounted]);

  if (initialRender.current) {
    initialRender.current = false;
  }
  
  // Wrap the content in a div to ensure stable DOM structure
  return (
    <div className="app-content">
      {renderContent()}
      <ErrorIndicator maxErrors={10} autoHideTimeout={5000} />
      <ToastContainer />
    </div>
  );
};

const App = () => (
  <DOMErrorBoundary>
    <ErrorBoundary>
      <PortalProvider>
        <NotificationProvider>
          <UIStateProvider>
            <SettingsProvider>
              <ThemeProvider>
                <Router>
                  <AppContent />
                </Router>
              </ThemeProvider>
            </SettingsProvider>
          </UIStateProvider>
        </NotificationProvider>
      </PortalProvider>
    </ErrorBoundary>
  </DOMErrorBoundary>
);

export default App;
