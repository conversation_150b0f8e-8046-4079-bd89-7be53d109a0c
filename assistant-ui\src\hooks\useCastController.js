import { useState, useCallback, useRef, useEffect } from 'react';
import { safeInvoke } from '../utils/tauriHelpers';

// Mock device discovery service that can be replaced with real implementation
const deviceDiscoveryService = {
  discoverDevices: async () => {
    try {
      // Try to use <PERSON><PERSON> backend first
      if (window.__TAURI__) {
        const devices = await safeInvoke('discover_devices');
        if (devices && Array.isArray(devices)) {
          return devices;
        }
      }
      
      // Fallback to mock devices
      return [
        {
          id: 'local-display',
          name: 'Local Display',
          type: 'display',
          isLocal: true,
          icon: 'tv',
        },
        {
          id: 'chromecast-1',
          name: 'Living Room TV',
          type: 'chromecast',
          isLocal: false,
          icon: 'tv',
        },
      ];
    } catch (error) {
      console.error('Device discovery failed:', error);
      throw new Error('Failed to discover devices');
    }
  },
  
  startCasting: async (deviceId, content) => {
    console.log(`Starting cast to device ${deviceId} with content:`, content);
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    return { sessionId: `session-${Date.now()}` };
  },
  
  stopCasting: async (sessionId) => {
    console.log(`Stopping cast session ${sessionId}`);
    await new Promise(resolve => setTimeout(resolve, 500));
    return { success: true };
  }
};

export const useCastController = () => {
  const [devices, setDevices] = useState([]);
  const [selectedDevice, setSelectedDevice] = useState(null);
  const [activeSession, setActiveSession] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isCasting, setIsCasting] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const isMounted = useRef(true);
  const refreshInterval = useRef(null);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
      if (refreshInterval.current) {
        clearInterval(refreshInterval.current);
      }
    };
  }, []);

  // Discover available devices
  const discoverDevices = useCallback(async () => {
    if (!isMounted.current) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      const discoveredDevices = await deviceDiscoveryService.discoverDevices();
      
      if (!isMounted.current) return;
      
      setDevices(discoveredDevices);
      
      // If we have a selected device, make sure it's in the updated list
      if (selectedDevice) {
        const deviceStillExists = discoveredDevices.some(d => d.id === selectedDevice.id);
        if (!deviceStillExists) {
          setSelectedDevice(null);
          setActiveSession(null);
        }
      }
    } catch (err) {
      console.error('Error discovering devices:', err);
      if (isMounted.current) {
        setError('Failed to discover devices. Please check your network connection.');
      }
    } finally {
      if (isMounted.current) {
        setIsLoading(false);
      }
    }
  }, [selectedDevice]);

  // Start casting to a device
  const startCasting = useCallback(async (device) => {
    if (!device || !isMounted.current) return null;
    
    setIsCasting(true);
    setError(null);
    setSelectedDevice(device);

    try {
      const contentToCast = {
        type: 'tab',
        url: window.location.href,
        title: document.title
      };
      
      const result = await deviceDiscoveryService.startCasting(device.id, contentToCast);
      
      if (!isMounted.current) return null;
      
      setActiveSession({ id: result.sessionId, device });
      return result;
    } catch (err) {
      console.error('Error starting cast:', err);
      if (isMounted.current) {
        setError(`Failed to start casting: ${err.message}`);
      }
      throw err;
    } finally {
      if (isMounted.current) {
        setIsCasting(false);
      }
    }
  }, []);

  // Stop the current casting session
  const stopCasting = useCallback(async () => {
    if (!activeSession || !isMounted.current) return;
    
    try {
      await deviceDiscoveryService.stopCasting(activeSession.id);
      
      if (isMounted.current) {
        setActiveSession(null);
        setSelectedDevice(null);
      }
    } catch (err) {
      console.error('Error stopping cast:', err);
      if (isMounted.current) {
        setError(`Failed to stop casting: ${err.message}`);
      }
      throw err;
    }
  }, [activeSession]);

  // Open the cast modal and start device discovery
  const openCastModal = useCallback(() => {
    setIsModalOpen(true);
    discoverDevices();
    
    // Refresh devices periodically while modal is open
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
    }
    
    refreshInterval.current = setInterval(discoverDevices, 10000);
  }, [discoverDevices]);

  // Close the cast modal and clean up
  const closeCastModal = useCallback(() => {
    setIsModalOpen(false);
    setError(null);
    
    if (refreshInterval.current) {
      clearInterval(refreshInterval.current);
      refreshInterval.current = null;
    }
    
    // Don't reset selected device if we have an active session
    if (!activeSession) {
      setSelectedDevice(null);
    }
  }, [activeSession]);

  return {
    // State
    devices,
    selectedDevice,
    activeSession,
    isLoading,
    error,
    isCasting,
    isModalOpen,
    
    // Actions
    openCastModal,
    closeCastModal,
    startCasting,
    stopCasting,
    refreshDevices: discoverDevices,
    setSelectedDevice,
  };
};

export default useCastController;
