import { useState, useCallback, useEffect } from 'react';

export const useErrorBoundary = (options = {}) => {
  const [error, setError] = useState(null);
  const { onError, resetOnChange = [] } = options;

  const handleError = useCallback((error, errorInfo) => {
    console.error('Error Boundary caught an error:', error, errorInfo);
    setError(error);
    
    if (typeof onError === 'function') {
      onError(error, errorInfo);
    }
  }, [onError]);

  const resetErrorBoundary = useCallback(() => {
    setError(null);
  }, []);

  // Reset error when dependencies change
  useEffect(() => {
    if (error) {
      resetErrorBoundary();
    }
  }, [resetOnChange]); // eslint-disable-line react-hooks/exhaustive-deps

  return {
    error,
    handleError,
    resetErrorBoundary,
    ErrorBoundary: useCallback(({ children, fallback }) => {
      if (error) {
        return typeof fallback === 'function' ? fallback({ error, reset: resetErrorBoundary }) : fallback;
      }
      return children;
    }, [error, resetErrorBoundary])
  };
};

export default useErrorBoundary;
