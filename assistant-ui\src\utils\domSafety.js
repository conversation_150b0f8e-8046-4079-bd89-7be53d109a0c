/**
 * DOM Safety Utilities
 * 
 * This module provides safe DOM manipulation utilities to prevent
 * "NotFoundError: Failed to execute 'removeChild' on 'Node'" errors
 * and other DOM-related issues.
 */

/**
 * Safely removes a child element from its parent
 * @param {Element} child - The child element to remove
 * @param {Element} parent - The parent element (optional, will use child.parentNode if not provided)
 * @returns {boolean} - True if removal was successful, false otherwise
 */
export const safeRemoveChild = (child, parent = null) => {
  try {
    if (!child) {
      console.warn('safeRemoveChild: child element is null or undefined');
      return false;
    }

    const parentElement = parent || child.parentNode;
    
    if (!parentElement) {
      console.warn('safeRemoveChild: parent element not found');
      return false;
    }

    // Verify the child is actually a child of the parent
    if (parentElement.contains(child)) {
      parentElement.removeChild(child);
      return true;
    } else {
      console.warn('safeRemoveChild: child is not a child of the specified parent');
      return false;
    }
  } catch (error) {
    console.error('safeRemoveChild: Error removing child element:', error);
    return false;
  }
};

/**
 * Safely appends a child element to a parent
 * @param {Element} parent - The parent element
 * @param {Element} child - The child element to append
 * @returns {boolean} - True if append was successful, false otherwise
 */
export const safeAppendChild = (parent, child) => {
  try {
    if (!parent || !child) {
      console.warn('safeAppendChild: parent or child element is null or undefined');
      return false;
    }

    // Check if parent exists in the DOM
    if (!document.contains(parent)) {
      console.warn('safeAppendChild: parent element is not in the DOM');
      return false;
    }

    parent.appendChild(child);
    return true;
  } catch (error) {
    console.error('safeAppendChild: Error appending child element:', error);
    return false;
  }
};

/**
 * Safely creates and manages a temporary DOM element for clipboard operations
 * @param {string} content - The content to copy
 * @returns {Promise<boolean>} - True if copy was successful, false otherwise
 */
export const safeCopyToClipboard = async (content) => {
  // Try modern clipboard API first
  try {
    await navigator.clipboard.writeText(content);
    return true;
  } catch (clipboardError) {
    console.warn('Modern clipboard API failed, trying fallback method:', clipboardError);
  }

  // Fallback to legacy method with safe DOM manipulation
  try {
    const textArea = document.createElement('textarea');
    textArea.value = content;
    textArea.style.position = 'fixed';
    textArea.style.left = '-999999px';
    textArea.style.top = '-999999px';
    textArea.style.opacity = '0';
    textArea.setAttribute('readonly', '');

    // Safely append to body
    if (!safeAppendChild(document.body, textArea)) {
      return false;
    }

    // Focus and select
    textArea.focus();
    textArea.select();
    textArea.setSelectionRange(0, 99999); // For mobile devices

    // Execute copy command
    const successful = document.execCommand('copy');

    // Safely remove the element
    safeRemoveChild(textArea, document.body);

    return successful;
  } catch (fallbackError) {
    console.error('Fallback clipboard method also failed:', fallbackError);
    return false;
  }
};

/**
 * Safely creates and injects a style element
 * @param {string} css - The CSS content
 * @param {string} id - The ID for the style element
 * @returns {boolean} - True if injection was successful, false otherwise
 */
export const safeInjectCSS = (css, id) => {
  try {
    if (!css || !id) {
      console.warn('safeInjectCSS: css or id parameter is missing');
      return false;
    }

    // Check if element already exists
    let styleElement = document.getElementById(id);
    
    if (!styleElement) {
      styleElement = document.createElement('style');
      styleElement.id = id;
      styleElement.type = 'text/css';
      
      // Safely append to head
      if (!document.head) {
        console.error('safeInjectCSS: document.head is not available');
        return false;
      }
      
      if (!safeAppendChild(document.head, styleElement)) {
        return false;
      }
    }

    // Only update if element still exists and is attached
    if (styleElement && styleElement.parentNode) {
      styleElement.textContent = css;
      return true;
    }

    return false;
  } catch (error) {
    console.error('safeInjectCSS: Error injecting CSS:', error);
    return false;
  }
};

/**
 * Safely removes a style element by ID
 * @param {string} id - The ID of the style element to remove
 * @returns {boolean} - True if removal was successful, false otherwise
 */
export const safeRemoveCSS = (id) => {
  try {
    const styleElement = document.getElementById(id);
    if (styleElement) {
      return safeRemoveChild(styleElement);
    }
    return true; // Consider it successful if element doesn't exist
  } catch (error) {
    console.error('safeRemoveCSS: Error removing CSS:', error);
    return false;
  }
};
