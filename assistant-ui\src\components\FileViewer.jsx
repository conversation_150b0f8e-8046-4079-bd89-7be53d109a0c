import React, { useState, useEffect } from 'react';
import { safeInvoke, isTauri } from '../utils/tauriHelpers';

const FileViewer = ({ filePath }) => {
  const [content, setContent] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    const fetchFileContent = async () => {
      if (!filePath) {
        setContent('');
        setError('No file path provided.');
        return;
      }

      try {
        setLoading(true);
        setError('');
        
        if (!(await isTauri())) {
          setError('File viewing requires desktop (Tauri) environment.');
          setContent('');
          return;
        }
        
        // Use the correct Tauri command name
        const fileContent = await safeInvoke('get_file_content', {
          file_path: filePath,
        });
        setContent(fileContent);
      } catch (err) {
        console.error('Error reading file content:', err);
        setError(`Failed to read file: ${err.message || err}`);
        setContent('');
      } finally {
        setLoading(false);
      }
    };

    fetchFileContent();
  }, [filePath]);

  // Determine if content is likely binary (not text)
  const isBinaryContent = (content) => {
    // Simple heuristic: if content contains null bytes, it's likely binary
    return content.includes('\u0000');
  };

  // Format file size for display
  const formatFileSize = (str) => {
    if (!str) return '0 B';
    const size = str.length;
    if (size < 1024) return `${size} B`;
    if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)} KB`;
    if (size < 1024 * 1024 * 1024) return `${(size / (1024 * 1024)).toFixed(1)} MB`;
    return `${(size / (1024 * 1024 * 1024)).toFixed(1)} GB`;
  };

  return (
    <div className="file-viewer h-full flex flex-col">
      {loading && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-slate-600 dark:text-slate-400">Loading file content...</p>
          </div>
        </div>
      )}
      
      {error && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-md m-2">
          <div className="flex items-center">
            <div className="ml-2">
              <h3 className="text-sm font-medium text-red-800">File Error</h3>
              <p className="text-xs text-red-700 mt-1">{error}</p>
            </div>
          </div>
        </div>
      )}
      
      {!loading && !error && content && (
        <div className="flex-1 flex flex-col h-full">
          <div className="bg-white dark:bg-slate-800 border-b border-slate-200 dark:border-slate-700 p-2 flex items-center justify-between">
            <div className="text-sm text-slate-600 dark:text-slate-400">
              {filePath && (
                <span className="font-mono text-xs">{filePath.split(/[\\/]/).pop()}</span>
              )}
            </div>
            <div className="text-xs text-slate-500">
              {formatFileSize(content)}
            </div>
          </div>
          <div className="flex-1 overflow-auto p-4">
            {isBinaryContent(content) ? (
              <div className="text-center py-12">
                <div className="text-slate-400 mb-4">
                  <svg className="h-16 w-16 mx-auto" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-slate-900 dark:text-slate-100 mb-2">Binary File</h3>
                <p className="text-slate-600 dark:text-slate-400 mb-4">
                  This file contains binary data and cannot be displayed as text.
                </p>
                <div className="text-sm text-slate-500">
                  File size: {formatFileSize(content)}
                </div>
              </div>
            ) : (
              <pre className="file-content bg-slate-50 dark:bg-slate-900/50 p-4 rounded-lg overflow-auto text-sm">
                <code className="font-mono">{content}</code>
              </pre>
            )}
          </div>
        </div>
      )}
      
      {!loading && !error && !content && !filePath && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center text-slate-500">
            <svg className="h-16 w-16 mx-auto mb-4 text-slate-300" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
            <p>Select a file to view its content.</p>
          </div>
        </div>
      )}
      
      {!loading && !error && !content && filePath && (
        <div className="flex items-center justify-center h-full">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className="text-slate-600 dark:text-slate-400">Loading file content...</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default FileViewer;