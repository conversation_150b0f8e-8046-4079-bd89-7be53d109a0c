import React from 'react';
import { useTheme } from '../../contexts/ThemeContext';
import { Card, CardHeader, CardTitle, CardContent } from '../ui/card';
import { Switch } from '../ui/switch';
import { Label } from '../ui/label';
import { Button } from '../ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, TabsContent } from '../ui/tabs';

const ColorSchemePreview = ({ scheme, isSelected, onClick }) => (
  <div 
    className={`relative rounded-lg overflow-hidden border-2 cursor-pointer transition-all duration-200 ${
      isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : 'border-border hover:border-primary/50'
    }`}
    onClick={onClick}
  >
    <div className="h-20 flex">
      <div className="w-1/5 h-full" style={{ backgroundColor: scheme.colors.background }} />
      <div className="w-1/5 h-full" style={{ backgroundColor: scheme.colors.surface }} />
      <div className="w-1/5 h-full" style={{ backgroundColor: scheme.colors.primary }} />
      <div className="w-1/5 h-full" style={{ backgroundColor: scheme.colors.accent }} />
      <div className="w-1/5 h-full" style={{ backgroundColor: scheme.colors.text }} />
    </div>
    <div className="p-3 bg-card">
      <div className="text-sm font-medium text-foreground">{scheme.name}</div>
      <div className="text-xs text-muted-foreground">
        {Object.values(scheme.colors).map((color, i) => (
          <span key={i} className="inline-block w-3 h-3 rounded-full mr-1" style={{ backgroundColor: color }} />
        ))}
      </div>
    </div>
  </div>
);

const BackgroundOption = ({ option, isSelected, onClick }) => (
  <div 
    className={`relative rounded-lg overflow-hidden border-2 cursor-pointer transition-all duration-200 ${
      isSelected ? 'ring-2 ring-primary ring-offset-2 ring-offset-background' : 'border-border hover:border-primary/50'
    }`}
    onClick={onClick}
  >
    <div className={`h-24 rounded-md ${option.value}`} />
    <div className="p-2 text-center">
      <div className="text-sm font-medium text-foreground">{option.name}</div>
    </div>
  </div>
);

const FontOption = ({ font, isSelected, onClick }) => (
  <div 
    className={`p-4 border rounded-lg cursor-pointer transition-colors ${
      isSelected 
        ? 'border-primary bg-primary/10' 
        : 'border-border hover:border-primary/50 hover:bg-accent/20'
    }`}
    onClick={onClick}
    style={{ fontFamily: font.id === 'sans' ? 'Inter, sans-serif' : font.id === 'serif' ? 'Merriweather, serif' : font.id === 'mono' ? 'JetBrains Mono, monospace' : 'Plus Jakarta Sans, sans-serif' }}
  >
    <div className="font-medium">{font.name}</div>
    <div className="text-sm text-muted-foreground">Aa Bb Cc Xx Yy Zz</div>
  </div>
);

export default function AppearanceSettings() {
  const {
    isDarkMode,
    toggleDarkMode,
    colorScheme,
    selectColorScheme,
    background,
    selectBackground,
    fontFamily,
    selectFont,
    availableColorSchemes,
    availableBackgrounds,
    availableFonts,
  } = useTheme();

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Theme</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-medium">Dark Mode</h3>
              <p className="text-sm text-muted-foreground">
                {isDarkMode ? 'Dark theme is enabled' : 'Light theme is enabled'}
              </p>
            </div>
            <Switch
              checked={isDarkMode}
              onCheckedChange={toggleDarkMode}
              className="data-[state=checked]:bg-primary"
            />
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Color Scheme</CardTitle>
          <p className="text-sm text-muted-foreground">
            Choose a color scheme that matches your style
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {availableColorSchemes.map((scheme) => (
              <ColorSchemePreview
                key={scheme.id}
                scheme={scheme}
                isSelected={colorScheme.id === scheme.id}
                onClick={() => selectColorScheme(scheme.id)}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Background</CardTitle>
          <p className="text-sm text-muted-foreground">
            Customize your background style
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {availableBackgrounds.map((bg) => (
              <BackgroundOption
                key={bg.id}
                option={bg}
                isSelected={background.id === bg.id}
                onClick={() => selectBackground(bg.id)}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Typography</CardTitle>
          <p className="text-sm text-muted-foreground">
            Choose a font family for your interface
          </p>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {availableFonts.map((font) => (
              <FontOption
                key={font.id}
                font={font}
                isSelected={fontFamily === font.id}
                onClick={() => selectFont(font.id)}
              />
            ))}
          </div>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Preview</CardTitle>
          <p className="text-sm text-muted-foreground">
            See how your theme will look
          </p>
        </CardHeader>
        <CardContent>
          <div className="rounded-lg border p-6 bg-card text-card-foreground">
            <h3 className="text-lg font-semibold mb-2">Card Title</h3>
            <p className="text-muted-foreground mb-4">
              This is a preview of how text will appear with your current settings.
            </p>
            <div className="flex items-center space-x-4">
              <Button>Primary Button</Button>
              <Button variant="secondary">Secondary</Button>
              <Button variant="outline">Outline</Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
