{"extensions": [{"name": "Prompt", "extension": ".prompt", "description": "System prompts that define AI behavior and personality", "file_type": "Text", "handler": "Prompt Editor", "status": "active", "icon": "file", "color": "blue", "metadata": {}}, {"name": "Context", "extension": ".context", "description": "Contextual information and knowledge base entries", "file_type": "JSON", "handler": "Context Manager", "status": "active", "icon": "database", "color": "green", "metadata": {}}, {"name": "Modal", "extension": ".modal", "description": "Specialized nano-algorithms for expertise domains", "file_type": "Binary", "handler": "Modal Engine", "status": "development", "icon": "box-open", "color": "purple", "metadata": {}}, {"name": "Agent", "extension": ".agent", "description": "AI agent profiles with task specializations", "file_type": "JSON", "handler": "Agent Manager", "status": "active", "icon": "robot", "color": "orange", "metadata": {}}], "handlers": [{"name": "Prompt Editor", "description": "Edits system prompts with syntax highlighting", "version": "1.0.0"}, {"name": "Context Manager", "description": "Manages contextual knowledge entries", "version": "1.0.0"}, {"name": "Modal Engine", "description": "Executes specialized nano-algorithms", "version": "0.5.0"}, {"name": "Agent Manager", "description": "Configures AI agent profiles", "version": "1.0.0"}], "version": 1}