## Component Audit Summary and Recommendations

### Reusable Components (used in multiple places):

*   **Accordion:** Used in `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`.
*   **Badge:** Used in `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx` and `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`.
*   **Button:** Widely used across many files including `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, `e:\TheCollective\assistant-ui\src\components\TabSystem.jsx`, `e:\TheCollective\assistant-ui\src\components\ServerStatusTile.jsx`, `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, `e:\TheCollective\assistant-ui\src\components\NotificationPopout.jsx`, `e:\TheCollective\assistant-ui\src\components\FileManager.jsx`, `e:\TheCollective\assistant-ui\src\components\StorageSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\ThemeImporter.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\UnifiedThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`, `e:\TheCollective\assistant-ui\src\components\PathSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\AppearanceSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\EnhancedPathSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`, `e:\TheCollective\assistant-ui\src\pages\Dashboard.jsx`, `e:\TheCollective\assistant-ui\src\components\UnifiedFileManager.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\ThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\ThemeExporter.jsx`, `e:\TheCollective\assistant-ui\src\components\ui\enhanced-button.jsx`, `e:\TheCollective\assistant-ui\src\components\SimpleFolderSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\ServerStatus.jsx`, and `e:\TheCollective\assistant-ui\src\components\RecentActivityTile.jsx`. This is a good sign of a highly reusable component.
*   **Card:** Widely used across many files including `e:\TheCollective\assistant-ui\src\components\SystemHealthTile.jsx`, `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`, `e:\TheCollective\assistant-ui\src\components\FileManager.jsx`, `e:\TheCollective\assistant-ui\src\components\StorageSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\ThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\pages\Dashboard.jsx`, `e:\TheCollective\assistant-ui\src\components\RecentActivityTile.jsx`, `e:\TheCollective\assistant-ui\src\components\SimpleFolderSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\UnifiedFileManager.jsx`, `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, `e:\TheCollective\assistant-ui\src\components\EnhancedPathSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\ModelDownloadStatus.jsx`, `e:\TheCollective\assistant-ui\src\components\live-tile.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\UnifiedThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\AppearanceSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\NotificationPopout.jsx`, and `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`. This is another highly reusable component.
*   **Dialog:** Used in `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`.
*   **EnhancedButton:** Used in `e:\TheCollective\assistant-ui\src\components\ServerStatusTile.jsx` and `e:\TheCollective\assistant-ui\src\pages\Dashboard.jsx`. This seems to be a specialized version of the `Button` component.
*   **Form Fields (StandardInput, StandardSelect, StandardCheckbox):** Used in `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, `e:\TheCollective\assistant-ui\src\components\EnhancedPathSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, and `e:\TheCollective\assistant-ui\src\components\PathSelector.jsx`. This indicates a good pattern for form elements.
*   **Input:** Used in `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, `e:\TheCollective\assistant-ui\src\components\SimpleFolderSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\ThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\UnifiedFileManager.jsx`, `e:\TheCollective\assistant-ui\src\components\ui\form-fields.jsx`, `e:\TheCollective\assistant-ui\src\components\FileManager.jsx`, and `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`. This is a core reusable component.
*   **Label:** Widely used across many files including `e:\TheCollective\assistant-ui\src\components\PathSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\ui\form-fields.jsx`, `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\ThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\AppearanceSettings.jsx`, `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`, `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, `e:\TheCollective\assistant-ui\src\components\SimpleFolderSelector.jsx`, `e:\TheCollective\assistant-ui\src\components\FileManager.jsx`, and `e:\TheCollective\assistant-ui\src\components\EnhancedPathSelector.jsx`. This is a core reusable component.
*   **Progress:** Used in `e:\TheCollective\assistant-ui\src\components\SystemHealthTile.jsx` and `e:\TheCollective\assistant-ui\src\components\ModelDownloadStatus.jsx`.
*   **Select:** Used in `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, and `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`.
*   **Switch:** Used in `e:\TheCollective\assistant-ui\src\pages\Tools.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\ThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\UnifiedThemeSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\ServerStatus.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\AppearanceSettings.jsx`, and `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`.
*   **Tabs:** Used in `e:\TheCollective\assistant-ui\src\components\settings\AppearanceSettings.jsx`, `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`, `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`, and `e:\TheCollective\assistant-ui\src\pages\Settings.jsx`.
*   **Textarea:** Used in `e:\TheCollective\assistant-ui\src\components\ExtensionRegistry.jsx`.
*   **Toast (useToast):** Used in `e:\TheCollective\assistant-ui\src\pages\Settings.jsx` and `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`.

### Random Components (used in only one place or seem very specific):

*   **Avatar:** The `avatar.jsx` component itself imports from `@radix-ui/react-avatar` but doesn't seem to be imported and used elsewhere in the codebase based on the search. This suggests it might be a wrapper that isn't directly reused, or its usage was missed by the regex. Further investigation would be needed to confirm if it's truly "random" or if its usage is more implicit.
*   **Popover:** No direct imports found.
*   **RadioGroup:** No direct imports found.
*   **ScrollArea:** No direct imports found.
*   **Separator:** No direct imports found.
*   **Sheet:** No direct imports found.
*   **Slider:** No direct imports found.
*   **Table:** No direct imports found.
*   **Toggle:** No direct imports found.

### Design Inconsistencies:

*   **Button vs. EnhancedButton:** The existence of both `Button` and `EnhancedButton` suggests a potential inconsistency. While `EnhancedButton` might offer specific functionality, it's worth evaluating if its enhancements could be integrated into the base `Button` component or if a clear distinction and usage guidelines are in place.
*   **Input vs. StandardInput:** Similar to buttons, having both `Input` and `StandardInput` (from `e:\TheCollective\assistant-ui\src\components\ui\form-fields.jsx`) could lead to inconsistencies. It's important to ensure a clear purpose for each and consistent styling/behavior.
*   **Toast Implementation:** There's `ToastContainer` in `e:\TheCollective\assistant-ui\src\App.jsx` and `useToast` in `e:\TheCollective\assistant-ui\src\pages\Settings.jsx` and `e:\TheCollective\assistant-ui\src\components\settings\NotificationSettings.jsx`. This might indicate two different toast implementations or a lack of a centralized toast management system, which can lead to inconsistent notification styles and behavior.
*   **Missing UI Components:** Several UI components (`Popover`, `RadioGroup`, `ScrollArea`, `Separator`, `Sheet`, `Slider`, `Table`, `Toggle`) exist in the `ui` directory but are not imported or used in any of the `pages` or `components` directories based on the regex search. This could mean:
    *   They are intended for future use.
    *   They are remnants of previous development.
    *   They are used in a way that the regex didn't catch (e.g., dynamic imports, or used within other components that weren't searched).
    *   This represents a significant inconsistency where components are defined but not utilized, potentially leading to dead code or unused styling.

### What Looks Good for Consistency:

*   **Centralized UI Components:** The presence of a `components/ui` directory is excellent for centralizing reusable UI components.
*   **High Reusability of Core Components:** `Button`, `Card`, `Input`, `Label`, `Switch`, `Tabs`, and `Form Fields` are being used extensively, which is a strong indicator of good reusability and a consistent design system for these elements.
*   **Consistent Naming Conventions:** The naming of components within the `ui` directory (e.g., `accordion.jsx`, `button.jsx`) seems consistent.

### Recommendations for Improving Consistency:

1.  **Consolidate Button Components:** Evaluate if `EnhancedButton` can be merged into `Button` with props to handle variations, or clearly define when to use each.
2.  **Standardize Input Components:** Consolidate `Input` and `StandardInput` into a single, flexible input component.
3.  **Centralize Toast Management:** Implement a single, consistent toast notification system across the application.
4.  **Review Unused UI Components:** Investigate why `Popover`, `RadioGroup`, `ScrollArea`, `Separator`, `Sheet`, `Slider`, `Table`, and `Toggle` are not being used. Either implement their usage, remove them if they are truly dead code, or document their intended future use.
5.  **Establish Component Usage Guidelines:** Create clear guidelines on when to create a new component, when to reuse an existing one, and how to extend existing components.