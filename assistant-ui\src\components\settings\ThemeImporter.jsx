import { useState } from 'react';
import { Button } from "@/components/ui/button";
import { Upload } from "lucide-react";

export function ThemeImporter({ onImport }) {
  const [error, setError] = useState('');

  const handleFileUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const importedTheme = JSON.parse(e.target.result);
        // Basic validation
        if (!importedTheme.colors || !importedTheme.colors.primary) {
          throw new Error('Invalid theme format');
        }
        setError('');
        onImport(importedTheme);
      } catch (err) {
        setError('Failed to import theme. Please check the file format.');
        console.error('Import error:', err);
      }
    };
    reader.onerror = () => {
      setError('Error reading file');
    };
    reader.readAsText(file);
  };

  return (
    <div className="space-y-4">
      <h3 className="font-medium">Import Theme</h3>
      <p className="text-sm text-muted-foreground">
        Import a theme from a JSON file.
      </p>
      <div className="flex items-center space-x-4">
        <Button 
          asChild
          variant="outline"
          className="cursor-pointer"
        >
          <label>
            <Upload className="mr-2 h-4 w-4" />
            Import Theme
            <input 
              type="file" 
              className="hidden" 
              accept=".json"
              onChange={handleFileUpload}
            />
          </label>
        </Button>
        {error && (
          <p className="text-sm text-destructive">{error}</p>
        )}
      </div>
    </div>
  );
}
