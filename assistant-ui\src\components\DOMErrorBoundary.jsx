import React from 'react';

/**
 * Specialized Error Boundary for DOM manipulation errors
 * This component specifically handles React DOM reconciliation errors
 * that can occur during component unmounting or portal cleanup
 */
class DOMErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    // Check if this is a DOM manipulation error
    const errorMessage = error?.message || '';
    const isDOMError = errorMessage.includes('removeChild') ||
                      errorMessage.includes('insertBefore') ||
                      errorMessage.includes('appendChild') ||
                      errorMessage.includes('removeChildFromContainer') ||
                      errorMessage.includes('commitDeletionEffectsOnFiber') ||
                      errorMessage.includes('The node to be removed is not a child');

    if (isDOMError) {
      // For DOM errors, we'll just log and continue
      console.warn('DOM Error Boundary caught DOM manipulation error:', error);
      return { hasError: false }; // Don't show error UI for DOM errors
    }

    // For other errors, show the error UI
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    const errorMessage = error?.message || '';
    const isDOMError = errorMessage.includes('removeChild') ||
                      errorMessage.includes('insertBefore') ||
                      errorMessage.includes('appendChild') ||
                      errorMessage.includes('removeChildFromContainer') ||
                      errorMessage.includes('commitDeletionEffectsOnFiber') ||
                      errorMessage.includes('The node to be removed is not a child');

    if (isDOMError) {
      console.warn('DOM Error Boundary: DOM manipulation error caught and suppressed:', {
        error: error.toString(),
        stack: error.stack,
        componentStack: errorInfo.componentStack,
        timestamp: new Date().toISOString()
      });
      
      // Reset the error state to continue rendering
      this.setState({ hasError: false, error: null, errorInfo: null });
      return;
    }

    // For non-DOM errors, store the error info
    console.error('DOM Error Boundary: Non-DOM error caught:', error, errorInfo);
    this.setState({ error, errorInfo });
  }

  render() {
    if (this.state.hasError) {
      // Fallback UI for non-DOM errors
      return (
        <div className="flex items-center justify-center h-full p-4">
          <div className="text-center">
            <h2 className="text-lg font-semibold text-red-600 mb-2">
              Something went wrong
            </h2>
            <p className="text-sm text-gray-600 mb-4">
              An error occurred in this component. Please try refreshing the page.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
            >
              Refresh Page
            </button>
            {process.env.NODE_ENV === 'development' && (
              <details className="mt-4 text-left">
                <summary className="cursor-pointer text-sm text-gray-500">
                  Error Details (Development)
                </summary>
                <pre className="mt-2 text-xs bg-gray-100 p-2 rounded overflow-auto">
                  {this.state.error && this.state.error.toString()}
                  {this.state.errorInfo.componentStack}
                </pre>
              </details>
            )}
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default DOMErrorBoundary;
