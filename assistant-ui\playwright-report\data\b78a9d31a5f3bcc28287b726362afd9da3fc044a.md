# Page snapshot

```yaml
- generic [ref=e4]:
  - generic [ref=e5]:
    - generic [ref=e6]:
      - generic [ref=e7]:
        - generic [ref=e9]:
          - link "Dashboard" [ref=e11] [cursor=pointer]:
            - /url: /
            - img [ref=e12] [cursor=pointer]
            - generic [ref=e14] [cursor=pointer]: Dashboard
          - link "Chat" [ref=e16] [cursor=pointer]:
            - /url: /chat
            - img [ref=e18] [cursor=pointer]
            - generic [ref=e20] [cursor=pointer]: Chat
          - link "Tools" [ref=e22] [cursor=pointer]:
            - /url: /tools
            - img [ref=e23] [cursor=pointer]
            - generic [ref=e25] [cursor=pointer]: Tools
          - link "Settings" [ref=e27] [cursor=pointer]:
            - /url: /settings
            - img [ref=e28] [cursor=pointer]
            - generic [ref=e30] [cursor=pointer]: Settings
        - button [ref=e32] [cursor=pointer]:
          - img
      - main [ref=e33]:
        - generic [ref=e34]:
          - generic [ref=e35]:
            - heading "Chat" [level=1] [ref=e36]
            - button [ref=e38] [cursor=pointer]:
              - img [ref=e39] [cursor=pointer]
          - generic [ref=e42]:
            - generic [ref=e43]:
              - img [ref=e45]
              - heading "Welcome! How can I help you today?" [level=2] [ref=e47]
              - paragraph [ref=e48]: I'm powered by AI and ready to assist you with any questions or tasks.
            - generic [ref=e49]:
              - generic [ref=e50]:
                - img [ref=e51]
                - generic [ref=e53]: "Suggested prompts to get started:"
              - generic [ref=e54]:
                - button "Help me write a professional email" [ref=e55] [cursor=pointer]:
                  - generic [ref=e58] [cursor=pointer]: Help me write a professional email
                - button "Explain quantum computing in simple terms" [ref=e59] [cursor=pointer]:
                  - generic [ref=e62] [cursor=pointer]: Explain quantum computing in simple terms
                - button "Create a meal plan for this week" [ref=e63] [cursor=pointer]:
                  - generic [ref=e66] [cursor=pointer]: Create a meal plan for this week
                - button "Debug this code snippet" [ref=e67] [cursor=pointer]:
                  - generic [ref=e70] [cursor=pointer]: Debug this code snippet
                - button "Brainstorm creative project ideas" [ref=e71] [cursor=pointer]:
                  - generic [ref=e74] [cursor=pointer]: Brainstorm creative project ideas
          - generic [ref=e77]:
            - generic [ref=e78]:
              - generic [ref=e79]:
                - button "Error loading models" [ref=e81] [cursor=pointer]:
                  - generic [ref=e83] [cursor=pointer]: Error loading models
                  - img [ref=e84] [cursor=pointer]
                - paragraph [ref=e86]: Failed to fetch models.
              - button [ref=e87] [cursor=pointer]:
                - img [ref=e88] [cursor=pointer]
            - generic [ref=e90]:
              - button "Attach File" [ref=e91] [cursor=pointer]:
                - img [ref=e92] [cursor=pointer]
              - button "Voice Input" [ref=e94] [cursor=pointer]:
                - img [ref=e95] [cursor=pointer]
              - generic [ref=e97]:
                - textbox "Type your message..." [disabled]
              - button "Send" [disabled]:
                - img
                - text: Send
    - generic [ref=e98]:
      - generic [ref=e102]: Error
      - generic [ref=e103]:
        - generic [ref=e104]: "CPU: N/A"
        - generic [ref=e105]: "RAM: N/A"
        - button [ref=e106] [cursor=pointer]:
          - img
        - link [ref=e107] [cursor=pointer]:
          - /url: /settings
          - button [ref=e108] [cursor=pointer]:
            - img
  - button "10" [ref=e110] [cursor=pointer]:
    - img [ref=e111] [cursor=pointer]
    - generic [ref=e113] [cursor=pointer]: "10"
```