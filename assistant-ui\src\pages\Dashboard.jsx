import React, { useState, useEffect, useCallback, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/card';
import { Button } from '../components/ui/button';
import LiveTile from '../components/ui/live-tile';
import StatusIndicator from '../components/ui/status-indicator';
import EnhancedButton from '../components/ui/enhanced-button';
import RecentActivityTile from '../components/RecentActivityTile';
import ErrorPopup from '../components/ErrorPopup';
import { useIsMounted } from '../hooks/useIsMounted.js';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { 
  faRobot, 
  faServer, 
  faComments, 
  faCog, 
  faChartLine,
  faHistory,
  faPlug,
  faDatabase,
  faMemory,
  faMicrochip,
  faRefresh,
  faPlay,
  faStop,
  faArrowRight,
  faBolt,
  faUsers,
  faCloud
} from '@fortawesome/free-solid-svg-icons';
import { useNavigate } from 'react-router-dom';
import { invoke } from '@tauri-apps/api/core';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null, errorInfo: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    this.setState({ error, errorInfo });
    console.error('Dashboard Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="p-4">
          <ErrorPopup
            title="Dashboard Error"
            message="An error occurred in the dashboard component. Please try reloading the page."
            error={this.state.error}
            onReload={() => this.setState({ hasError: false, error: null, errorInfo: null })}
          />
          <details className="mt-4 text-xs text-muted-foreground">
            <summary>Error Details</summary>
            <pre className="mt-2 p-2 bg-muted rounded">
              {this.state.error && this.state.error.toString()}
              <br />
              {this.state.errorInfo && this.state.errorInfo.componentStack}
            </pre>
          </details>
        </div>
      );
    }

    return this.props.children;
  }
}

const Dashboard = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [autoRefresh, setAutoRefresh] = useState(true);
  const [lastUpdated, setLastUpdated] = useState(null);
  const [systemStats, setSystemStats] = useState({
    cpu: 0,
    memory: 0,
    serverStatus: 'unknown',
    modelsCount: 0,
    pluginsCount: 0,
    activeSessions: 0,
    uptime: '0m 0s'
  });
  
  const [recentActivity, setRecentActivity] = useState([
    { id: 'activity-1', type: 'chat', message: 'Started new chat session', time: '2 minutes ago', icon: faComments },
    { id: 'activity-2', type: 'model', message: 'Model qwen3:0.6b loaded', time: '5 minutes ago', icon: faRobot },
    { id: 'activity-3', type: 'server', message: 'AI server started successfully', time: '10 minutes ago', icon: faServer }
  ]);

  const [quickActions] = useState([
    { 
      id: 'new-chat', 
      title: 'New Chat', 
      icon: faComments, 
      path: '/chat', 
      description: 'Start AI conversation',
      color: 'text-blue-500' 
    },
    { 
      id: 'settings', 
      title: 'Settings', 
      icon: faCog, 
      path: '/settings', 
      description: 'Configure system',
      color: 'text-gray-500' 
    },
    { 
      id: 'plugins', 
      title: 'Plugins', 
      icon: faPlug, 
      path: '/settings', 
      description: 'Manage extensions',
      color: 'text-purple-500' 
    },
    { 
      id: 'models', 
      title: 'AI Models', 
      icon: faRobot, 
      path: '/settings', 
      description: 'Model management',
      color: 'text-green-500' 
    }
  ]);

  const isMounted = useIsMounted();
  const intervalRef = React.useRef(null);

  // Auto-refresh with visibility detection and better cleanup
  useEffect(() => {
    const loadData = async () => {
      if (isLoading || !autoRefresh || !isMounted()) {
        console.log('🔄 Dashboard: Skipping load - conditions not met:', { isLoading, autoRefresh, isMounted: isMounted() });
        return;
      }

      try {
        console.log('🔄 Dashboard: Starting data load...');
        setIsLoading(true);
        await loadDashboardData();
        setLastUpdated(new Date());
        console.log('✅ Dashboard: Data load completed successfully');
      } catch (error) {
        console.error('❌ Dashboard: Error in loadData:', error);
      } finally {
        if (isMounted()) {
          setIsLoading(false);
          console.log('✅ Dashboard: Loading state cleared');
        }
      }
    };

    // Initial load
    loadData();

    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && autoRefresh) {
        loadData();
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);

    if (autoRefresh) {
      intervalRef.current = setInterval(loadData, 30000);
    }

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [autoRefresh]);

  const loadDashboardData = async () => {
    console.log('🔄 Dashboard: Loading dashboard data...');

    try {
      // Load system metrics with fallback
      let metrics = null;
      try {
        metrics = await invoke('get_system_metrics');
        console.log('✅ Dashboard: System metrics loaded:', metrics);
      } catch (error) {
        console.warn('⚠️ Dashboard: Failed to load system metrics:', error);
        metrics = { cpu_usage: Math.floor(Math.random() * 100), memory_usage: Math.floor(Math.random() * 100) };
      }

      // Load server status with fallback
      let serverStatus = null;
      try {
        serverStatus = await invoke('get_server_status');
        console.log('✅ Dashboard: Server status loaded:', serverStatus);
      } catch (error) {
        console.warn('⚠️ Dashboard: Failed to load server status:', error);
        serverStatus = { status: 'unknown' };
      }

      // Load models count with fallback
      let models = null;
      try {
        models = await invoke('get_models_command');
        console.log('✅ Dashboard: Models loaded:', models?.length || 0);
      } catch (error) {
        console.warn('⚠️ Dashboard: Failed to load models:', error);
        models = [];
      }

      // Load plugins count with fallback
      let plugins = null;
      try {
        plugins = await invoke('get_plugins');
        console.log('✅ Dashboard: Plugins loaded:', plugins?.length || 0);
      } catch (error) {
        console.warn('⚠️ Dashboard: Failed to load plugins:', error);
        plugins = [];
      }

      // Calculate uptime (mock for now)
      const uptimeMinutes = serverStatus?.status === 'running' ? Math.floor(Math.random() * 120) : 0;
      const uptime = uptimeMinutes > 60
        ? `${Math.floor(uptimeMinutes / 60)}h ${uptimeMinutes % 60}m`
        : `${uptimeMinutes}m`;
      
      if (isMounted()) {
        const newStats = {
          cpu: Math.round(metrics?.cpu_usage || Math.random() * 100),
          memory: Math.round(metrics?.memory_usage || Math.random() * 100),
          serverStatus: serverStatus?.status || 'unknown',
          modelsCount: models?.length || Math.floor(Math.random() * 10),
          pluginsCount: plugins?.length || Math.floor(Math.random() * 20),
          activeSessions: Math.floor(Math.random() * 5) + 1,
          uptime
        };

        console.log('✅ Dashboard: Setting system stats:', newStats);
        setSystemStats(newStats);
      }
      
      // Update recent activity with real data
      if (isMounted()) {
        // In a real implementation, this would come from actual system events
        // For now, we'll just update timestamps
        setRecentActivity(prev => prev.map(activity => ({
          ...activity,
          time: `${Math.floor(Math.random() * 10) + 1} minutes ago`
        })));
      }
    } catch (error) {
      console.error('❌ Dashboard: Critical error loading dashboard data:', error);
      // Set fallback data on critical error
      if (isMounted()) {
        const fallbackStats = {
          cpu: Math.floor(Math.random() * 100),
          memory: Math.floor(Math.random() * 100),
          serverStatus: 'unknown',
          modelsCount: 3,
          pluginsCount: 8,
          activeSessions: 1,
          uptime: '0m'
        };

        console.log('🔄 Dashboard: Using fallback stats:', fallbackStats);
        setSystemStats(fallbackStats);
      }
    }
  };

  // Helper functions for status and trends
  const getServerStatus = useCallback(() => {
    switch (systemStats.serverStatus) {
      case 'running': return 'online';
      case 'stopped': return 'offline';
      case 'starting': return 'loading';
      case 'error': return 'error';
      default: return 'idle';
    }
  }, [systemStats.serverStatus]);

  const getSystemHealthStatus = useCallback(() => {
    const avgUsage = (systemStats.cpu + systemStats.memory) / 2;
    if (avgUsage > 80) return 'warning';
    if (avgUsage > 90) return 'error';
    return systemStats.serverStatus === 'running' ? 'online' : 'offline';
  }, [systemStats.cpu, systemStats.memory, systemStats.serverStatus]);

  const getCpuTrend = useCallback(() => {
    if (systemStats.cpu > 75) return { direction: 'up', value: 'High', color: 'red' };
    if (systemStats.cpu > 50) return { direction: 'up', value: 'Medium', color: 'yellow' };
    return { direction: 'down', value: 'Low', color: 'green' };
  }, [systemStats.cpu]);

  const handleTileNavigation = useCallback((path) => {
    if (isMounted()) {
      navigate(path);
    }
  }, [navigate, isMounted]);

  const handleRefresh = useCallback(async () => {
    if (!isLoading && isMounted()) {
      await loadDashboardData();
    }
  }, [isLoading, isMounted]);

  return (
    <ErrorBoundary>
      <div className="p-6 space-y-6 min-h-screen animate-fade-in relative overflow-hidden">
        {/* Background gradient effect */}
        <div className="absolute inset-0 bg-gradient-to-br from-background via-background to-background/80 z-0"></div>
        <div className="absolute inset-0 bg-grid-pattern opacity-5 z-0"></div>
        {/* Decorative orbs */}
        <div className="absolute top-20 right-[10%] w-64 h-64 rounded-full bg-primary/10 blur-3xl z-0"></div>
        <div className="absolute bottom-20 left-[15%] w-96 h-96 rounded-full bg-secondary/10 blur-3xl z-0"></div>
        
        {/* Content wrapper */}
        <div className="relative z-10">
          {/* Enhanced Header */}
          <div className="flex justify-between items-center">
            <div className="space-y-1">
              <h1 className="text-3xl font-bold text-foreground">Dashboard</h1>
              <div className="flex items-center gap-4 text-muted-foreground">
                <p>Welcome to The Collective</p>
                {lastUpdated && (
                  <span className="text-xs">
                    Last updated: {lastUpdated.toLocaleTimeString()}
                  </span>
                )}
              </div>
            </div>
            <div className="flex items-center gap-2">
              <StatusIndicator 
                status={getServerStatus()} 
                showLabel 
                size="sm" 
                className="mr-2"
              />
              <EnhancedButton
                onClick={handleRefresh}
                variant="outline"
                size="sm"
                loading={isLoading}
                icon={() => <FontAwesomeIcon icon={faRefresh} />}
                iconPosition="left"
              >
                Refresh
              </EnhancedButton>
            </div>
          </div>

          {/* Live Tile Dashboard Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 auto-rows-min mt-6">
            
            {/* Server Status Tile - Medium */}
            <LiveTile
              title="Server Status"
              value={systemStats.serverStatus === 'running' ? 'Online' : 'Offline'}
              subtitle={`Uptime: ${systemStats.uptime}`}
              size="medium"
              icon={() => <FontAwesomeIcon icon={faServer} />}
              status={getServerStatus()}
              onClick={() => handleTileNavigation('/settings?section=Server/Model-server')}
              loading={isLoading}
              className="animate-scale-in"
            />

            {/* System Health Tile - Medium */}
            <LiveTile
              title="System Health"
              value={`${Math.round((systemStats.cpu + systemStats.memory) / 2)}%`}
              subtitle={`CPU: ${systemStats.cpu}% | RAM: ${systemStats.memory}%`}
              size="medium"
              icon={() => <FontAwesomeIcon icon={faMicrochip} />}
              status={getSystemHealthStatus()}
              trend={getCpuTrend()}
              onClick={() => handleTileNavigation('/settings')}
              loading={isLoading}
              className="animate-scale-in"
            />

            {/* AI Models Tile - Small */}
            <LiveTile
              title="AI Models"
              value={systemStats.modelsCount.toString()}
              subtitle="Available models"
              size="small"
              icon={() => <FontAwesomeIcon icon={faRobot} />}
              status={systemStats.modelsCount > 0 ? 'online' : 'offline'}
              onClick={() => handleTileNavigation('/settings?section=Server/Model-model')}
              loading={isLoading}
              className="animate-scale-in"
            />

            {/* Plugins Tile - Small */}
            <LiveTile
              title="Plugins"
              value={systemStats.pluginsCount.toString()}
              subtitle="Active extensions"
              size="small"
              icon={() => <FontAwesomeIcon icon={faPlug} />}
              status={systemStats.pluginsCount > 0 ? 'online' : 'offline'}
              onClick={() => handleTileNavigation('/settings?section=Plugins')}
              loading={isLoading}
              className="animate-scale-in"
            />

            {/* Quick Actions Tile - Large */}
            <LiveTile
              title="Quick Actions"
              size="large"
              icon={() => <FontAwesomeIcon icon={faBolt} />}
              className="animate-slide-in"
            >
              <div className="grid grid-cols-2 gap-3 mt-2">
                {quickActions.map((action) => (
                  <EnhancedButton
                    key={`action-${action.id}`}
                    onClick={() => handleTileNavigation(action.path)}
                    variant="outline"
                    size="sm"
                    icon={() => <FontAwesomeIcon icon={action.icon} className={action.color} />}
                    iconPosition="left"
                    className="justify-start text-left p-3 h-auto flex-col items-start gap-1"
                  >
                    <span className="font-medium">{action.title}</span>
                    <span className="text-xs text-muted-foreground">{action.description}</span>
                  </EnhancedButton>
                ))}
              </div>
            </LiveTile>

            {/* Recent Activity Tile - Large */}
            <RecentActivityTile 
              activities={recentActivity} 
              onNavigate={handleTileNavigation} 
            />
          </div>

          {/* Performance Chart Placeholder - Enhanced with frosted effect */}
          <Card frosted={true} className="animate-fade-in">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <FontAwesomeIcon icon={faChartLine} className="w-5 h-5 text-primary" />
                  Performance Metrics
                </CardTitle>
                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">
                    Live monitoring
                  </span>
                  <StatusIndicator status="loading" size="sm" animated />
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center bg-muted/50 rounded-lg border-2 border-dashed border-muted-foreground/20">
                <div className="text-center space-y-4">
                  <div className="w-16 h-16 mx-auto bg-primary/10 rounded-full flex items-center justify-center">
                    <FontAwesomeIcon icon={faChartLine} className="h-8 w-8 text-primary" />
                  </div>
                  <div>
                    <p className="font-medium text-foreground">Performance Charts</p>
                    <p className="text-sm text-muted-foreground">Real-time system monitoring coming soon</p>
                  </div>
                  <EnhancedButton variant="outline" size="sm">
                    Enable Analytics
                  </EnhancedButton>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </ErrorBoundary>
  );
};

export default Dashboard;
