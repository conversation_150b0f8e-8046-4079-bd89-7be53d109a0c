import React, { useState } from 'react';
import { useNotifications } from '../contexts/NotificationContext';
import { <PERSON>, CardHeader, CardContent, CardFooter } from './ui/card';
import { Button } from './ui/button';
import { ChevronDown, ChevronUp, X } from 'lucide-react';

const NotificationPopout = ({ isOpen, onClose }) => {
  const { notifications, markAsRead, clearNotification, markAllAsRead, clearAll } = useNotifications();
  const [expandedGroups, setExpandedGroups] = useState({});

  if (!isOpen) return null;

  // Group notifications by source and category
  const groupedNotifications = notifications.reduce((acc, notification) => {
    const key = `${notification.source || 'system'}-${notification.category || 'general'}`;
    if (!acc[key]) {
      acc[key] = { 
        source: notification.source || 'system',
        category: notification.category || 'general',
        notifications: [],
        unreadCount: 0
      };
    }
    acc[key].notifications.push(notification);
    if (!notification.read) {
      acc[key].unreadCount++;
    }
    return acc;
  }, {});

  const toggleGroup = (key) => {
    setExpandedGroups(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  return (
    <div className="absolute bottom-full right-0 mb-2 w-80 max-h-96 overflow-y-auto bg-card border border-border rounded-md shadow-lg z-50">
      <div className="p-4">
        <div className="flex justify-between items-center mb-3">
          <h3 className="text-lg font-semibold text-card-foreground">Notifications</h3>
          <Button variant="ghost" size="icon" onClick={onClose} className="h-6 w-6 p-0 text-muted-foreground hover:text-card-foreground">
            <X className="h-4 w-4" />
          </Button>
        </div>
        {Object.keys(groupedNotifications).length === 0 ? (
          <p className="text-muted-foreground">No new notifications.</p>
        ) : (
          <div className="space-y-3">
            {Object.entries(groupedNotifications).map(([key, group]) => (
              <Card key={key} className="bg-background">
                <CardHeader className="p-3 pb-2 flex flex-row items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-card-foreground">{group.source} - {group.category}</span>
                    {group.unreadCount > 0 && (
                      <span className="ml-2 px-2 py-0.5 bg-primary text-primary-foreground rounded-full text-xs">
                        {group.unreadCount}
                      </span>
                    )}
                  </div>
                  <Button variant="ghost" size="icon" onClick={() => toggleGroup(key)} className="h-6 w-6 p-0">
                    {expandedGroups[key] ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
                  </Button>
                </CardHeader>
                {expandedGroups[key] && (
                  <CardContent className="p-3 pt-0">
                    <ul className="space-y-2">
                      {group.notifications.map((notification) => (
                        <li key={notification.id} className={`p-2 rounded-md ${notification.read ? 'bg-muted' : 'bg-accent'}`}>
                          <div className="flex justify-between items-start">
                            <p className={`text-sm ${notification.read ? 'text-muted-foreground' : 'font-medium text-card-foreground'}`}>{notification.message}</p>
                            <Button variant="ghost" size="icon" onClick={() => clearNotification(notification.id)} className="h-5 w-5 p-0 text-muted-foreground hover:text-card-foreground">
                              <X className="h-3 w-3" />
                            </Button>
                          </div>
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-xs text-muted-foreground">{notification.time}</span>
                            {!notification.read && (
                              <Button variant="link" size="sm" onClick={() => markAsRead(notification.id)} className="h-auto p-0 text-xs">
                                Mark as Read
                              </Button>
                            )}
                          </div>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                )}
              </Card>
            ))}
          </div>
        )}
        <div className="mt-4 flex justify-end space-x-2">
          <Button variant="link" size="sm" onClick={markAllAsRead} className="text-sm text-primary hover:underline">Mark all as read</Button>
          <Button variant="link" size="sm" onClick={clearAll} className="text-sm text-destructive hover:underline">Clear all</Button>
        </div>
      </div>
    </div>
  );
};

export default NotificationPopout;