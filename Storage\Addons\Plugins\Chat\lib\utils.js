// Simple utility functions for the chat plugin

export function cn(...inputs) {
  // Simple class name merger without external dependencies
  return inputs
    .filter(Boolean)
    .join(' ')
    .replace(/\s+/g, ' ')
    .trim();
}

// Additional utility functions
export function clsx(...inputs) {
  return cn(...inputs);
}

export function twMerge(...inputs) {
  // Simple Tailwind merge without external dependencies
  const classes = cn(...inputs).split(' ');
  const merged = {};
  
  // Keep only the last occurrence of conflicting classes
  classes.forEach(cls => {
    if (cls) {
      const key = cls.split('-')[0] || cls;
      merged[key] = cls;
    }
  });
  
  return Object.values(merged).join(' ');
}