import React, { createContext, useContext, useEffect, useState, useRef, useCallback, useMemo } from 'react';
import { useSettings } from './SettingsContext';

// Available color schemes
export const COLOR_SCHEMES = {
  voidCircuit: {
    id: 'voidCircuit',
    name: 'Void Circuit',
    colors: {
      primary: '#8E24AA',
      background: '#0F1115',
      surface: '#1C1F26',
      accent: '#FF7043',
      text: '#CFD8DC',
    },
  },
  midnight: {
    id: 'midnight',
    name: 'Midnight',
    colors: {
      primary: '#9C27B0',
      background: '#0A0E17',
      surface: '#131A2E',
      accent: '#7C4DFF',
      text: '#E0E0E0',
    },
  },
  cyberpunk: {
    id: 'cyberpunk',
    name: 'Cyberpunk',
    colors: {
      primary: '#FF2A6D',
      background: '#0F0A1E',
      surface: '#1A1032',
      accent: '#00F0FF',
      text: '#E6F1FF',
    },
  },
  forest: {
    id: 'forest',
    name: 'Forest',
    colors: {
      primary: '#4CAF50',
      background: '#0D1F0E',
      surface: '#1B2E1C',
      accent: '#8BC34A',
      text: '#E8F5E9',
    },
  },
  ocean: {
    id: 'ocean',
    name: 'Ocean',
    colors: {
      primary: '#2196F3',
      background: '#0A1929',
      surface: '#1E3A8A',
      accent: '#03DAC6',
      text: '#E3F2FD',
    },
  },
};

// Available background options
export const BACKGROUND_OPTIONS = [
  { id: 'solid', name: 'Solid', value: 'bg-background', preview: 'bg-background' },
  { id: 'gradient', name: 'Gradient', value: 'bg-gradient-to-br from-background to-surface', preview: 'bg-gradient-to-br from-slate-900 to-slate-800' },
  { id: 'dots', name: 'Dots', value: 'bg-background bg-dot-pattern', preview: 'bg-slate-900 bg-dot-pattern' },
  { id: 'grid', name: 'Grid', value: 'bg-background bg-grid-pattern', preview: 'bg-slate-900 bg-grid-pattern' },
];

// Font options
const FONT_OPTIONS = [
  { id: 'sans', name: 'Sans (Inter)', className: 'font-sans' },
  { id: 'serif', name: 'Serif (Merriweather)', className: 'font-serif' },
  { id: 'mono', name: 'Mono (JetBrains)', className: 'font-mono' },
  { id: 'display', name: 'Display (Jakarta)', className: 'font-display' },
];

const ThemeContext = createContext(undefined);

export function ThemeProvider({ children }) {
  const { userSettings, updateSetting } = useSettings();
  const isMounted = useRef(true);
  
  // Simple state - no complex memoization
  const [themeState, setThemeState] = useState({
    isDarkMode: true,
    colorScheme: COLOR_SCHEMES.voidCircuit,
    background: BACKGROUND_OPTIONS[0],
    fontFamily: 'sans',
    initialized: false
  });

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      isMounted.current = false;
    };
  }, []);

  // Available options - merge built-in with custom schemes from settings
  const availableColorSchemes = useMemo(() => {
    const customSchemes = userSettings?.appearance?.color_schemes || [];
    const builtinSchemes = Object.values(COLOR_SCHEMES);

    // Create a map to avoid duplicates, with custom schemes overriding built-in ones
    const schemeMap = new Map();
    builtinSchemes.forEach(scheme => schemeMap.set(scheme.id, scheme));
    customSchemes.forEach(scheme => schemeMap.set(scheme.id, scheme));

    return Array.from(schemeMap.values());
  }, [userSettings?.appearance?.color_schemes]);

  const availableBackgrounds = useMemo(() => {
    const customBackgrounds = userSettings?.appearance?.backgrounds || [];
    const builtinBackgrounds = BACKGROUND_OPTIONS;

    const bgMap = new Map();
    builtinBackgrounds.forEach(bg => bgMap.set(bg.id, bg));
    customBackgrounds.forEach(bg => bgMap.set(bg.id, bg));

    return Array.from(bgMap.values());
  }, [userSettings?.appearance?.backgrounds]);

  const availableFonts = FONT_OPTIONS;

  // Load settings on mount - wait for userSettings to be ready
  useEffect(() => {
    if (!userSettings) {
      console.log('🎨 ThemeContext: Waiting for userSettings...');
      return;
    }

    const appearance = userSettings.appearance || {};
    const isDarkMode = appearance.dark_mode ?? true;
    const schemeId = appearance.theme ?? 'voidCircuit';
    const bgId = appearance.background ?? 'solid';
    const fontFamily = appearance.font_family ?? 'sans';

    // Find scheme and background from available options
    const colorScheme = availableColorSchemes.find(s => s.id === schemeId) || COLOR_SCHEMES.voidCircuit;
    const background = availableBackgrounds.find(bg => bg.id === bgId) || BACKGROUND_OPTIONS[0];

    console.log('🎨 ThemeContext: Initializing theme from settings:', {
      isDarkMode,
      schemeId,
      colorScheme: colorScheme?.name,
      background: background?.name,
      fontFamily,
      userSettings: userSettings.appearance,
      availableSchemes: availableColorSchemes.map(s => s.id)
    });

    setThemeState({
      isDarkMode,
      colorScheme,
      background,
      fontFamily,
      initialized: true
    });
  }, [userSettings, availableColorSchemes, availableBackgrounds]);

  // Helper function to convert hex to HSL
  const hexToHsl = useCallback((hex) => {
    if (!hex || typeof hex !== 'string') return '0 0% 0%';

    // Remove # if present
    hex = hex.replace('#', '');

    // Convert to RGB
    const r = parseInt(hex.substr(0, 2), 16) / 255;
    const g = parseInt(hex.substr(2, 2), 16) / 255;
    const b = parseInt(hex.substr(4, 2), 16) / 255;

    const max = Math.max(r, g, b);
    const min = Math.min(r, g, b);
    let h, s, l = (max + min) / 2;

    if (max === min) {
      h = s = 0; // achromatic
    } else {
      const d = max - min;
      s = l > 0.5 ? d / (2 - max - min) : d / (max + min);
      switch (max) {
        case r: h = (g - b) / d + (g < b ? 6 : 0); break;
        case g: h = (b - r) / d + 2; break;
        case b: h = (r - g) / d + 4; break;
        default: h = 0;
      }
      h /= 6;
    }

    const H = Math.round(h * 360);
    const S = Math.round(s * 100);
    const L = Math.round(l * 100);

    return `${H} ${S}% ${L}%`;
  }, []);

  // Apply theme to DOM
  useEffect(() => {
    if (!themeState.initialized) return;

    const root = document.documentElement;

    // Apply dark mode
    root.classList.toggle('dark', themeState.isDarkMode);

    // Apply color scheme - map to the correct CSS variables your app expects
    if (themeState.colorScheme?.colors) {
      const colors = themeState.colorScheme.colors;

      // Map theme colors to CSS variables (convert hex to HSL)
      if (colors.background) root.style.setProperty('--background', hexToHsl(colors.background));
      if (colors.text) root.style.setProperty('--foreground', hexToHsl(colors.text));
      if (colors.primary) root.style.setProperty('--primary', hexToHsl(colors.primary));
      if (colors.surface) {
        root.style.setProperty('--card', hexToHsl(colors.surface));
        root.style.setProperty('--popover', hexToHsl(colors.surface));
        root.style.setProperty('--secondary', hexToHsl(colors.surface));
        root.style.setProperty('--muted', hexToHsl(colors.surface));
      }
      if (colors.accent) root.style.setProperty('--accent', hexToHsl(colors.accent));

      // Set foreground colors for contrast
      if (colors.text) {
        root.style.setProperty('--card-foreground', hexToHsl(colors.text));
        root.style.setProperty('--popover-foreground', hexToHsl(colors.text));
        root.style.setProperty('--secondary-foreground', hexToHsl(colors.text));
        root.style.setProperty('--muted-foreground', hexToHsl(colors.text));
      }

      // Set primary foreground (usually white for dark themes)
      root.style.setProperty('--primary-foreground', '0 0% 100%');
      root.style.setProperty('--accent-foreground', '0 0% 100%');
    }

    // Apply font
    root.style.setProperty('--font-family', getFontFamily(themeState.fontFamily));

    // Debug: Log what theme is being applied
    console.log('🎨 Theme Applied:', {
      scheme: themeState.colorScheme?.name,
      colors: themeState.colorScheme?.colors,
      isDark: themeState.isDarkMode,
      cssVars: {
        background: root.style.getPropertyValue('--background'),
        primary: root.style.getPropertyValue('--primary'),
        foreground: root.style.getPropertyValue('--foreground')
      }
    });
  }, [themeState, hexToHsl]);

  // Helper function for font families
  const getFontFamily = useCallback((fontId) => {
    const fontMap = {
      sans: 'Inter, system-ui, sans-serif',
      serif: 'Merriweather, Georgia, serif',
      mono: 'JetBrains Mono, Consolas, monospace',
      display: 'Plus Jakarta Sans, system-ui, sans-serif'
    };
    return fontMap[fontId] || fontMap.sans;
  }, []);



  // Action functions - these update state immediately and persist in background
  const selectColorScheme = useCallback((schemeId) => {
    console.log('🎨 ThemeContext: Selecting color scheme:', schemeId);
    // Find scheme in both built-in and custom schemes
    const scheme = availableColorSchemes.find(s => s.id === schemeId) || COLOR_SCHEMES.voidCircuit;

    // Update state immediately
    setThemeState(prev => ({ ...prev, colorScheme: scheme }));

    // Persist to settings immediately
    if (updateSetting) {
      console.log('💾 ThemeContext: Persisting color scheme to settings:', schemeId);
      updateSetting('appearance', { theme: schemeId })
        .then(() => {
          console.log('✅ ThemeContext: Color scheme saved successfully');
        })
        .catch((error) => {
          console.error('❌ ThemeContext: Failed to save color scheme:', error);
        });
    }
  }, [availableColorSchemes, updateSetting]);

  const selectBackground = useCallback((bgId) => {
    const bg = availableBackgrounds.find(b => b.id === bgId) || BACKGROUND_OPTIONS[0];
    setThemeState(prev => ({ ...prev, background: bg }));

    // Persist to settings
    if (updateSetting) {
      updateSetting('appearance', { background: bgId }).catch(console.error);
    }
  }, [availableBackgrounds, updateSetting]);

  const selectFont = useCallback((fontId) => {
    setThemeState(prev => ({ ...prev, fontFamily: fontId }));
    
    // Persist to settings
    if (updateSetting) {
      updateSetting('appearance', { font_family: fontId }).catch(console.error);
    }
  }, [updateSetting]);

  const toggleDarkMode = useCallback((isDark) => {
    const newDarkMode = isDark !== undefined ? isDark : !themeState.isDarkMode;
    setThemeState(prev => ({ ...prev, isDarkMode: newDarkMode }));
    
    // Persist to settings
    if (updateSetting) {
      updateSetting('appearance', { dark_mode: newDarkMode }).catch(console.error);
    }
  }, [themeState.isDarkMode, updateSetting]);

  // CRUD operations for custom schemes
  const addColorScheme = useCallback((scheme) => {
    const current = userSettings?.appearance?.color_schemes || [];
    const updated = [...current.filter(s => s.id !== scheme.id), scheme];
    
    if (updateSetting) {
      updateSetting('appearance', { color_schemes: updated }).catch(console.error);
    }
  }, [userSettings, updateSetting]);

  const updateColorScheme = useCallback((id, updates) => {
    const current = userSettings?.appearance?.color_schemes || [];
    const existingIndex = current.findIndex(s => s.id === id);

    let updated;
    if (existingIndex >= 0) {
      // Update existing custom scheme
      updated = current.map(s => s.id === id ? { ...s, ...updates } : s);
    } else {
      // Add new custom scheme (editing a built-in scheme creates a custom version)
      updated = [...current, updates];
    }

    if (updateSetting) {
      updateSetting('appearance', { color_schemes: updated }).catch(console.error);
    }

    // Update current scheme if it's the active one
    if (themeState.colorScheme?.id === id) {
      setThemeState(prev => ({ ...prev, colorScheme: updates }));
    }
  }, [userSettings, updateSetting, themeState.colorScheme?.id]);

  const deleteColorScheme = useCallback((id) => {
    const current = userSettings?.appearance?.color_schemes || [];
    const updated = current.filter(s => s.id !== id);
    
    if (updateSetting) {
      updateSetting('appearance', { color_schemes: updated }).catch(console.error);
    }
    
    // Switch to default if deleting active scheme
    if (themeState.colorScheme?.id === id) {
      selectColorScheme('voidCircuit');
    }
  }, [userSettings, updateSetting, themeState.colorScheme?.id, selectColorScheme]);

  // Background CRUD operations
  const addBackground = useCallback((bg) => {
    const current = userSettings?.appearance?.backgrounds || [];
    const updated = [...current.filter(b => b.id !== bg.id), bg];
    
    if (updateSetting) {
      updateSetting('appearance', { backgrounds: updated }).catch(console.error);
    }
  }, [userSettings, updateSetting]);

  const updateBackground = useCallback((id, updates) => {
    const current = userSettings?.appearance?.backgrounds || [];
    const updated = current.map(b => b.id === id ? { ...b, ...updates } : b);
    
    if (updateSetting) {
      updateSetting('appearance', { backgrounds: updated }).catch(console.error);
    }
    
    // Update current background if it's the active one
    if (themeState.background?.id === id) {
      const newBg = updated.find(b => b.id === id);
      if (newBg) {
        setThemeState(prev => ({ ...prev, background: newBg }));
      }
    }
  }, [userSettings, updateSetting, themeState.background?.id]);

  const deleteBackground = useCallback((id) => {
    const current = userSettings?.appearance?.backgrounds || [];
    const updated = current.filter(b => b.id !== id);
    
    if (updateSetting) {
      updateSetting('appearance', { backgrounds: updated }).catch(console.error);
    }
    
    // Switch to default if deleting active background
    if (themeState.background?.id === id) {
      selectBackground('solid');
    }
  }, [userSettings, updateSetting, themeState.background?.id, selectBackground]);

  // Stable context value - this is the key to fixing hook rendering issues
  const contextValue = {
    // Current theme state
    isDarkMode: themeState.isDarkMode,
    colorScheme: themeState.colorScheme,
    background: themeState.background,
    fontFamily: themeState.fontFamily,
    
    // Available options
    availableColorSchemes,
    availableBackgrounds,
    availableFonts,
    
    // Actions
    selectColorScheme,
    selectBackground,
    selectFont,
    toggleDarkMode,
    
    // CRUD operations
    addColorScheme,
    updateColorScheme,
    deleteColorScheme,
    addBackground,
    updateBackground,
    deleteBackground,
    
    // Helpers
    getFontFamily,
    COLOR_SCHEMES,
    BACKGROUND_OPTIONS,
    
    // For compatibility with existing code
    updateSetting
  };

  return (
    <ThemeContext.Provider value={contextValue}>
      <div className={`${themeState.background?.value || ''} min-h-screen transition-colors duration-300`}>
        {children}
      </div>
    </ThemeContext.Provider>
  );
}

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};
