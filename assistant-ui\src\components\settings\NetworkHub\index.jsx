import React, { useState } from 'react';
import { Card, Tabs, Tab, Box } from '@mui/material';
import NetworkDetails from './NetworkDetails';
import NetworkProxyVpn from './NetworkProxyVpn';

const NetworkHub = () => {
  const [activeTab, setActiveTab] = useState(0);

  const handleTabChange = (event, newValue) => {
    setActiveTab(newValue);
  };

  return (
    <Card variant="outlined" sx={{ width: '100%', maxWidth: 1200, mx: 'auto', mt: 4 }}>
      <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
        <Tabs 
          value={activeTab} 
          onChange={handleTabChange}
          aria-label="network settings tabs"
          sx={{
            '& .MuiTabs-indicator': {
              backgroundColor: 'primary.main',
            },
          }}
        >
          <Tab 
            label="Details" 
            id="network-tab-0" 
            aria-controls="network-tabpanel-0"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
          <Tab 
            label="Proxy/VPN" 
            id="network-tab-1" 
            aria-controls="network-tabpanel-1"
            sx={{ textTransform: 'none', fontWeight: 500 }}
          />
        </Tabs>
      </Box>
      
      <Box sx={{ p: 3 }}>
        {activeTab === 0 && (
          <NetworkDetails />
        )}
        {activeTab === 1 && (
          <NetworkProxyVpn />
        )}
      </Box>
    </Card>
  );
};

export default NetworkHub;
