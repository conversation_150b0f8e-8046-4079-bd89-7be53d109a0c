import React from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faDisplay } from '@fortawesome/free-solid-svg-icons';
import { useCastController } from '../hooks/useCastController';
import CastModal from './CastModal';

const CastTriggerButton = ({ variant = 'default', className = '', children }) => {
  const {
    isModalOpen,
    openCastModal,
    closeCastModal,
    devices,
    selectedDevice,
    isLoading,
    error,
    isCasting,
    startCasting,
    stopCasting,
    setSelectedDevice,
    refreshDevices,
  } = useCastController();

  const handleSelectDevice = (device) => {
    setSelectedDevice(device);
    startCasting(device);
  };

  return (
    <>
      <button
        onClick={openCastModal}
        className={`inline-flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ${
          variant === 'icon' 
            ? 'p-2 rounded-full hover:bg-accent hover:text-accent-foreground' 
            : 'bg-primary text-primary-foreground hover:bg-primary/90 h-10'
        } ${className}`}
        aria-label="Cast to device"
      >
        {children || (
          <>
            <FontAwesomeIcon icon={faDisplay} className="mr-2" />
            {variant !== 'icon' && 'Cast'}
          </>
        )}
      </button>

      <CastModal
        isOpen={isModalOpen}
        onClose={closeCastModal}
        devices={devices}
        selectedDevice={selectedDevice}
        onSelectDevice={handleSelectDevice}
        isLoading={isLoading}
        error={error}
        isCasting={isCasting}
        onRefresh={refreshDevices}
        onStopCasting={stopCasting}
      />
    </>
  );
};

export default CastTriggerButton;
