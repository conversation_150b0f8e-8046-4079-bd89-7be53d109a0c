import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { FontAwesomeIcon } from './icons/FontAwesome.jsx';
import {
  faSearch,
  faTimes,
  faSpinner,
  faFilter,
  faCalendar,
  faUser,
  faRobot,
  faBookmark,
  faArrowUp,
  faArrowDown,
  faHighlighter
} from './icons/FontAwesome.jsx';

const SearchPanel = ({
  query,
  onQueryChange,
  results,
  isSearching,
  onSearch,
  onClose
}) => {
  const [filters, setFilters] = useState({
    sender: 'all', // all, user, assistant, system
    dateRange: 'all', // all, today, week, month
    hasBookmarks: false,
    hasFiles: false
  });
  const [showFilters, setShowFilters] = useState(false);
  const [selectedResult, setSelectedResult] = useState(0);
  const searchInputRef = useRef(null);

  useEffect(() => {
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  }, []);

  useEffect(() => {
    if (query.trim()) {
      const timeoutId = setTimeout(() => {
        onSearch(query, filters);
      }, 300);
      return () => clearTimeout(timeoutId);
    }
  }, [query, filters, onSearch]);

  const handleKeyDown = (e) => {
    if (e.key === 'Escape') {
      onClose();
    } else if (e.key === 'ArrowDown') {
      e.preventDefault();
      setSelectedResult(prev => Math.min(prev + 1, results.length - 1));
    } else if (e.key === 'ArrowUp') {
      e.preventDefault();
      setSelectedResult(prev => Math.max(prev - 1, 0));
    } else if (e.key === 'Enter' && results[selectedResult]) {
      // TODO: Navigate to selected result
      console.log('Navigate to result:', results[selectedResult]);
    }
  };

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / 86400000);

    if (diffDays === 0) return 'Today';
    if (diffDays === 1) return 'Yesterday';
    if (diffDays < 7) return `${diffDays} days ago`;
    return date.toLocaleDateString();
  };

  const highlightText = (text, searchQuery) => {
    if (!searchQuery.trim()) return text;
    
    const regex = new RegExp(`(${searchQuery.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
    const parts = text.split(regex);
    
    return parts.map((part, index) => 
      regex.test(part) ? (
        <mark key={index} className="bg-yellow-200 dark:bg-yellow-800 px-1 rounded">
          {part}
        </mark>
      ) : part
    );
  };

  const getResultPreview = (result) => {
    const maxLength = 150;
    const content = result.content || '';
    
    if (content.length <= maxLength) return content;
    
    // Try to find the search term and show context around it
    const queryIndex = content.toLowerCase().indexOf(query.toLowerCase());
    if (queryIndex !== -1) {
      const start = Math.max(0, queryIndex - 50);
      const end = Math.min(content.length, queryIndex + query.length + 50);
      const preview = content.slice(start, end);
      return (start > 0 ? '...' : '') + preview + (end < content.length ? '...' : '');
    }
    
    return content.slice(0, maxLength) + '...';
  };

  return (
    <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 shadow-sm">
      <div className="p-4">
        {/* Search input */}
        <div className="flex items-center gap-3 mb-4">
          <div className="flex-1 relative">
            <FontAwesomeIcon
              icon={faSearch}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400"
            />
            <Input
              ref={searchInputRef}
              type="text"
              placeholder="Search messages..."
              value={query}
              onChange={(e) => onQueryChange(e.target.value)}
              onKeyDown={handleKeyDown}
              className="pl-10 pr-10"
            />
            {isSearching && (
              <FontAwesomeIcon
                icon={faSpinner}
                className="absolute right-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400 animate-spin"
              />
            )}
          </div>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className={showFilters ? 'bg-blue-50 dark:bg-blue-900/20' : ''}
          >
            <FontAwesomeIcon icon={faFilter} className="h-4 w-4 mr-2" />
            Filters
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="h-9 w-9 p-0"
          >
            <FontAwesomeIcon icon={faTimes} className="h-4 w-4" />
          </Button>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-4 space-y-3">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Sender filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FontAwesomeIcon icon={faUser} className="h-4 w-4 mr-1" />
                  Sender
                </label>
                <select
                  value={filters.sender}
                  onChange={(e) => setFilters(prev => ({ ...prev, sender: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All messages</option>
                  <option value="user">My messages</option>
                  <option value="assistant">AI responses</option>
                  <option value="system">System messages</option>
                </select>
              </div>

              {/* Date range filter */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  <FontAwesomeIcon icon={faCalendar} className="h-4 w-4 mr-1" />
                  Date Range
                </label>
                <select
                  value={filters.dateRange}
                  onChange={(e) => setFilters(prev => ({ ...prev, dateRange: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                >
                  <option value="all">All time</option>
                  <option value="today">Today</option>
                  <option value="week">This week</option>
                  <option value="month">This month</option>
                </select>
              </div>

              {/* Bookmark filter */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="bookmarks-filter"
                  checked={filters.hasBookmarks}
                  onChange={(e) => setFilters(prev => ({ ...prev, hasBookmarks: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="bookmarks-filter" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  <FontAwesomeIcon icon={faBookmark} className="h-4 w-4 mr-1" />
                  Bookmarked only
                </label>
              </div>

              {/* Files filter */}
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="files-filter"
                  checked={filters.hasFiles}
                  onChange={(e) => setFilters(prev => ({ ...prev, hasFiles: e.target.checked }))}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                />
                <label htmlFor="files-filter" className="ml-2 text-sm text-gray-700 dark:text-gray-300">
                  Has attachments
                </label>
              </div>
            </div>
          </div>
        )}

        {/* Results summary */}
        {query.trim() && (
          <div className="flex items-center justify-between text-sm text-gray-600 dark:text-gray-400 mb-3">
            <span>
              {isSearching ? 'Searching...' : `${results.length} result${results.length !== 1 ? 's' : ''} found`}
            </span>
            {results.length > 0 && (
              <div className="flex items-center gap-2">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedResult(Math.max(0, selectedResult - 1))}
                  disabled={selectedResult === 0}
                  className="h-6 w-6 p-0"
                >
                  <FontAwesomeIcon icon={faArrowUp} className="h-3 w-3" />
                </Button>
                <span className="text-xs">
                  {selectedResult + 1} of {results.length}
                </span>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedResult(Math.min(results.length - 1, selectedResult + 1))}
                  disabled={selectedResult === results.length - 1}
                  className="h-6 w-6 p-0"
                >
                  <FontAwesomeIcon icon={faArrowDown} className="h-3 w-3" />
                </Button>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Results list */}
      {results.length > 0 && (
        <div className="max-h-80 overflow-y-auto border-t border-gray-200 dark:border-gray-700">
          {results.map((result, index) => (
            <div
              key={result.id}
              className={`p-4 border-b border-gray-100 dark:border-gray-700 cursor-pointer transition-colors ${
                index === selectedResult
                  ? 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800'
                  : 'hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => {
                setSelectedResult(index);
                // TODO: Navigate to message
                console.log('Navigate to message:', result);
              }}
            >
              <div className="flex items-start gap-3">
                {/* Sender avatar */}
                <div className={`w-6 h-6 rounded-full flex items-center justify-center flex-shrink-0 ${
                  result.sender === 'user'
                    ? 'bg-blue-100 dark:bg-blue-900'
                    : result.sender === 'assistant'
                    ? 'bg-purple-100 dark:bg-purple-900'
                    : 'bg-gray-100 dark:bg-gray-700'
                }`}>
                  <FontAwesomeIcon
                    icon={result.sender === 'user' ? faUser : faRobot}
                    className={`h-3 w-3 ${
                      result.sender === 'user'
                        ? 'text-blue-600 dark:text-blue-400'
                        : result.sender === 'assistant'
                        ? 'text-purple-600 dark:text-purple-400'
                        : 'text-gray-600 dark:text-gray-400'
                    }`}
                  />
                </div>

                {/* Message content */}
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-1">
                    <span className="text-sm font-medium text-gray-900 dark:text-gray-100 capitalize">
                      {result.sender}
                    </span>
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      {formatDate(result.timestamp)}
                    </span>
                    {result.bookmarked && (
                      <FontAwesomeIcon icon={faBookmark} className="h-3 w-3 text-yellow-500" />
                    )}
                    {result.files && result.files.length > 0 && (
                      <span className="text-xs bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded">
                        {result.files.length} file{result.files.length !== 1 ? 's' : ''}
                      </span>
                    )}
                  </div>
                  
                  <div className="text-sm text-gray-700 dark:text-gray-300 line-clamp-2">
                    {highlightText(getResultPreview(result), query)}
                  </div>
                </div>

                {/* Match indicator */}
                <div className="flex-shrink-0">
                  <FontAwesomeIcon icon={faHighlighter} className="h-4 w-4 text-yellow-500" />
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* No results */}
      {query.trim() && !isSearching && results.length === 0 && (
        <div className="p-8 text-center border-t border-gray-200 dark:border-gray-700">
          <FontAwesomeIcon icon={faSearch} className="h-12 w-12 text-gray-300 dark:text-gray-600 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
            No results found
          </h3>
          <p className="text-gray-600 dark:text-gray-400">
            Try adjusting your search terms or filters
          </p>
        </div>
      )}
    </div>
  );
};

export default SearchPanel;