import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { useToast } from "../ui/use-toast";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { useSettings } from '../../contexts/SettingsContext';

const NotificationSettings = () => {
  const { toast } = useToast();
  const { userSettings, updateSetting } = useSettings();

  const [notificationsEnabled, setNotificationsEnabled] = useState(userSettings.notifications?.enabled || false);
  const [notificationPosition, setNotificationPosition] = useState(userSettings.notifications?.position || 'bottom-right');
  const [testMessage, setTestMessage] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [saveStatus, setSaveStatus] = useState({
    backendConnection: 'Unknown',
    localStorage: 'Unknown',
    totalSaves: 0,
    lastSave: 'Never',
  });
  const [activeTab, setActiveTab] = useState('general');

  // System Notification States
  const [systemNotificationTypes, setSystemNotificationTypes] = useState(userSettings.notifications?.systemNotificationTypes || {
    systemUpdates: false,
    securityAlerts: false,
    newFeatures: false,
    maintenance: false,
  });

  // Handler for enabling/disabling all notifications
  const handleNotificationsEnabledChange = useCallback(async (checked) => {
    setNotificationsEnabled(checked);
    await updateSetting('notifications.enabled', checked);
  }, [updateSetting]);

  // Handler for changing notification position
  const handleNotificationPositionChange = useCallback(async (value) => {
    setNotificationPosition(value);
    await updateSetting('notifications.position', value);
  }, [updateSetting]);

  // Effect to load settings from local storage on component mount
  useEffect(() => {
    setNotificationsEnabled(userSettings.notifications?.enabled || false);
    setNotificationPosition(userSettings.notifications?.position || 'bottom-right');
    setSystemNotificationTypes(userSettings.notifications?.systemNotificationTypes || {
      systemUpdates: false,
      securityAlerts: false,
      newFeatures: false,
      maintenance: false,
    });

    // Initialize save status based on userSettings presence
    setSaveStatus((prev) => ({
      ...prev,
      localStorage: userSettings.notifications ? 'Loaded' : 'No settings found',
      totalSaves: userSettings.notifications ? prev.totalSaves : 0,
      lastSave: userSettings.notifications ? prev.lastSave : 'Never',
    }));
  }, [userSettings]);

  // Function to save settings to local storage
  const saveSettings = useCallback(async () => {
    setIsSaving(true);
    // The individual settings are already being updated via updateSetting in their respective handlers
    // This saveSettings is now primarily for showing the toast and updating saveStatus

    // We need to explicitly save the systemNotificationTypes here as they are not saved on individual change
    await updateSetting('notifications.systemNotificationTypes', systemNotificationTypes);

    setSaveStatus((prev) => {
      const newTotalSaves = prev.totalSaves + 1;
      const newLastSave = new Date().toLocaleTimeString();
      return { ...prev, localStorage: 'Saved', totalSaves: newTotalSaves, lastSave: newLastSave };
    });

    toast.success({
      title: 'Settings Saved',
      description: 'Your notification settings have been updated.',
    });
    setIsSaving(false);
  }, [systemNotificationTypes, updateSetting]);

  // Function to clear save stats
  const clearSaveStats = () => {
    setSaveStatus({
      backendConnection: 'N/A',
      localStorage: 'Cleared',
      totalSaves: 0,
      lastSave: 'Never',
    });
    // No need to clear from local storage as settings are managed by SettingsContext
    toast.success({
      title: "Save Stats Cleared",
      description: "All save statistics have been reset.",
    });
  };

  // Handler for system notification type changes
  const handleSystemNotificationTypeChange = useCallback(async (key, checked) => {
    setSystemNotificationTypes((prev) => ({
      ...prev,
      [key]: checked,
    }));
    // System notification types are saved when the 'Save System Settings' button is clicked
  }, []);

  return (
    <div className="notification-settings-page space-y-6">
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="general">General</TabsTrigger>
          <TabsTrigger value="system">System</TabsTrigger>
          <TabsTrigger value="applications">Applications</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
        </TabsList>

        <TabsContent value="general" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>General Notification Preferences</CardTitle>
              <CardDescription>
                Configure basic notification settings like enabling/disabling all notifications,
                position, and test messages.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="enable-notifications-tab" className="flex flex-col space-y-1">
                  <span>Enable All Notifications</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Globally turn on or off all notifications.
                  </span>
                </Label>
                <Switch
                  id="enable-notifications-tab"
                  checked={notificationsEnabled}
                  onCheckedChange={handleNotificationsEnabledChange}
                />
              </div>

              <div className="flex items-center justify-between space-x-2">
                <Label htmlFor="notification-position-tab" className="flex flex-col space-y-1">
                  <span>Notification Display Position</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Select where notifications will appear on your screen.
                  </span>
                </Label>
                <Select value={notificationPosition} onValueChange={handleNotificationPositionChange}>
                  <SelectTrigger id="notification-position-tab" className="w-[180px]">
                    <SelectValue placeholder="Select position" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="top-right">Top Right</SelectItem>
                    <SelectItem value="top-center">Top Center</SelectItem>
                    <SelectItem value="top-left">Top Left</SelectItem>
                    <SelectItem value="bottom-right">Bottom Right</SelectItem>
                    <SelectItem value="bottom-center">Bottom Center</SelectItem>
                    <SelectItem value="bottom-left">Bottom Left</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
            <CardFooter className="flex justify-between">
              <div className="text-sm text-muted-foreground">
                <p>Backend Connection: {saveStatus.backendConnection}</p>
                <p>Local Storage: {saveStatus.localStorage}</p>
                <p>Total Saves: {saveStatus.totalSaves}</p>
                <p>Last Save: {saveStatus.lastSave}</p>
              </div>
              <div className="space-x-2">
                <Button variant="outline" onClick={clearSaveStats}>
                  Clear Stats
                </Button>
                <Button onClick={saveSettings} disabled={isSaving}>
                  {isSaving ? 'Saving...' : 'Save General Settings'}
                </Button>
              </div>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="system" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Notification Controls</CardTitle>
              <CardDescription>
                Manage specific types of notifications generated by the system.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {Object.entries(systemNotificationTypes).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between space-x-2">
                  <Label htmlFor={`system-${key}`} className="flex flex-col space-y-1">
                    <span>
                      {key.replace(/([A-Z])/g, ' $1').replace(/^./, (str) => str.toUpperCase())}
                    </span>
                    <span className="font-normal leading-snug text-muted-foreground">
                      Receive notifications for {key.replace(/([A-Z])/g, ' $1').toLowerCase()}.
                    </span>
                  </Label>
                  <Switch
                    id={`system-${key}`}
                    checked={value}
                    onCheckedChange={(checked) =>
                      handleSystemNotificationTypeChange(key, checked)
                    }
                  />
                </div>
              ))}
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save System Settings'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="applications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Application & Plugin Notifications</CardTitle>
              <CardDescription>
                Configure notification preferences for individual applications and plugins.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                Here you can manage notifications for each installed application and plugin.
              </p>
              <div className="space-y-2">
                {/* Placeholder for a list of installed applications/plugins */}
                <div className="flex items-center justify-between space-x-2 opacity-50 cursor-not-allowed">
                  <Label htmlFor="app-notification-example" className="flex flex-col space-y-1">
                    <span>Example App Notifications</span>
                    <span className="font-normal leading-snug text-muted-foreground">
                      Receive notifications from Example App.
                    </span>
                  </Label>
                  <Switch id="app-notification-example" checked={false} disabled />
                </div>
                <div className="flex items-center justify-between space-x-2 opacity-50 cursor-not-allowed">
                  <Label htmlFor="plugin-notification-example" className="flex flex-col space-y-1">
                    <span>Example Plugin Notifications</span>
                    <span className="font-normal leading-snug text-muted-foreground">
                      Receive notifications from Example Plugin.
                    </span>
                  </Label>
                  <Switch id="plugin-notification-example" checked={false} disabled />
                </div>
                {/* More applications/plugins would be mapped here */}
              </div>
            </CardContent>
            <CardFooter>
              <Button onClick={saveSettings} disabled={isSaving}>
                {isSaving ? 'Saving...' : 'Save Application Settings'}
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>

        <TabsContent value="history" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Notification History</CardTitle>
              <CardDescription>
                View a log of all past notifications received.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-muted-foreground">
                This section will display a chronological list of your notifications.
                Currently, there are no historical notifications to display.
              </p>
              {/* Placeholder for a list of past notifications */}
              <div className="space-y-2 text-center text-muted-foreground">
                <p>No notifications in history yet.</p>
                <p>Notifications will appear here as you receive them.</p>
              </div>
            </CardContent>
            <CardFooter>
              <Button variant="outline" disabled>
                Clear History (Coming Soon)
              </Button>
            </CardFooter>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default NotificationSettings;
