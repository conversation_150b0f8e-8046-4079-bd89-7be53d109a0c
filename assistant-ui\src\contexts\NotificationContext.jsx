import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { useSettings } from './SettingsContext';

const NotificationContext = createContext();

// Removed defaultNotificationSettings as it will now come from SettingsContext

export const NotificationProvider = ({ children }) => {
  const [notifications, setNotifications] = useState([]);
  const { userSettings } = useSettings();

  // Access notification settings directly from userSettings
  const currentNotificationSettings = userSettings?.notifications || {};

  const addNotification = useCallback(({ type = 'info', message, duration, source, category, priority, clearable = true }) => {
    setNotifications((prevNotifications) => {
      const newNotification = {
        id: uuidv4(),
        type,
        message,
        duration: duration || currentNotificationSettings.duration || 5000, // Fallback to 5000 if not in settings
        source: source || currentNotificationSettings.defaultSource || 'system',
        category: category || currentNotificationSettings.defaultCategory || 'general',
        priority: priority || currentNotificationSettings.defaultPriority || 'medium',
        read: false,
        cleared: false,
        timestamp: new Date().toISOString(),
        clearable,
      };
      return [...prevNotifications, newNotification];
    });
  }, [currentNotificationSettings]); // Dependency on currentNotificationSettings

  const markAsRead = useCallback((id) => {
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, read: true } : n))
    );
  }, []);

  const clearNotification = useCallback((id) => {
    setNotifications(prev =>
      prev.map(n => (n.id === id ? { ...n, cleared: true } : n))
    );
  }, []);

  const markAllAsRead = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, read: true })));
  }, []);

  const clearAll = useCallback(() => {
    setNotifications(prev => prev.map(n => ({ ...n, cleared: true })));
  }, []);

  useEffect(() => {
    if (notifications.length > 0) {
      const timer = setTimeout(() => {
        setNotifications(prev => prev.map(n => ({
          ...n,
          cleared: n.cleared || (Date.now() - new Date(n.timestamp).getTime() >= n.duration)
        })));
      }, 1000);
      return () => clearTimeout(timer);
    }
  }, [notifications]);

  const value = {
    notifications: notifications.filter(n => !n.cleared),
    addNotification,
    markAsRead,
    clearNotification,
    markAllAsRead,
    clearAll,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
};

export const useNotifications = () => useContext(NotificationContext);
