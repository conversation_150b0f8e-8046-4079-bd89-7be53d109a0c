# Page snapshot

```yaml
- generic [ref=e4]:
  - generic [ref=e5]:
    - generic [ref=e6]:
      - generic [ref=e7]:
        - generic [ref=e9]:
          - link "Dashboard" [ref=e11] [cursor=pointer]:
            - /url: /
            - img [ref=e12] [cursor=pointer]
            - generic [ref=e14] [cursor=pointer]: Dashboard
          - link "Chat" [ref=e16] [cursor=pointer]:
            - /url: /chat
            - img [ref=e17] [cursor=pointer]
            - generic [ref=e19] [cursor=pointer]: Chat
          - link "Tools" [ref=e21] [cursor=pointer]:
            - /url: /tools
            - img [ref=e22] [cursor=pointer]
            - generic [ref=e24] [cursor=pointer]: Tools
          - link "Settings" [ref=e26] [cursor=pointer]:
            - /url: /settings
            - img [ref=e28] [cursor=pointer]
            - generic [ref=e30] [cursor=pointer]: Settings
        - button [ref=e32] [cursor=pointer]:
          - img
      - main [ref=e33]:
        - generic [ref=e34]:
          - generic [ref=e35]:
            - generic [ref=e36]:
              - img [ref=e37]
              - heading "Settings" [level=1] [ref=e39]
              - generic [ref=e40]: /
              - generic [ref=e41]: System
              - generic [ref=e42]: /
              - generic [ref=e43]: Appearance
            - generic [ref=e44]: The Collective v0.1.0
          - generic [ref=e46]:
            - button "System" [ref=e47] [cursor=pointer]:
              - generic [ref=e48] [cursor=pointer]:
                - img [ref=e49] [cursor=pointer]
                - generic [ref=e51] [cursor=pointer]: System
            - button "Addons" [ref=e52] [cursor=pointer]:
              - generic [ref=e53] [cursor=pointer]:
                - img [ref=e54] [cursor=pointer]
                - generic [ref=e56] [cursor=pointer]: Addons
            - button "Vault" [ref=e57] [cursor=pointer]:
              - generic [ref=e58] [cursor=pointer]:
                - img [ref=e59] [cursor=pointer]
                - generic [ref=e61] [cursor=pointer]: Vault
          - generic [ref=e62]:
            - complementary [ref=e63]:
              - generic [ref=e65]:
                - img [ref=e66]
                - searchbox "Search settings..." [ref=e69]
              - generic [ref=e70]: System Settings
              - navigation [ref=e71]:
                - button "General" [ref=e73] [cursor=pointer]:
                  - generic [ref=e74] [cursor=pointer]:
                    - img
                    - generic [ref=e75] [cursor=pointer]: General
                  - img
                - button "Server/Model" [ref=e77] [cursor=pointer]:
                  - generic [ref=e78] [cursor=pointer]:
                    - img
                    - generic [ref=e79] [cursor=pointer]: Server/Model
                  - img
                - button "Logic" [ref=e81] [cursor=pointer]:
                  - generic [ref=e82] [cursor=pointer]:
                    - img
                    - generic [ref=e83] [cursor=pointer]: Logic
                  - img
                - button "Extensions" [ref=e85] [cursor=pointer]:
                  - generic [ref=e86] [cursor=pointer]:
                    - img
                    - generic [ref=e87] [cursor=pointer]: Extensions
                - button "Network" [ref=e89] [cursor=pointer]:
                  - generic [ref=e90] [cursor=pointer]:
                    - img
                    - generic [ref=e91] [cursor=pointer]: Network
                  - img
                - button "Devices" [ref=e93] [cursor=pointer]:
                  - generic [ref=e94] [cursor=pointer]:
                    - img
                    - generic [ref=e95] [cursor=pointer]: Devices
                  - img
                - button "Notification Preferences" [ref=e97] [cursor=pointer]:
                  - generic [ref=e98] [cursor=pointer]:
                    - img
                    - generic [ref=e99] [cursor=pointer]: Notification Preferences
                - button "Appearance" [ref=e101] [cursor=pointer]:
                  - generic [ref=e102] [cursor=pointer]:
                    - img
                    - generic [ref=e103] [cursor=pointer]: Appearance
                - button "Data Controls" [ref=e105] [cursor=pointer]:
                  - generic [ref=e106] [cursor=pointer]:
                    - img
                    - generic [ref=e107] [cursor=pointer]: Data Controls
                - button "Storage" [ref=e109] [cursor=pointer]:
                  - generic [ref=e110] [cursor=pointer]:
                    - img
                    - generic [ref=e111] [cursor=pointer]: Storage
                - button "System Log" [ref=e113] [cursor=pointer]:
                  - generic [ref=e114] [cursor=pointer]:
                    - img
                    - generic [ref=e115] [cursor=pointer]: System Log
            - main [ref=e116]:
              - generic [ref=e119]:
                - generic [ref=e121]:
                  - heading "Appearance" [level=2] [ref=e122]
                  - paragraph [ref=e123]: Customize the look and feel of the application
                - generic [ref=e124]:
                  - generic [ref=e125]:
                    - button "Theme" [ref=e126] [cursor=pointer]
                    - button "Layout" [ref=e127] [cursor=pointer]
                    - button "Advanced" [ref=e128] [cursor=pointer]
                  - generic [ref=e131]:
                    - generic [ref=e133]:
                      - button "Colors" [ref=e134] [cursor=pointer]
                      - button "Backgrounds" [ref=e135] [cursor=pointer]
                      - button "Typography" [ref=e136] [cursor=pointer]
                      - button "Appearance" [ref=e137] [cursor=pointer]
                    - generic [ref=e140]:
                      - generic [ref=e141]:
                        - heading "Color Scheme" [level=3] [ref=e142]
                        - paragraph [ref=e143]: Choose a color scheme that matches your style
                      - generic [ref=e144]:
                        - generic [ref=e145]: Select, create, or edit your color schemes.
                        - generic [ref=e146]:
                          - button "New Scheme" [ref=e147] [cursor=pointer]
                          - button "Edit Current" [ref=e148] [cursor=pointer]
                      - generic [ref=e149]:
                        - generic [ref=e163] [cursor=pointer]:
                          - generic [ref=e164] [cursor=pointer]: Void Circuit
                          - generic [ref=e165] [cursor=pointer]: Active
                        - generic [ref=e186] [cursor=pointer]: Midnight
                        - generic [ref=e207] [cursor=pointer]: Cyberpunk
                        - generic [ref=e228] [cursor=pointer]: Forest
                        - generic [ref=e249] [cursor=pointer]: Ocean
    - generic [ref=e256]:
      - generic [ref=e260]: Error
      - generic [ref=e261]:
        - generic [ref=e262]: "CPU: N/A"
        - generic [ref=e263]: "RAM: N/A"
        - button [ref=e264] [cursor=pointer]:
          - img
        - link [ref=e265] [cursor=pointer]:
          - /url: /settings
          - button [ref=e266] [cursor=pointer]:
            - img
  - button "10" [ref=e268] [cursor=pointer]:
    - img [ref=e269] [cursor=pointer]
    - generic [ref=e271] [cursor=pointer]: "10"
```