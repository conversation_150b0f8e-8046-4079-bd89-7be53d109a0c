// Mock data for chat preview
export const mockConversations = [
  {
    id: 'conv_1',
    title: 'Code Review Discussion',
    created_at: '2024-01-15T10:00:00Z',
    updated_at: '2024-01-15T10:30:00Z',
    model: 'gpt-4',
    settings: {
      temperature: 0.7,
      maxTokens: 2048
    }
  },
  {
    id: 'conv_2', 
    title: 'Creative Writing Help',
    created_at: '2024-01-14T15:20:00Z',
    updated_at: '2024-01-14T16:45:00Z',
    model: 'claude-3',
    settings: {
      temperature: 0.9,
      maxTokens: 4096
    }
  },
  {
    id: 'conv_3',
    title: 'Technical Documentation',
    created_at: '2024-01-13T09:15:00Z', 
    updated_at: '2024-01-13T11:30:00Z',
    model: 'gpt-4',
    settings: {
      temperature: 0.3,
      maxTokens: 2048
    }
  }
];

export const mockMessages = [
  {
    id: 'msg_1',
    content: 'Hello! I need help reviewing this React component. Can you take a look?',
    sender: 'user',
    timestamp: '2024-01-15T10:00:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1'
  },
  {
    id: 'msg_2',
    content: `I'd be happy to help you review your React component! Please share the code you'd like me to look at.

Here are some things I typically look for in React components:

- **Component structure** and organization
- **Props validation** and TypeScript usage
- **Performance optimizations** (useMemo, useCallback, etc.)
- **Accessibility** considerations
- **Code readability** and maintainability

Please paste your component code, and I'll provide detailed feedback!`,
    sender: 'assistant',
    timestamp: '2024-01-15T10:01:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1'
  },
  {
    id: 'msg_3',
    content: `Here's the component I'm working on:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetch(\`/api/users/\${userId}\`)
      .then(res => res.json())
      .then(data => {
        setUser(data);
        setLoading(false);
      });
  }, [userId]);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="user-profile">
      <img src={user.avatar} alt="Avatar" />
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};

export default UserProfile;
\`\`\`

What do you think? Any improvements you'd suggest?`,
    sender: 'user',
    timestamp: '2024-01-15T10:05:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1'
  },
  {
    id: 'msg_4',
    content: `Great component! I can see the basic structure is solid. Here are some improvements I'd suggest:

## 🔧 **Error Handling & Loading States**

\`\`\`jsx
import React, { useState, useEffect } from 'react';

const UserProfile = ({ userId }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setLoading(true);
        setError(null);
        const response = await fetch(\`/api/users/\${userId}\`);
        
        if (!response.ok) {
          throw new Error(\`Failed to fetch user: \${response.status}\`);
        }
        
        const data = await response.json();
        setUser(data);
      } catch (err) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    if (userId) {
      fetchUser();
    }
  }, [userId]);

  if (loading) {
    return (
      <div className="user-profile-loading" role="status" aria-label="Loading user profile">
        <div className="spinner" />
        <span>Loading user profile...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="user-profile-error" role="alert">
        <h3>Error loading profile</h3>
        <p>{error}</p>
        <button onClick={() => window.location.reload()}>
          Try Again
        </button>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="user-profile-empty">
        <p>User not found</p>
      </div>
    );
  }

  return (
    <div className="user-profile">
      <img 
        src={user.avatar} 
        alt={\`\${user.name}'s profile picture\`}
        onError={(e) => {
          e.target.src = '/default-avatar.png';
        }}
      />
      <h2>{user.name}</h2>
      <p>{user.email}</p>
    </div>
  );
};

export default UserProfile;
\`\`\`

## ✨ **Key Improvements:**

1. **Async/await** for cleaner promise handling
2. **Error state management** with user-friendly messages  
3. **Response validation** to catch HTTP errors
4. **Accessibility** improvements with ARIA labels
5. **Image fallback** for broken avatar URLs
6. **Conditional fetching** only when userId exists
7. **Better loading UX** with descriptive text

## 🚀 **Optional Enhancements:**

- Add **PropTypes** or **TypeScript** for type safety
- Implement **retry logic** for failed requests
- Add **skeleton loading** instead of spinner
- Consider **React Query** for caching and background updates

Would you like me to elaborate on any of these suggestions?`,
    sender: 'assistant',
    timestamp: '2024-01-15T10:08:00Z',
    model_used: 'gpt-4',
    conversationId: 'conv_1',
    bookmarked: true
  }
];

export const mockModels = [
  'gpt-4',
  'gpt-3.5-turbo',
  'claude-3-opus',
  'claude-3-sonnet',
  'llama-2-70b',
  'mistral-7b'
];

export const mockSettings = {
  autoWelcome: true,
  enableStreaming: true,
  showModelInfo: true,
  autoScroll: true,
  typingIndicators: true,
  theme: 'system',
  fontSize: 'medium',
  temperature: 0.7,
  maxTokens: 2048,
  systemPrompt: 'You are a helpful AI assistant focused on providing clear, accurate, and useful responses.',
  autoSave: true,
  exportFormat: 'markdown',
  messageAnimations: true,
  soundEnabled: false,
  showTimestamps: true,
  compactMode: false
};