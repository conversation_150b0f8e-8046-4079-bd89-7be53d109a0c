import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from './ui/card';
import { Button } from './ui/button';
import ErrorPopup from './ErrorPopup';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faHistory } from '@fortawesome/free-solid-svg-icons';

// Simple ErrorBoundary component
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    console.error('RecentActivityTile Error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <ErrorPopup
          title="Recent Activity Error"
          message="An error occurred in the recent activity component. Please try reloading the page."
          error={this.state.error}
          onReload={() => window.location.reload()}
        />
      );
    }

    return this.props.children;
  }
}

const RecentActivityTile = React.memo(({ activities = [], onNavigate }) => {
  return (
    <ErrorBoundary>
      <Card className="animate-slide-in">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FontAwesomeIcon icon={faHistory} className="w-5 h-5 text-primary" />
            Recent Activity
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {activities.map((activity, index) => (
              <div
                key={activity.id || `activity-${index}`}
                className="flex items-start gap-3 p-3 rounded-lg hover:bg-muted/50 transition-colors cursor-pointer"
                onClick={() => typeof onNavigate === 'function' && onNavigate('/settings')}
              >
                <div className="mt-0.5">
                  <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                    <FontAwesomeIcon
                      icon={activity.icon}
                      className="w-4 h-4 text-primary"
                    />
                  </div>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-foreground truncate">
                    {activity.message}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    {activity.time}
                  </p>
                </div>
                <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                  <FontAwesomeIcon icon={faHistory} className="w-4 h-4" />
                </Button>
              </div>
            ))}

            {activities.length === 0 && (
              <div className="text-center py-8 text-muted-foreground">
                <FontAwesomeIcon icon={faHistory} className="w-8 h-8 mb-2 opacity-50" />
                <p className="text-sm">No recent activity</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </ErrorBoundary>
  );
});

RecentActivityTile.displayName = 'RecentActivityTile';

export default RecentActivityTile;
