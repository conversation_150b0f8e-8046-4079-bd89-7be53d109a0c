HTML script tag executed
settings:55 ES modules are supported
react-dom.development.js:29895 Download the React DevTools for a better development experience: https://reactjs.org/link/react-devtools
index.jsx:7 index.jsx loaded
index.jsx:77 React root created
index.jsx:86 App rendered
App.jsx:82 AppContent: Component rendered.
App.jsx:95 AppContent State: isLoading true isBackendReady false dynamicTabs 0 error null
App.jsx:187 renderContent: isLoading true isBackendReady false
App.jsx:189 AppContent: Rendering LoadingScreen. isLoading: true isBackendReady: false
App.jsx:103 AppContent: tabsRef useEffect triggered. dynamicTabs changed: []
App.jsx:109 AppContent: Global error and backend initialization useEffect triggered. isMounted: true
App.jsx:110 App.jsx useEffect triggered.
App.jsx:113 checkBackendReadyAndSetupListener: Starting...
deprecations.ts:9  ⚠️ React Router Future Flag Warning: React Router will begin wrapping state updates in `React.startTransition` in v7. You can use the `v7_startTransition` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_starttransition.
warnOnce @ deprecations.ts:9
logDeprecation @ deprecations.ts:14
logV6DeprecationWarnings @ deprecations.ts:26
(anonymous) @ index.tsx:816
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
deprecations.ts:9  ⚠️ React Router Future Flag Warning: Relative route resolution within Splat routes is changing in v7. You can use the `v7_relativeSplatPath` future flag to opt-in early. For more information, see https://reactrouter.com/v6/upgrading/future#v7_relativesplatpath.
warnOnce @ deprecations.ts:9
logDeprecation @ deprecations.ts:14
logV6DeprecationWarnings @ deprecations.ts:37
(anonymous) @ index.tsx:816
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
ThemeContext.jsx:144 🎨 ThemeContext: Initializing theme from settings: {isDarkMode: false, schemeId: 'voidCircuit', colorScheme: 'Void Circuit', background: 'Solid', fontFamily: 'sans-serif', …}
SettingsContext.jsx:126 🚀 SettingsContext: SaveManager initialized
SettingsContext.jsx:131 🚀 SettingsContext: Initializing with default settings
SettingsContext.jsx:142 📥 SettingsContext: Loading settings via SaveManager
SaveManager.js:117 📥 SaveManager: Loading settings
SaveManager.js:117 📥 SaveManager: Loading ui_state
ThemeContext.jsx:242 🎨 Theme Applied: {scheme: 'Void Circuit', colors: {…}, isDark: false, cssVars: {…}}
App.jsx:117 AppContent: Backend was already ready. Setting states.
App.jsx:82 AppContent: Component rendered.
App.jsx:95 AppContent State: isLoading false isBackendReady true dynamicTabs 0 error null
App.jsx:187 renderContent: isLoading false isBackendReady true
Settings.jsx:764 renderContent called with activeSidebarItem: General
react-dom.development.js:86  Warning: React does not recognize the `activeTab` prop on a DOM element. If you intentionally want it to appear in the DOM as a custom attribute, spell it as lowercase `activetab` instead. If you accidentally passed it from a parent component, remove it from the DOM element.
    at div
    at TabsList (http://localhost:3000/src/components/ui/tabs.jsx:42:21)
    at div
    at Tabs (http://localhost:3000/src/components/ui/tabs.jsx:19:17)
    at div
    at http://localhost:3000/src/pages/Settings.jsx?t=1758037470902:74:115
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4088:5)
    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4558:5)
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at main
    at div
    at div
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at MainLayout (http://localhost:3000/src/components/MainLayout.jsx:27:30)
    at div
    at AppContent (http://localhost:3000/src/App.jsx?t=1758037470902:129:66)
    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4501:15)
    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:5247:5)
    at div
    at ThemeProvider (http://localhost:3000/src/contexts/ThemeContext.jsx?t=1758037470902:90:33)
    at SettingsProvider (http://localhost:3000/src/contexts/SettingsContext.jsx?t=1758037470902:125:36)
    at UIStateProvider (http://localhost:3000/src/contexts/UIStateContext.jsx?t=1758037295703:43:35)
    at NotificationProvider (http://localhost:3000/src/contexts/NotificationContext.jsx:29:40)
    at PortalProvider (http://localhost:3000/src/contexts/PortalContext.jsx:33:34)
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at DOMErrorBoundary (http://localhost:3000/src/components/DOMErrorBoundary.jsx:7:5)
    at App
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateProperty$1 @ react-dom.development.js:3757
warnUnknownProperties @ react-dom.development.js:3803
validateProperties$2 @ react-dom.development.js:3827
validatePropertiesInDevelopment @ react-dom.development.js:9541
setInitialProperties @ react-dom.development.js:9830
finalizeInitialChildren @ react-dom.development.js:10950
completeWork @ react-dom.development.js:22232
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
react-dom.development.js:86  Warning: Unknown event handler property `onValueChange`. It will be ignored.
    at div
    at TabsList (http://localhost:3000/src/components/ui/tabs.jsx:42:21)
    at div
    at Tabs (http://localhost:3000/src/components/ui/tabs.jsx:19:17)
    at div
    at http://localhost:3000/src/pages/Settings.jsx?t=1758037470902:74:115
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at RenderedRoute (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4088:5)
    at Routes (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4558:5)
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at main
    at div
    at div
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at MainLayout (http://localhost:3000/src/components/MainLayout.jsx:27:30)
    at div
    at AppContent (http://localhost:3000/src/App.jsx?t=1758037470902:129:66)
    at Router (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:4501:15)
    at BrowserRouter (http://localhost:3000/node_modules/.vite/deps/react-router-dom.js?v=b9a5fbd3:5247:5)
    at div
    at ThemeProvider (http://localhost:3000/src/contexts/ThemeContext.jsx?t=1758037470902:90:33)
    at SettingsProvider (http://localhost:3000/src/contexts/SettingsContext.jsx?t=1758037470902:125:36)
    at UIStateProvider (http://localhost:3000/src/contexts/UIStateContext.jsx?t=1758037295703:43:35)
    at NotificationProvider (http://localhost:3000/src/contexts/NotificationContext.jsx:29:40)
    at PortalProvider (http://localhost:3000/src/contexts/PortalContext.jsx:33:34)
    at ErrorBoundary (http://localhost:3000/src/components/ErrorBoundary.jsx:8:5)
    at DOMErrorBoundary (http://localhost:3000/src/components/DOMErrorBoundary.jsx:7:5)
    at App
printWarning @ react-dom.development.js:86
error @ react-dom.development.js:60
validateProperty$1 @ react-dom.development.js:3692
warnUnknownProperties @ react-dom.development.js:3803
validateProperties$2 @ react-dom.development.js:3827
validatePropertiesInDevelopment @ react-dom.development.js:9541
setInitialProperties @ react-dom.development.js:9830
finalizeInitialChildren @ react-dom.development.js:10950
completeWork @ react-dom.development.js:22232
completeUnitOfWork @ react-dom.development.js:26635
performUnitOfWork @ react-dom.development.js:26607
workLoopSync @ react-dom.development.js:26505
renderRootSync @ react-dom.development.js:26473
performConcurrentWorkOnRoot @ react-dom.development.js:25777
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
SaveManager.js:125 ✅ Backend load successful for settings: {config_version: 2, dark_mode: false, layout: 'default', theme: 'light', plugin_states: {…}, …}
SaveManager.js:175 🔄 Data merged with defaults
SettingsContext.jsx:147 ✅ SettingsContext: Settings loaded successfully {
  "dark_mode": false,
  "theme": "light",
  "appearance": {
    "theme": "light",
    "dark_mode": false,
    "color_scheme": "voidCircuit",
    "background": "solid",
    "font_family": "sans-serif",
    "font_size": "16px",
    "line_height": 1.5,
    "ui_scale": 1,
    "animations": true,
    "reduced_motion": false,
    "high_contrast": false,
    "color_blind_mode": "none",
    "custom_css": "",
    "color_schemes": [
      {
        "id": "voidCircuit",
        "name": "Void Circuit",
        "colors": {
          "primary": "#8E24AA",
          "background": "#0F1115",
          "surface": "#1C1F26",
          "accent": "#FF7043",
          "text": "#CFD8DC"
        }
      }
    ],
    "backgrounds": []
  },
  "layout": "default",
  "plugin_states": {},
  "system_log_path": "E:\\TheCollective\\Storage\\System\\logs",
  "indexed_directory": "E:\\TheCollective\\Storage",
  "plugins_path": "E:\\TheCollective\\Storage\\Addons\\Plugins",
  "mcps_path": "E:\\TheCollective\\Storage\\Addons\\MCP",
  "apis_path": "E:\\TheCollective\\Storage\\Addons\\API",
  "models_path": "E:\\TheCollective\\Storage\\System\\Models",
  "servers_path": "E:\\TheCollective\\Storage\\System\\Servers",
  "system_prompts_path": "E:\\TheCollective\\Storage\\System\\Logic",
  "date_format": "YYYY-MM-DD",
  "time_format": "24h",
  "timezone": "UTC",
  "auto_save": true,
  "startup_tab": "chat",
  "window_maximized": false,
  "notifications_enabled": true,
  "browser_homepage": "https://www.google.com",
  "browser_zoom_level": 100,
  "browser_enable_javascript": true,
  "browser_enable_images": true,
  "browser_enable_cookies": true,
  "browser_block_popups": true,
  "server_url": "http://127.0.0.1:11435",
  "server_host": "127.0.0.1",
  "server_port": 11435,
  "network_protocol": "http",
  "proxy_mode": "none",
  "proxy_host": "",
  "proxy_port": 0,
  "connection_timeout": 30,
  "max_retries": 3,
  "enable_ssl": false,
  "verify_certificates": true,
  "default_model": "",
  "auto_start_server": true,
  "ios_projection_enabled": false,
  "ios_device_name": "The Collective",
  "ios_projection_quality": "auto",
  "android_projection_enabled": false,
  "android_device_name": "The Collective",
  "android_projection_quality": "auto",
  "miracast_enabled": false,
  "dlna_enabled": false,
  "projection_local_only": true,
  "projection_port": 8080,
  "projection_protocol": "auto",
  "config_version": 2,
  "logic_hub_path": "E:\\TheCollective\\Storage\\System\\Logic\\Hub",
  "logic_system_prompts_path": "E:\\TheCollective\\Storage\\System\\Logic\\SystemPrompts",
  "logic_modals_path": "E:\\TheCollective\\Storage\\System\\Logic\\Modals",
  "logic_agents_path": "E:\\TheCollective\\Storage\\System\\Logic\\Agents",
  "extensions_path": "E:\\TheCollective\\Storage\\System\\Extensions",
  "server_profiles": {
    "llama.cpp_server": {
      "name": "llama.cpp_server",
      "enabled": true,
      "folder_path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server",
      "detected_executables": [
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\llama-server.exe",
          "file_name": "llama-server.exe",
          "executable_type": "llamacpp",
          "priority": 0
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-base.dll",
          "file_name": "ggml-base.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-alderlake.dll",
          "file_name": "ggml-cpu-alderlake.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-haswell.dll",
          "file_name": "ggml-cpu-haswell.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-icelake.dll",
          "file_name": "ggml-cpu-icelake.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-sandybridge.dll",
          "file_name": "ggml-cpu-sandybridge.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-sapphirerapids.dll",
          "file_name": "ggml-cpu-sapphirerapids.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-skylakex.dll",
          "file_name": "ggml-cpu-skylakex.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-sse42.dll",
          "file_name": "ggml-cpu-sse42.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-cpu-x64.dll",
          "file_name": "ggml-cpu-x64.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml-rpc.dll",
          "file_name": "ggml-rpc.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\ggml.dll",
          "file_name": "ggml.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\libcurl-x64.dll",
          "file_name": "libcurl-x64.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\libomp140.x86_64.dll",
          "file_name": "libomp140.x86_64.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\llama.dll",
          "file_name": "llama.dll",
          "executable_type": "library",
          "priority": 10
        },
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\mtmd.dll",
          "file_name": "mtmd.dll",
          "executable_type": "library",
          "priority": 10
        }
      ],
      "primary_executable": {
        "path": "E:\\TheCollective\\Storage\\System\\Servers\\llama.cpp_server\\llama-server.exe",
        "file_name": "llama-server.exe",
        "executable_type": "llamacpp",
        "priority": 0
      },
      "server_type": "llamacpp",
      "last_verified": 1757892249,
      "verification_status": "verified",
      "error_message": null,
      "metadata": {
        "primary_type": "llamacpp",
        "executable_count": "16"
      }
    },
    "ollama": {
      "name": "ollama",
      "enabled": false,
      "folder_path": "E:\\TheCollective\\Storage\\System\\Servers\\ollama",
      "detected_executables": [
        {
          "path": "E:\\TheCollective\\Storage\\System\\Servers\\ollama\\ollama.exe",
          "file_name": "ollama.exe",
          "executable_type": "exe",
          "priority": 1
        }
      ],
      "primary_executable": {
        "path": "E:\\TheCollective\\Storage\\System\\Servers\\ollama\\ollama.exe",
        "file_name": "ollama.exe",
        "executable_type": "exe",
        "priority": 1
      },
      "server_type": "ollama",
      "last_verified": 1757892249,
      "verification_status": "verified",
      "error_message": null,
      "metadata": {
        "primary_type": "exe",
        "executable_count": "1"
      }
    }
  },
  "model_profiles": {
    "manifests": {
      "name": "manifests",
      "enabled": false
    },
    "blobs": {
      "name": "blobs",
      "enabled": true
    },
    "qwen3:0.6b": {
      "name": "qwen3:0.6b",
      "enabled": true
    }
  },
  "active_server_profile": "llama.cpp_server",
  "active_model_profile": ""
}
SaveManager.js:125 ✅ Backend load successful for ui_state: {config_version: 2, dark_mode: false, layout: 'default', theme: 'light', plugin_states: {…}, …}
SaveManager.js:175 🔄 Data merged with defaults
UIStateContext.jsx:38 ✅ UIState: Loaded UI state {active_tab: 'chat', sidebar_collapsed: false, window_size: {…}, last_visited_settings_section: 'appearance', theme_panel_open: false, …}
Settings.jsx:72 Settings page: userSettings updated: {dark_mode: true, theme: 'light', appearance: {…}, layout: 'default', plugin_states: {…}, …}
Settings.jsx:190 Settings page: Fetching server status...
Settings.jsx:195 === FRONTEND: Fetching server status ===
Settings.jsx:365 🚀 Settings: Initializing all data on mount
Settings.jsx:378  ❌ Settings: Failed to initialize data: ReferenceError: fetchOllamaStatus is not defined
    at initializeAllData (Settings.jsx:369:23)
    at Settings.jsx:382:5
    at commitHookEffectListMount (react-dom.development.js:23189:26)
    at commitPassiveMountOnFiber (react-dom.development.js:24965:13)
    at commitPassiveMountEffects_complete (react-dom.development.js:24930:9)
    at commitPassiveMountEffects_begin (react-dom.development.js:24917:7)
    at commitPassiveMountEffects (react-dom.development.js:24905:3)
    at flushPassiveEffectsImpl (react-dom.development.js:27078:3)
    at flushPassiveEffects (react-dom.development.js:27023:14)
    at react-dom.development.js:26808:9
initializeAllData @ Settings.jsx:378
(anonymous) @ Settings.jsx:382
commitHookEffectListMount @ react-dom.development.js:23189
commitPassiveMountOnFiber @ react-dom.development.js:24965
commitPassiveMountEffects_complete @ react-dom.development.js:24930
commitPassiveMountEffects_begin @ react-dom.development.js:24917
commitPassiveMountEffects @ react-dom.development.js:24905
flushPassiveEffectsImpl @ react-dom.development.js:27078
flushPassiveEffects @ react-dom.development.js:27023
(anonymous) @ react-dom.development.js:26808
workLoop @ scheduler.development.js:266
flushWork @ scheduler.development.js:239
performWorkUntilDeadline @ scheduler.development.js:533
App.jsx:82 AppContent: Component rendered.
App.jsx:95 AppContent State: isLoading false isBackendReady true dynamicTabs 0 error null
App.jsx:187 renderContent: isLoading false isBackendReady true
Settings.jsx:764 renderContent called with activeSidebarItem: General
Settings.jsx:72 Settings page: userSettings updated: {dark_mode: false, theme: 'light', appearance: {…}, layout: 'default', plugin_states: {…}, …}
ThemeContext.jsx:144 🎨 ThemeContext: Initializing theme from settings: {isDarkMode: false, schemeId: 'voidCircuit', colorScheme: 'Void Circuit', background: 'Solid', fontFamily: 'sans-serif', …}
ThemeContext.jsx:242 🎨 Theme Applied: {scheme: 'Void Circuit', colors: {…}, isDark: false, cssVars: {…}}
Settings.jsx:198 Raw status response: {status: 'stopped'}
Settings.jsx:200 Server status set to: stopped
Settings.jsx:764 renderContent called with activeSidebarItem: General
